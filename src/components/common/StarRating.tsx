import React from 'react';
import {View, StyleSheet, Image} from 'react-native';
import MaterialIcon from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {DashboardIcons} from '../../utils/constants/AppStrings';
import {Colors} from '../../utils/constants/Theme';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: number;
  showEmptyStars?: boolean;
  useImages?: boolean;
  color?: string;
  emptyColor?: string;
}

/**
 * StarRating Component
 * Displays star ratings similar to Ionic's rating component
 * Supports both icon-based and image-based stars
 */
const StarRating: React.FC<StarRatingProps> = ({
  rating = 0,
  maxRating = 5,
  size = 16,
  showEmptyStars = true,
  useImages = false,
  color = '#8f0a19',
  emptyColor = '#e7b7b7ff',
}) => {
  // Ensure rating is within valid range
  const normalizedRating = Math.max(0, Math.min(rating, maxRating));
  const fullStars = Math.floor(normalizedRating);
  const hasHalfStar = normalizedRating % 1 >= 0.5;

  if (useImages) {
    // Use image-based stars (like in MechanicCard)
    return (
      <View style={styles.container}>
        {Array.from({length: maxRating}).map((_, index) => {
          const isFilled = index < fullStars || (index === fullStars && hasHalfStar);
          return (
            <Image
              key={index}
              source={
                isFilled
                  ? DashboardIcons.MCX_STAR_ICON
                  : DashboardIcons.MCX_LIGHT_STAR_ICON
              }
              style={[styles.starIcon, {width: size, height: size}]}
            />
          );
        })}
      </View>
    );
  }

  // Use icon-based stars
  return (
    <View style={styles.container}>
      {Array.from({length: maxRating}).map((_, index) => {
        if (index < fullStars) {
          // Full star
          return (
            <MaterialIcon
              key={index}
              name="star"
              size={size}
              color={color}
              style={styles.starIcon}
            />
          );
        } else if (index === fullStars && hasHalfStar) {
          // Half star - using Ionicons for better support
          return (
            <Ionicons
              key={index}
              name="star-half"
              size={size}
              color={color}
              style={styles.starIcon}
            />
          );
        } else if (showEmptyStars) {
          // Empty star
          return (
            <MaterialIcon
              key={index}
              name="star-outline"
              size={size}
              color={emptyColor}
              style={styles.starIcon}
            />
          );
        }
        return null;
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    marginRight: 2,
  },
});

export default StarRating;

