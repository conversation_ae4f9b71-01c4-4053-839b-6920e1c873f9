import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {THEME} from '../../utils/constants/Theme';

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface CustomAlertProps {
  visible: boolean;
  title: string;
  message?: string;
  onDismiss?: () => void;
  onViewDetails?: () => void;
  dismissButtonText?: string;
  viewDetailsButtonText?: string;
  showDismissButton?: boolean;
  buttons?: AlertButton[];
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  title,
  message,
  onDismiss,
  onViewDetails,
  dismissButtonText = 'Dismiss',
  viewDetailsButtonText = 'View Details',
  showDismissButton = true,
  buttons,
}) => {
  const handleButtonPress = (button: AlertButton) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onDismiss) {
      onDismiss();
    }
  };

  // If buttons prop is provided, use flexible button layout
  if (buttons && buttons.length > 0) {
    const isSingleButton = buttons.length === 1;
    return (
      <Modal isVisible={visible} onBackdropPress={onDismiss} backdropOpacity={0.5}>
        <View style={styles.overlay}>
          <View style={styles.alertBox}>
            <Text style={styles.title}>{title}</Text>
            {message && <Text style={styles.message}>{message}</Text>}
            <View style={[styles.buttonContainer, isSingleButton && styles.singleButtonLayout]}>
              {buttons.map((button, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleButtonPress(button)}
                  style={[
                    isSingleButton ? styles.singleButton : styles.flexButton,
                    button.style === 'cancel' && styles.cancelButton,
                    button.style === 'destructive' && styles.destructiveButton,
                  ]}>
                  <Text
                    style={[
                      styles.buttonText,
                      button.style === 'cancel' && styles.cancelButtonText,
                    ]}>
                    {button.text}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    );
  }

  // Original layout for backward compatibility
  return (
    <Modal isVisible={visible} onBackdropPress={onDismiss} backdropOpacity={0.5}>
      <View style={styles.overlay}>
        <View style={styles.alertBox}>
          <Text style={styles.title}>{title}</Text>
          {message && <Text style={styles.message}>{message}</Text>}
          <View style={[styles.buttonContainer, !showDismissButton && styles.singleButtonContainer]}>
            {showDismissButton && (
              <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
                <Text style={styles.dismissText}>{dismissButtonText}</Text>
              </TouchableOpacity>
            )}
            {onViewDetails && (
              <TouchableOpacity onPress={onViewDetails} style={styles.viewDetailsButton}>
                <Text style={styles.viewDetailsText}>{viewDetailsButtonText}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  alertBox: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: THEME.COLORS.PRIMARY_RED,
  },
  message: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#000'
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  singleButtonContainer: {
    justifyContent: 'center',
  },
  dismissButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: THEME.COLORS.PRIMARY_RED,
  },
  viewDetailsButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: THEME.COLORS.PRIMARY_RED,
  },
  fullWidthButton: {
    flex: 1,
  },
  dismissText: {
    color: 'white',
  },
  viewDetailsText: {
    color: 'white',
  },
  flexButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: THEME.COLORS.PRIMARY_RED,
    marginHorizontal: 5,
    flex: 1,
  },
  singleButton: {
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 5,
    backgroundColor: THEME.COLORS.PRIMARY_RED,
    minWidth: 100,
    maxWidth: 150,
  },
  singleButtonLayout: {
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#ef4444',
  },
  destructiveButton: {
    backgroundColor: '#ef4444',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
  cancelButtonText: {
    color: 'white',
  },
});


export default CustomAlert;