import React from 'react';
import { View, Text, StyleSheet, Modal } from 'react-native';
import CustomButton from './CustomButton';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface UseAddressPopupPageProps {
  visible: boolean;
  heading: string;
  body: string;
  doAction: {
    callback: () => void;
  };
  onCancel?: () => void;
  confirmText?: string;
}

const UseAddressPopupPage: React.FC<UseAddressPopupPageProps> = ({
  visible,
  heading,
  body,
  doAction,
  onCancel,
  confirmText = 'Use Address',
}) => {
  const handleCancel = () => {
    console.log('UseAddressPopupPage: Cancel pressed');
    onCancel?.();
  };

  const handleConfirm = () => {
    console.log('UseAddressPopupPage: Confirm pressed');
    doAction.callback();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.heading}>{heading}</Text>
          <Text style={styles.body}>{body}</Text>

          <View style={styles.buttonContainer}>
            <CustomButton
              text="No"
              onPress={handleCancel}
              variant="secondary"
              size="medium"
              fullWidth={false}
              style={styles.cancelButton}
            />
            <CustomButton
              text={confirmText}
              onPress={handleConfirm}
              variant="primary"
              size="medium"
              fullWidth={false}
              style={styles.confirmButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 24,
    paddingHorizontal: 20,
    width: '85%',
    maxWidth: 420,
    alignSelf: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  heading: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.SECONDARY,
    textAlign: 'center',
    marginBottom: 16,
  },
  body: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
    maxHeight: 160,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  confirmButton: {
    flex: 1,
    marginLeft: 8,
  },
});

export default UseAddressPopupPage;
