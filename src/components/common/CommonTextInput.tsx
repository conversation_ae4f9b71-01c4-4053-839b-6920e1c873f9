import React from 'react';
import { TextInput, StyleSheet } from 'react-native';
import { Colors, CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';

interface CommonTextInputProps {
    value: string;
    onChangeText: (text: string) => void;
    placeholder: string;
    style?: object;
    disabled?: boolean;
    multiline?: boolean;
    numberOfLines?: number;
    maxLength?: number;
    keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad';
    backgroundColor?: string;
    placeholderTextColor?: string;
    fontSize?: number;
    secureTextEntry?: boolean;
    onFocus?: () => void;
    onEndEditing?: () => void;
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
    autoCorrect?: boolean;
    autoComplete?: 'off' | 'username' | 'password' | 'email' | 'name' | 'tel' | 'street-address' | 'postal-code' | 'cc-number' | 'cc-exp' | 'cc-csc';
}

const CommonTextInput = ({
    value,
    onChangeText,
    placeholder,
    style,
    disabled = false,
    multiline = false,
    numberOfLines = 1,
    maxLength,
    keyboardType = 'default',
    backgroundColor = '#fff',
    placeholderTextColor = Colors.COMMON_GREY_SHADE_LIGHT,
    fontSize = Sizes.MEDIUM,
    secureTextEntry = false,
    onFocus,
    onEndEditing,
    autoCapitalize,
    autoCorrect,
    autoComplete,
}: CommonTextInputProps) => {
    return (
        <TextInput
            style={[
                styles.textInput,
                { backgroundColor, fontSize },
                multiline && styles.textInputMultiline,
                disabled && styles.textInputDisabled,
                style,
            ]}
            placeholder={placeholder}
            value={value}
            onChangeText={onChangeText}
            onFocus={onFocus}
            onEndEditing={onEndEditing}
            editable={!disabled}
            multiline={multiline}
            numberOfLines={numberOfLines}
            maxLength={maxLength}
            keyboardType={keyboardType}
            placeholderTextColor={placeholderTextColor}
            secureTextEntry={secureTextEntry}
            autoCapitalize={autoCapitalize}
            autoCorrect={autoCorrect}
            autoComplete={autoComplete}
        />
    );
};

const styles = StyleSheet.create({
    textInput: {
        borderWidth: 1,
        borderColor: Colors.COMMON_HEADING_COLOR_1,
        borderRadius: 2,
        padding: 8,
        marginLeft: CommonUIParams.CUSTOM_PADDING_16,
        marginRight: CommonUIParams.CUSTOM_PADDING_16,
        fontSize: Sizes.MEDIUM,
        marginTop: 4,
        fontFamily: Fonts.ROBO_REGULAR,
        color: Colors.COMMON_COMPONENT_TEXT_COLOR,
        fontWeight: '900',
    },
    textInputMultiline: {
        minHeight: 80,
        textAlignVertical: 'top',
    },
    textInputDisabled: {
        backgroundColor: Colors.COMMON_GREY_SHADE_LIGHT,
        opacity: 0.6,
    },
});

export default CommonTextInput;