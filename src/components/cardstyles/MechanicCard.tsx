import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import HorizontalDivider from '../common/HorizontalDivider';
import StarRating from '../common/StarRating';
import { AppCommonIcons, AppStrings, DashboardIcons } from '../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface MechanicCardProps {
  id: number;
  name: string;
  address: string;
  userRating: number;
  ratingOutOf: number;
  availability: string;
  isFavorite?: boolean;
  onFavoriteToggle?: (id: number | string) => void;
  onCardPress?: (id: number | string) => void;
  showFavoriteIcon?: boolean;
  availabilityColor?: string;
  cardStyle?: object;
  // Additional profile data
  experience?: string;
  gender?: string;
  email?: string;
  mobile?: string;
  imageUrl?: string;
  dateOfBirth?: string;
  maritalStatus?: string;
  country?: string;

}

const MechanicCard = ({
  id,
  name,
  address,
  userRating,
  ratingOutOf,
  availability,
  isFavorite = false,
  onFavoriteToggle,
  onCardPress,
  showFavoriteIcon = true,
  availabilityColor,
  cardStyle,
  experience,
  email,
  mobile,
  imageUrl,
}: MechanicCardProps) => {

  const handleCardPress = () => {
    if (onCardPress) {
      onCardPress(id);
    }
  };

  const handleFavoritePress = () => {
    if (onFavoriteToggle) {
      onFavoriteToggle(id);
    }
  };

  const getAvailabilityColor = () => {
    if (availabilityColor) {
      return availabilityColor;
    }
    return availability === 'Open' ? Colors.PRIMARY : Colors.PRIMARY;
  };

  return (
    <TouchableOpacity
      style={[styles.mechanicCard, cardStyle]}
      onPress={handleCardPress}
      activeOpacity={onCardPress ? 0.7 : 1}
    >
      {/* Top Row: Avatar, Name, Address */}
      <View style={styles.mechanicTopRow}>
        <Image
          source={imageUrl ? { uri: imageUrl } : AppCommonIcons.MCX_USER_PROFILE_PIC}
          style={styles.mechanicAvatar}
        />
        <View style={styles.mechanicDetails}>
          <View style={styles.nameRow}>
            <View style={styles.nameAndInfo}>
              <Text style={styles.mechanicName}>{name}</Text>
              {experience && <Text style={styles.experienceText}> • {experience}{AppStrings.MCX_EXPERIENCE_SUFFIX}</Text>}
            </View>
            {showFavoriteIcon && (
              <TouchableOpacity
                style={styles.heartContainer}
                onPress={handleFavoritePress}
              >
                <Icon
                  name={isFavorite ? 'favorite' : 'favorite-border'}
                  size={20}
                  color={isFavorite ? Colors.PRIMARY : Colors.COMMON_GREY_SHADE_LIGHT}
                />
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.mechanicAddress}>{address}</Text>
          {/* {(mobile || email) && (
            <View style={styles.contactInfo}>
              {mobile && <Text style={styles.contactText}>📱 {mobile}</Text>}
              {email && <Text style={styles.contactText}>✉️ {email}</Text>}
            </View>
          )} */}
        </View>
      </View>

      {/* Divider */}
      <HorizontalDivider />

      {/* Bottom Row: Stars and Availability */}
      <View style={styles.mechanicBottomRow}>
        <View style={styles.ratingRow}>
          <StarRating
            rating={userRating}
            maxRating={ratingOutOf}
            size={16}
            useImages={true}
            showEmptyStars={true}
          />
        </View>

        <View style={styles.availabilityContainer}>
          <Text style={styles.availabilityText}>
            <Text style={styles.availabilityLabel}>{AppStrings.MCX_AVAILABILITY_LABEL}</Text>
            <Text style={[
              styles.availabilityValue,
              { color: getAvailabilityColor() },
            ]}>
              {availability}
            </Text>
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  mechanicTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  mechanicAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#eee',
    marginRight: 12,
  },
  mechanicDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  nameAndInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: Sizes.LARGE,
    color: Colors.SECONDARY,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  experienceText: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_DARK,
    fontFamily: Fonts.ROBO_REGULAR,
    marginLeft: 4,
  },
  mechanicAddress: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_GREY_SHADE_DARK,
    marginTop: 2,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  contactInfo: {
    flexDirection: 'row',
    marginTop: 2,
  },
  contactText: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_DARK,
    fontFamily: Fonts.ROBO_REGULAR,
    marginRight: 8,
  },
  heartContainer: {
    marginLeft: 8,
    padding: 4,
    alignSelf: 'flex-start',
    marginTop: -2,
  },
  mechanicBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    width: 16,
    height: 16,
    marginRight: 2,
  },
  availabilityContainer: {
    alignItems: 'flex-end',
  },
  availabilityText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  availabilityLabel: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  availabilityValue: {
    fontSize: Sizes.MEDIUM,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_REGULAR,
  },

});

export default MechanicCard;