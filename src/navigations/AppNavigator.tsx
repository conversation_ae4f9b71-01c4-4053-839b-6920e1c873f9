import React, {RefObject} from 'react';
import {
  <PERSON><PERSON><PERSON>r,
  NavigationContainerRef,
  ParamListBase,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {RootStackParamList} from '../utils/configs/types';
import SplashScreen from '../screens/splash/SplashScreen';
import LoginMainScreen from '../screens/login/LoginMainScreen';
import AppLoginPageScreen from '../screens/login/AppLoginPageScreen';
import ResetPasswordScreen from '../screens/reset-password/ResetPasswordScreen';
import DrawerNavigator from './DrawerNavigator';
import ChatListScreen from '../screens/features/chat/chatList';
import ChatRoomScreen from '../screens/features/chat/ChatRoomScreen';

import Schedule from '../screens/features/schedule/Schedule';
import SettingsScreen from '../screens/features/settings/SettingsScreen';
import MessagesScreen from '../screens/features/notifications/messages/MessagesScreen';

import FindMechanics from '../screens/features/find_mechanics/FindMechanics';
import Help from '../screens/help/Help';
import ReferFriend from '../screens/features/referral/ReferFriend';
import AccountRegistration from '../screens/features/registration/AccountRegistration';
import {RouteNames} from '../utils/constants/AppStrings';
import EditPaymentPage from '../screens/features/users/EditPaymentPage';
import VehicleEditPage from '../screens/features/users/VehicleEditPage';
import MechanicProfilePage from '../screens/features/mechanic_profile/MechanicProfilePage';
import BookAppointmentsScreen from '../screens/features/appointments/BookAppointmentsScreen';
import AppointmentDetails from '../screens/features/appointments/AppointmentDetails';
import PrivacyPolicyScreen from '../screens/legal/PrivacyPolicyScreen';
import TermsConditionsScreen from '../screens/legal/TermsConditionsScreen';
import ConfirmAppointment from '../screens/features/appointments/ConfirmAppointment';
import MessageDetails from '../screens/features/notifications/messages/MessageDetails';
import CheckOutScreen from '../screens/features/notifications/messages/CheckOutScreen';
import AppointmentSession from '../screens/features/appointments/AppointmentSession';
import DisputeInvoice from '../screens/features/appointments/DisputeInvoice';
import HelpDetailsScreen from '../screens/help/HelpDetailsScreen';
import NetworkFailedPage from '../screens/common/NetworkFailedPage';
import { useAuth } from '../utils/configs/AuthContext';
import { AnalyticService } from '../utils/services/AnalyticService';
import CompletedSession from '../screens/features/appointments/CompletedSession';
import EditProfile from '../screens/features/users/EditProfile';

const Stack = createNativeStackNavigator<RootStackParamList>();
type AppNavigatorProps = {
  navigationRef?: RefObject<NavigationContainerRef<ParamListBase> | null>;
};

const AppNavigator: React.FC<AppNavigatorProps> = ({navigationRef}) => {
  const {user, loading} = useAuth();
  const wasLoggedInRef = React.useRef<boolean>(false);
  const previousUserRef = React.useRef<any>(null);
  React.useEffect(() => {
    if (user) {
      wasLoggedInRef.current = true;
      previousUserRef.current = user;
    } else if (previousUserRef.current && !user) {
      previousUserRef.current = null;
    }
  }, [user]);

  const onStateChange = (state: any) => {
    if (state) {
      const currentRoute = state.routes[state.index];
      const screenName = currentRoute.name;
      AnalyticService.AddAnalyticsScreenView(screenName, screenName);
    }
  };
  const shouldShowSplash = !user && !loading && !wasLoggedInRef.current;

  return (
    <NavigationContainer ref={navigationRef} onStateChange={onStateChange}>
      <Stack.Navigator
        screenOptions={{headerShown: false, animation: 'slide_from_right'}}
        initialRouteName={!user ? (shouldShowSplash ? RouteNames.MCX_NAV_SPLASH : RouteNames.MCX_NAV_LoginMainScreen) : RouteNames.MCX_NAV_DashBoard}>
        {!user ? (
          <>
            {shouldShowSplash && (
              <Stack.Screen
                name={RouteNames.MCX_NAV_SPLASH}
                component={SplashScreen}
              />
            )}
            <Stack.Screen
              name={RouteNames.MCX_NAV_LoginMainScreen}
              component={LoginMainScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}
              component={AccountRegistration}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_AppLoginPageScreen}
              component={AppLoginPageScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_ResetPasswordScreen}
              component={ResetPasswordScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_PRIVACY_POLICY}
              component={PrivacyPolicyScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_TERMS_CONDITIONS}
              component={TermsConditionsScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_CHAT_LIST}
              component={ChatListScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_NETWORK_FAILED}
              component={NetworkFailedPage}
            />
          </>
        ) : (
          <>
            <Stack.Screen
              name={RouteNames.MCX_NAV_DashBoard}
              component={DrawerNavigator}
              options={{
                headerShown: false,
                animation: 'none',
                animationTypeForReplace: 'push',
              }}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}
              component={AccountRegistration}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_MESSAGE_DETAILS}
              component={MessageDetails}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name="AppointmentSession"
              component={AppointmentSession}
              options={{ headerShown: false }}
            />
            {/* <Stack.Screen
              name="DisputeInvoice"
              component={DisputeInvoice}
              options={{ headerShown: false}}
            /> */}
            <Stack.Screen 
              name="CompletedSession" 
              component={CompletedSession}
              options={{ title: 'Completed Session' }}
              // options={{headerShown: false}}
            />
            <Stack.Screen
              name="DisputeInvoice"
              component={DisputeInvoice}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name="CheckOutScreen"
              component={CheckOutScreen}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_CHAT_ROOM}
              component={ChatRoomScreen}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name={RouteNames.MCX_FIND_MECHANIC}
              component={FindMechanics}
            />
            <Stack.Screen name={RouteNames.MCX_SCHEDULE} component={Schedule} />
            <Stack.Screen
              name={RouteNames.MCX_NAV_SETTINGS}
              component={SettingsScreen}
            />
            <Stack.Screen name={RouteNames.MCX_NAV_HELP} component={Help} />
            <Stack.Screen
              name={RouteNames.MCX_HELP_DETAILS}
              component={HelpDetailsScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_REFER_FRIEND}
              component={ReferFriend}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_EDIT_PAYMENT}
              component={EditPaymentPage}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_VEHICLE_EDIT}
              component={VehicleEditPage}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_MechanicProfilePage}
              component={MechanicProfilePage}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_EDIT_PROFILE}
              component={EditProfile}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_BookAppointmentScreen}
              component={BookAppointmentsScreen}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_AppointmentDetails}
              component={AppointmentDetails}
            />
            <Stack.Screen
              name="ConfirmAppointment"
              component={ConfirmAppointment}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name="NetworkFailedPage"
              component={NetworkFailedPage}
            />
            <Stack.Screen
              name={RouteNames.MCX_NAV_CHAT_LIST}
              component={ChatListScreen}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
