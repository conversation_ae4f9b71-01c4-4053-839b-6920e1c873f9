import {useEffect, useState, useRef} from 'react';
import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import {
  Platform,
  PermissionsAndroid,
  AppState,
  AppStateStatus,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GC_FCM_TOKEN } from '../utils/globals';

interface UseFcmTokenReturn {
  token: string | null;
  notificationPermissionStatus: 'granted' | 'denied' | 'loading';
  messagePayload: FirebaseMessagingTypes.RemoteMessage | null;
  refreshToken: () => Promise<void>;
}

//const FCM_TOKEN_KEY = GC_FCM_TOKEN;

const useFcmToken = (): UseFcmTokenReturn => {
  const [token, setToken] = useState<string | null>(null);
  const [notificationPermissionStatus, setNotificationPermissionStatus] =
    useState<'granted' | 'denied' | 'loading'>('loading');
  const [messagePayload, setMessagePayload] =
    useState<FirebaseMessagingTypes.RemoteMessage | null>(null);

  // Refs to hold unsubscribe functions so we can safely re-subscribe
  const unsubscribeMessageRef = useRef<(() => void) | null>(null);
  const unsubscribeTokenRefreshRef = useRef<(() => void) | null>(null);
  const hasInitializedRef = useRef(false);

  const requestPermission = async (): Promise<boolean> => {
    try {
      let permissionGranted = false;

      if (Platform.OS === 'android') {
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: 'Notification Permission',
              message:
                'This app needs access to send you notifications about your mechanic services.',
              buttonPositive: 'Allow',
              buttonNegative: 'Deny',
            },
          );
          permissionGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        } else {
          // For Android < 13, notifications are granted by default
          permissionGranted = true;
        }
      } else {
        const authStatus = await messaging().requestPermission();
        permissionGranted =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      }

      return permissionGranted;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  };

  const getFcmToken = async (): Promise<string | null> => {
    try {
      await messaging().registerDeviceForRemoteMessages();
      const fcmToken = await messaging().getToken();

      // Store token in AsyncStorage only
      await AsyncStorage.setItem( GC_FCM_TOKEN, fcmToken);

      return fcmToken;
    } catch (error) {
      console.error('Error retrieving FCM token:', error);
      return null;
    }
  };

  const attachListeners = () => {
    // Detach existing listeners to avoid duplicates
    if (unsubscribeMessageRef.current) {
      unsubscribeMessageRef.current();
      unsubscribeMessageRef.current = null;
    }
    if (unsubscribeTokenRefreshRef.current) {
      unsubscribeTokenRefreshRef.current();
      unsubscribeTokenRefreshRef.current = null;
    }

    // Listen for foreground messages
    unsubscribeMessageRef.current = messaging().onMessage(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('Chat notification received:', remoteMessage);

        setMessagePayload(remoteMessage);

        // Show alert for foreground notifications
        // if (remoteMessage.notification) {
        //   Alert.alert(
        //     remoteMessage.notification.title || 'Notification',
        //     remoteMessage.notification.body || 'You have a new message',
        //   );
        // }
      },
    );

    // Listen for token refresh
    unsubscribeTokenRefreshRef.current = messaging().onTokenRefresh(
      async newToken => {

        setToken(newToken);

        // Store refreshed token in AsyncStorage only
        await AsyncStorage.setItem( GC_FCM_TOKEN, newToken);
      },
    );

  };

  const detachListeners = () => {
    if (unsubscribeMessageRef.current) {
      unsubscribeMessageRef.current();
      unsubscribeMessageRef.current = null;

    }
    if (unsubscribeTokenRefreshRef.current) {
      unsubscribeTokenRefreshRef.current();
      unsubscribeTokenRefreshRef.current = null;

    }
  };

  const refreshToken = async (): Promise<void> => {
    const newToken = await getFcmToken();
    setToken(newToken);
  };

  // Load stored token when component mounts
  useEffect(() => {
    const loadStoredToken = async () => {
      const storedToken = await AsyncStorage.getItem( GC_FCM_TOKEN);
      if (storedToken && !token) {
        setToken(storedToken);
      }
    };

    loadStoredToken();
  }, [token]);

  useEffect(() => {
    let netinfoUnsubscribe: (() => void) | undefined;
    let appStateUnsubscribe: (() => void) | undefined;

    const initializeFCM = async () => {
      try {
        setNotificationPermissionStatus('loading');

        // Check if we already have a token stored
        const storedToken = await AsyncStorage.getItem( GC_FCM_TOKEN);
        if (storedToken) {
          setToken(storedToken);
          console.log('FCM Token retrieved from storage:', storedToken);
        }

        const permissionGranted = await requestPermission();

        if (permissionGranted) {
          setNotificationPermissionStatus('granted');

          // Get initial token
          const fcmToken = await getFcmToken();
          setToken(fcmToken);

          attachListeners();

          // Observe network changes to re-attach listeners on reconnect
          netinfoUnsubscribe = NetInfo.addEventListener(state => {
            if (state.isConnected && state.isInternetReachable !== false) {
              console.log(
                '🌐 Network connected - ensuring FCM listeners are active',
              );
              attachListeners();
              // Also verify token is still valid
              refreshToken();
            }
          });

          // Re-attach listeners when app returns to foreground
          const handleAppStateChange = (nextState: AppStateStatus) => {
            if (nextState === 'active') {
              console.log('🟢 App active - ensuring FCM listeners are active');
              attachListeners();
            }
          };
          appStateUnsubscribe = AppState.addEventListener(
            'change',
            handleAppStateChange,
          ).remove;
        } else {
          setNotificationPermissionStatus('denied');
          console.log('Notification permission denied');
        }
      } catch (error) {
        console.error('Error initializing FCM:', error);
        setNotificationPermissionStatus('denied');
      } finally {
        hasInitializedRef.current = true;
      }
    };

    initializeFCM();

    // Cleanup function
    return () => {
      detachListeners();
      if (netinfoUnsubscribe) netinfoUnsubscribe();
      if (appStateUnsubscribe) appStateUnsubscribe();
    };
  }, []);

  return {
    token,
    notificationPermissionStatus,
    messagePayload,
    refreshToken,
  };
};

export default useFcmToken;
