/**
 * Rating Utility Functions
 * 
 * Matches Ionic behavior for displaying mechanic ratings
 */

/**
 * Normalizes mechanic rating to match Ionic behavior
 * When rating is undefined or null, defaults to 0 (empty stars)
 * Rating of 0 shows empty stars (light pink), rating > 0 shows filled stars (dark red)
 * @param rating - The raw rating value from database
 * @returns Normalized rating (0 if undefined/null, otherwise the actual rating)
 */
export const normalizeMechanicRating = (rating: number | undefined | null): number => {
  if (rating === undefined || rating === null) {
    return 0;
  }
  // Return the actual rating value (0 means empty stars, >0 means filled stars)
  return rating;
};

