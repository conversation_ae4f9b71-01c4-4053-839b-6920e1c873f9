// ===== authErrorHandler.ts =====
// Place this in: utils/helpers/authErrorHandler.ts

import { Alert } from 'react-native';
import appleAuth from '@invertase/react-native-apple-authentication';

export type SocialProvider = 'Google' | 'Facebook' | 'Apple';

/**
 * ✅ Detects if error is a sign-in cancellation across all providers
 * Handles various error formats and messages
 */
export const isSignInCancelled = (error: any): boolean => {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toUpperCase() || '';
  const errorCodeLower = error.code?.toLowerCase() || '';

  // Common cancellation patterns
  const cancellationPatterns = [
    'cancelled',
    'canceled',
    'user cancelled',
    'user canceled',
    'sign-in was cancelled',
    'sign in was cancelled',
    'getTokens requires a user',
    'likely cancelled',
    'sign_in_cancelled',
    'cancelled by user',
  ];

  // Check error code
  if (
    errorCode === 'SIGN_IN_CANCELLED' ||
    errorCode === 'CANCELLED' ||
    errorCodeLower === 'sign_in_cancelled'
  ) {
    return true;
  }

  // Check Apple-specific error
  try {
    if (error.code === appleAuth.Error.CANCELED) {
      return true;
    }
  } catch (e) {
    // appleAuth might not be available on Android
  }

  // Check message for cancellation patterns
  for (const pattern of cancellationPatterns) {
    if (errorMessage.includes(pattern)) {
      return true;
    }
  }

  return false;
};

/**
 * ✅ Shows cancellation alert with provider-specific message
 */
export const showCancellationAlert = (
  provider: SocialProvider,
  onDismiss?: () => void
): void => {
  Alert.alert(
    'Sign-In Cancelled',
    `You cancelled the ${provider} sign-in process. Please try again if you want to access the app.`,
    [
      {
        text: 'OK',
        onPress: () => {
          console.log(`${provider} sign-in cancellation acknowledged`);
          onDismiss?.();
        },
        style: 'default',
      },
    ]
  );
};

/**
 * ✅ Handles social auth errors with automatic cancellation detection
 * Returns true if error was a cancellation, false if other error
 */
export const handleSocialAuthError = (
  error: any,
  provider: SocialProvider,
  onCancelled?: () => void,
  onError?: (message: string) => void
): boolean => {
  // Check for cancellation first
  if (isSignInCancelled(error)) {
    showCancellationAlert(provider, onCancelled);
    return true; // Was a cancellation
  }

  // Handle other errors
  let errorMessage = error.message || `An error occurred during ${provider} sign-in`;

  // Improve generic error messages
  if (errorMessage.includes('Network')) {
    errorMessage = 'Network error. Please check your internet connection.';
  } else if (errorMessage.includes('timeout')) {
    errorMessage = `${provider} sign-in timed out. Please try again.`;
  } else if (errorMessage.includes('invalid')) {
    errorMessage = `Invalid ${provider} credentials. Please try again.`;
  }

  Alert.alert(`${provider} Sign-In Error`, errorMessage, [
    {
      text: 'OK',
      onPress: () => {
        console.log(`${provider} error dismissed`);
        onError?.(errorMessage);
      },
    },
  ]);

  return false; // Was not a cancellation
};

/**
 * ✅ Checks if Apple Sign-In response indicates cancellation
 * Use this before processing identityToken
 */
export const isAppleSignInCancelled = (identityToken: any): boolean => {
  return !identityToken;
};

/**
 * ✅ Checks if Facebook login result indicates cancellation
 * Use this after LoginManager.logInWithPermissions()
 */
export const isFacebookLoginCancelled = (result: any): boolean => {
  return result?.isCancelled === true;
};

/**
 * ✅ Checks if Google Sign-In was cancelled
 * signIn() returns null/undefined on cancellation
 */
export const isGoogleSignInCancelled = (loggedUser: any): boolean => {
  return !loggedUser;
};