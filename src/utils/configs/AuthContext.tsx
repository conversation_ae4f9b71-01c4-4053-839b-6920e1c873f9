import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {
  getAuth,
  onAuthStateChanged,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
} from '@react-native-firebase/auth';
import {child, get, getDatabase } from '@react-native-firebase/database';
import {getAnalytics, logEvent} from '@react-native-firebase/analytics';
import {getApp} from '@react-native-firebase/app';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {UserInfo} from '../configs/types';
import {REGISTRATION_STATUS} from '../constants/RegistrationStatus';
import {GC_CUSTOMER_ID} from '../globals';

interface UserRegistrationData {
  email: string;
  firstName: string;
  lastName: string;
  mobile: string;
  providerType: string;
  providerTypeId: string;
  registrationStatus: string;
}

interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  loading: boolean;
  setUser: (user: FirebaseAuthTypes.User | null) => void;
  signUp: (email: string, password: string) => Promise<FirebaseAuthTypes.User>;
  signIn: (email: string, password: string) => Promise<FirebaseAuthTypes.User>;
  signOut: () => Promise<void>;
  isRegisteredAsMechanicUser: (userId: string) => Promise<boolean>;
  fetchUserInfo: (customerId: string) => Promise<UserInfo | null>;
  resetPassword: (email: string) => Promise<void>;
  registerUser: (
    email: string,
    password: string,
    userData: Omit<UserRegistrationData, 'email'>,
  ) => Promise<string>;
  updateUserRegistration: (
    customerId: string,
    firstname: string,
    lastname: string,
    email: string,
    phoneNumber: string,
    providerType: string,
    providerTypeId: string,
    registrationStatus: string,
  ) => Promise<void>;
  updateRegistrationStatus: (
    customerId: string,
    registrationStatus: string,
  ) => Promise<void>;
  isAvailableEmail: (email: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({children}: AuthProviderProps) => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const app = getApp();
    const auth = getAuth(app);
    const analytics = getAnalytics(app);

    const initializeAuth = async () => {
      try {
        // Check if this is a fresh install by looking for a file in the app's document directory
        const appInstallFlagPath = `${RNFS.DocumentDirectoryPath}/appInstalled.txt`;
        const fileExists = await RNFS.exists(appInstallFlagPath);

        console.log('App install flag exists:', fileExists);

        if (!fileExists) {
          console.log('Fresh install detected, clearing all storage and signing out...');

          // Clear all AsyncStorage
          await AsyncStorage.clear();

          // Sign out from Firebase Auth
          await auth.signOut();

          // Create the install flag file
          await RNFS.writeFile(appInstallFlagPath, 'installed', 'utf8');

          console.log('Cleared storage, signed out and created install flag');
        }
      } catch (error) {
        console.error('Error during auth initialization:', error);
      }
    };

    // Initialize auth first, then set up the listener
    initializeAuth().then(() => {
      const unsubscribe = onAuthStateChanged(
        auth,
        async (firebaseUser: FirebaseAuthTypes.User | null) => {
          if (firebaseUser) {
            // Check if user is registered as mechanic first (matches Ionic behavior)
            const isMechanic = await isRegisteredAsMechanicUser(firebaseUser.uid);
            if (isMechanic) {
              // Mechanic user detected - sign out and don't set user (matches Ionic)
              await auth.signOut();
              await AsyncStorage.removeItem(GC_CUSTOMER_ID);
              setUser(null);
              setLoading(false);
              return;
            }

            await AsyncStorage.setItem(GC_CUSTOMER_ID, firebaseUser.uid);
            const userInfo = await fetchUserInfo(firebaseUser.uid);
            // Only set user if registration is completed (matches Ionic behavior)
            if (userInfo && userInfo['registration-status'] === REGISTRATION_STATUS.COMPLETED) {
              setUser(firebaseUser);
              logEvent(analytics, 'user_login', {
                userId: firebaseUser.uid,
                method: firebaseUser.providerData[0]?.providerId || 'email',
              });
            } else {
              // User exists but registration not completed - don't set user
              // Navigation to registration will be handled by the login screens
              setUser(null);
            }
          } else {
            await AsyncStorage.removeItem(GC_CUSTOMER_ID);
            setUser(null);
          }
          setLoading(false);
        },
      );

      return unsubscribe;
    });
  }, []);

  const signUp = async (email: string, password: string) => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const analytics = getAnalytics(app);
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password,
      );
      await logEvent(analytics, 'user_signup', {
        method: 'email',
        userId: userCredential.user.uid,
      });
      return userCredential.user;
    } catch (error) {
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const auth = getAuth(getApp());
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password,
      );
      await AsyncStorage.setItem('isLoggedIn', 'true');
      return userCredential.user;
    } catch (error) {
      throw error;
    }
  };

  const isRegisteredAsMechanicUser = async (userId: string) => {
    const snapshot = await get(child(getDatabase().ref(), `mechanic/${userId}`));
    return snapshot.exists();
  };

  const fetchUserInfo = async (customerId: string) => {
    try {
      const dbRef = getDatabase().ref();
      const snapshot = await get(child(dbRef, `customer/${customerId}`));

      if (snapshot.exists()) {
        const userInfo = snapshot.val();
        console.log('User info fetched:', userInfo);
        return userInfo;
      } else {
        console.warn('No user data available for customerId:', customerId);
        return null;
      }
    } catch (e) {
      console.log('User info fetched: error => ', e);
      return null;
    }
  };

  const signOut = async () => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const analytics = getAnalytics(app);
      await auth.signOut();
      await AsyncStorage.removeItem('isLoggedIn');
      await logEvent(analytics, 'user_logout');
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const auth = getAuth(getApp());
      await auth.sendPasswordResetEmail(email);
    } catch (error) {
      throw error;
    }
  };

  const registerUser = async (
    email: string,
    password: string,
    userData: Omit<UserRegistrationData, 'email'>,
  ) => {
    try {
      const app = getApp();
      const auth = getAuth(app);
      const database = getDatabase(app);
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password,
      );
      const uid = userCredential.user.uid;
      await database.ref(`customer/${uid}`).set({
        email: email,
        'first-name': userData.firstName,
        'last-name': userData.lastName,
        mobile: userData.mobile,
        'provider-type': userData.providerType,
        'provider-type-id': userData.providerTypeId,
        'registration-status': userData.registrationStatus,
      });
      return uid;
    } catch (error) {
      throw error;
    }
  };

  const updateUserRegistration = async (
    customerId: string,
    firstname: string,
    lastname: string,
    email: string,
    phoneNumber: string,
    providerType: string,
    providerTypeId: string,
    registrationStatus: string,
  ) => {
    const database = getDatabase(getApp());
    return database.ref(`customer/${customerId}`).set({
      email: email,
      'first-name': firstname,
      'last-name': lastname,
      mobile: phoneNumber,
      'provider-type': providerType,
      'provider-type-id': providerTypeId,
      'registration-status': registrationStatus,
      loginParty: providerType === 'normal' ? 'normal' : providerType,
    });
  };

  const updateRegistrationStatus = async (
    customerId: string,
    registrationStatus: string,
  ) => {
    const database = getDatabase(getApp());
    return database
      .ref(`customer/${customerId}/registration-status`)
      .set(registrationStatus);
  };

  const isAvailableEmail = async (email: string): Promise<boolean> => {
    try {
      const database = getDatabase(getApp());
      // Query Firebase Realtime Database to check if email already exists
      const snapshot = await database.ref('customer')
        .orderByChild('email')
        .equalTo(email.toLowerCase())
        .limitToFirst(1)
        .once('value');
      
      return snapshot.exists();
    } catch (error) {
      console.error('Error checking email availability:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    setUser,
    signUp,
    signIn,
    isRegisteredAsMechanicUser,
    fetchUserInfo,
    signOut,
    resetPassword,
    registerUser,
    updateUserRegistration,
    updateRegistrationStatus,
    isAvailableEmail,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
