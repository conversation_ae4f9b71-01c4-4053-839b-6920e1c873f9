export interface DropdownOption {
  label: string;
  value: string;
}

export interface Question {
  id: string;
  question: string;
  type: 'dropdown' | 'radio' | 'text';
  options: string[];
}

export interface MechanicItem {
  id: string;
  indexKey :string;
  name?: string;
  'first-name'?: string;
  'last-name'?: string;
  email?: string;
  mobile?: string;
  gender?: string;
  dateofbirth?: string;
  maritalstatus?: string;
  address?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  zipcode?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  locationDetails?: string[];
  experience?: string;
  'mechanic-rating'?: number;
   rating?: number;
  availability?: string;
  'provider-type'?: string;
  'provider-type-id'?: string;
  'registration-status'?: string;
  'login-status'?: boolean;
  'image-verified'?: string;
  'image-verification-error'?: string;
  imageUrl?: string;
  'stripe-acc-key'?: string;
  bankaccount?: {
    status?: string;
    personalAddress?: any;
    payoutSchedule?: any;
    address?: any;
    external_account?: any;
    legalEntity?: any;
    error?: string;
    create?: any;
  };
  'payment-account'?: {
    'stripe-acc-key'?: string;
    'company-name'?: string;
    'acc-last-digit'?: string;
  };
  counts?: {
    'rightnow-count'?: number;
    'appointment-count'?: number;
    'cancelled-total'?: number;
  };
  cancellationRate?: string;
  byAvailability?: number;
  byAppointment?: number;
  services?: Record<string, any>;
  certificates?: Record<string, any>;
  certificatesCount?: number;
  grades?: Record<string, any>;
  identifications?: Record<string, any>;
  appointments?: Record<string, any>;
  sessions?: Record<string, any>;
  'work-requests'?: Record<string, any>;
  'my-chats'?: Record<string, any>;
  favourite?: boolean;
  userRating?: number;
  ratingOutOf?: number;
  serviceTypes?: string[];
  'invite-code'?: string;
  updateLocation?: boolean;
  settings?: any;
  certifications?: number;
}

export type RootStackParamList = {
  Splash: undefined;
  LoginMainScreen: undefined;
  AccountRegistration: undefined;
  AppLoginPageScreen: undefined;
  ResetPasswordScreen: undefined;
  DashBoard: undefined;
  MessagesScreen: undefined;
  MessageDetails: {
    'message-array': any[];
    'position': number;
    timeline?: any[];
  };
  PendingScreen: undefined;
  findMechanic: undefined;
  schedule: undefined;
  SettingsScreen: undefined;
  Help: undefined;
  ReferFriend: undefined;
  EditProfile: undefined;
  EditPaymentPage: undefined;
  VehicleEditPage: {
    'vehicle-info'?: any;
    'vehicle-array'?: any[];
  };
  MechanicProfilePage: {
    mechanic: MechanicItem;
    mechanics?: MechanicItem[];
    currentIndex?: number;
    selectedAvailability?: string;
  };
  BookAppointmentScreen: {
    mechanic: MechanicItem;
    selectedAvailability?: string;
  };
  AppointmentDetails: {
    appointment?: any;
    'appointment-detail'?: any;
     initialTab?:number
  };
  ConfirmAppointment: {
    'appointment-detail': any;
    initialTab?: number;
    origin?: 'messages' | 'appointments';
    fromMessageDetails?: boolean;
  };
  AppointmentSession: {
    'session-details': any;
    mechanicDetail?: any;
  };
  DisputeInvoice: {
    'session-details': any;
    mechanicDetail?: any;
  };
  CompletedSession: {
    'appointment-detail': any;
  };
  CheckOutScreen: {
    mechanicDetail: any;
    sessionDetail: any;
    costDetail: any;
    workRequestId: string;
    onPayNow: () => void;
    onRedeemPoints?: () => void;
  };
  ChatListScreen: undefined;
  ChatRoomScreen: {
    mechanicId: string;
    customerId: string;
    mechanicImage?: string;
    mechanicName?: string;
    workRequestId: string;
    workRequestDetails: {
      day?: any;
      month?: any;
      service?: any;
      time?: string;
    };
    isMessage?: boolean;
  };
  PrivacyPolicy: undefined;
  TermsConditions: undefined;
  HelpDetails: {
    'help-key': string;
  };
  NetworkFailedPage: undefined;
};
import {RegistrationStatusType} from '../constants/RegistrationStatus';

export type UserInfo = {
  'first-name': string;
  'last-name': string;
  email: string;
  mobile: string;
  loginParty: string;
  'registration-status': RegistrationStatusType;
};
