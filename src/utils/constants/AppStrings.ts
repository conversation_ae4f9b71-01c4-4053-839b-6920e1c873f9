export const AppStrings = {
  //Login main screen strings
  MCX_TERMS_PREFIX: 'BY LOGGING INTO OR SIGNING UP, YOU AGREE TO OUR',
  MCX_PRIVACY_POLICY: 'PRIVACY POLICY',
  MCX_TERMS_OF_SERVICE: 'TERMS OF SERVICE',
  MCX_AND_KEYWORD: 'AND',
  MCX_SIGN_UP: 'Sign Up',
  MCX_LOGIN: 'Login',
  MCX_OR_WITH: 'OR WITH',
  //User login screen strings
  MCX_ENTER_EMAIL: 'ENTER YOUR EMAIL ADDRESS',
  MCX_ENTER_PASSWORD: 'ENTER PASSWORD',
  ERROR_MESSAGE_LOGIN_INPUT: 'ENTER VALID EMAIL AND PASSWORD',
  MCX_FORGOT_PASSWORD: 'Forgot password',
  MCX_PASSWORD_RESET_TITLE: 'PASSWORD RESET',
  MCX_RESET_YOUR_PASSWORD: 'RESET YOUR PASSWORD',
  //Dashboard screen strings
  MC<PERSON>_DASHBOARD_TITLE: 'Dashboard',
  MCX_MECHANIC_AVAILABILITY_TEXT: 'Availability',
  MCX_MECHANIC_APPOINTMENT_TEXT: 'Appointment',
  //Schedule service screen strings
  MCX_MY_VEHICLE_TEXT: 'MY VEHICLE',
  MCX_VEHICLE_SELECT_TEXT: 'Select my vehicle',
  MCX_MY_LOCATION_TEXT: 'MY LOCATION',
  MCX_SERVICES_NEEDED_TEXT: 'Services Needed',
  MCX_CHOOSE_CURRENT_LOCATION_TEXT: 'Choose current location',
  MCX_SAVED_LOCATION_TEXT: 'Saved location',
  MCX_SEARCH_NEARBY_LOCATION_TEXT: 'Search near by location',
  MCX_SERVICE_TYPE_TEXT: 'Select service type',
  MCX_SERVICE_TYPE_KEYWORD: 'SERVICE TYPE',
  MCX_PRE_SCREENING_QUESTIONS: 'PRE SCREENING QUESTIONS',
  MCX_DROPDOWN_PLACEHOLDER: 'Choose option',
  MCX_TEXT_INPUT_PLACEHOLDER_TYPE_YOUR_ANSWER: 'Type your answer',
  MCX_ADD_YOUR_OWN_PRICE_MODEL_TEXT: 'ADD YOUR OWN PRICE MODEL',
  MCX_SELECT_TIME_TEXT: 'SELECT TIME',
  MCX_ALERTS_AND_CONFIRMATION_TEXT: 'ALERTS AND CONFIRMATION',
  MCX_SELECT_ALERT_CONFIRMATION_TEXT: 'Select alert and confirmation',
  MCX_CANCEL_BUTTON_TEXT: 'CANCEL',
  MCX_CANCEL_SCHEDULE_TEXT: 'CANCEL SCHEDULE',
  MCX_CONFIRM_APPOINTMENT_TEXT: 'CONFIRM APPOINTMENT',
  MCX_SCHEDULE_NOW_TEXT: 'SCHEDULE NOW',
  MCX_MECHANIC_DETAIL_TITLE: 'MECHANIC DETAIL',
  MCX_MECHANIC_EXPERIENCE_LABEL: 'EXPERIENCE',
  MCX_MECHANIC_CERTIFICATIONS_LABEL: 'CERTIFICATIONS',
  MCX_MECHANIC_AVAILABILITY_TITLE: 'AVAILABILITY',
  MCX_MECHANIC_LOCATION_TITLE: 'LOCATION',
  MCX_MECHANIC_PERFORMANCE_METRICS_TITLE: 'PERFORMANCE METRICS',
  MCX_MECHANIC_CANCELLATION_RATE_LABEL: 'Cancellation Rate',
  MCX_MECHANIC_BY_AVAILABILITY_LABEL: 'By Availability',
  MCX_MECHANIC_TOTAL_APPOINTMENTS_LABEL: 'Total Appointments',
  MCX_MECHANIC_SERVICES_TITLE: 'SERVICES',
  MCX_MECHANIC_NO_SERVICES_TEXT: 'No services available',
  MCX_MECHANIC_PERSONAL_INFO_TITLE: 'PERSONAL INFORMATION',
  MCX_MAKE_LABEL: 'Make',
  MCX_MODEL_LABEL: 'Model',
  MCX_YEAR_LABEL: 'Year',
  MCX_FUEL_TYPE_LABEL: 'Fuel Type',
  MCX_MECHANIC_GENDER_LABEL: 'Gender:',
  MCX_MECHANIC_DOB_LABEL: 'Date of Birth:',
  MCX_MECHANIC_EMAIL_LABEL: 'Email:',
  MCX_MECHANIC_MOBILE_LABEL: 'Mobile:',
  MCX_MECHANIC_PROFESSIONAL_DETAILS_TITLE: 'PROFESSIONAL DETAILS',
  MCX_MECHANIC_PROVIDER_TYPE_LABEL: 'Provider Type:',
  MCX_MECHANIC_REGISTRATION_STATUS_LABEL: 'Registration Status:',
  MCX_MECHANIC_STATUS_LABEL: 'Status:',
  MCX_NO_MECHANICS_FOUND: 'No mechanics found',
  MCX_ADVANTAGES_TEXT: 'Advantages of myCANx…',
  MCX_ACCOUNT_CREATED_TITLE: 'Account Created!',
  MCX_WELCOME_TITLE: 'Welcome to myCANx',
  MCX_FEATURES_INTRO_TEXT: 'What you can do with myCANx:',
  MCX_GET_STARTED_TEXT: 'Get Started',
  MCX_FIRST_NAME_TITLE: 'FIRST NAME',
  MCX_LAST_NAME_TITLE: 'LAST NAME',
  MCX_EMAIL_INPUT_TITLE: 'EMAIL',
  MCX_MOBILE_NUMBER_TITLE: 'MOBILE NUMBER',
  MCX_PERSONAL_INFO_TITLE: 'Personal Information',
  MCX_PROCESSING_TEXT: 'Processing...',
  MCX_CONTINUE_TEXT: 'Continue',
  MCX_VEHICLE_INFO_TITLE: 'Vehicle Information',
  MCX_DATE_PLACEHOLDER_TEXT: 'MM/DD/YYYY',
  MCX_DATE_TEXT: 'DATE',
 MCX_TIME_TEXT:"SELECT TIME",
  MCX_NOTES_AND_IMAGES: 'NOTES AND IMAGES',
  MCX_NOTES_PLACEHOLDER_TEXT: 'Please add Notes',
  //User profile screen strings
  MCX_USER_PROFILE_Title: 'MY PROFILE',
  MCX_USER_DETAIL_Title: 'MY DETAILS',
  MCX_USER_DETAIL_Text: 'MY DETAILS',
  MCX_USER_PROFILE_VEHICLE_TITLE: 'MY VEHICLES',
  MCX_USER_PROFILE_ADD_VEHICLES: 'Add Vehicles',
  //screen titles
  MCX_SCHEDULE_SERVICE_TITLE: 'SCHEDULE SERVICE',
  MCX_BOOK_APPOINTMENT_TITLE : 'BOOK APPOINTMENT',
  MCX_MY_PROFILE_TITLE: 'My Profile',
  //user profile strings
  MCX_EDIT_PROFILE_DETAILS_TEXT: 'Edit profile Details',
  MCX_ADD_PAYMENT_METHOD_TEXT: 'Add payment method',
  MCX_ADD_VEHICLES_TEXT: 'Add Vehicles',
  MCX_DELETE_ACCOUNT_TEXT: 'Delete Account',
  MCX_DELETE_ACCOUNT_KEYWORD: 'DELETE',
  //edit profile strings
  MCX_EDIT_PROFILE_TITLE: 'MY DETAILS',
  MCX_FIRST_NAME_LABEL: 'FIRST NAME',
  MCX_LAST_NAME_LABEL: 'LAST NAME',
  MCX_EMAIL_LABEL: 'EMAIL',
  MCX_MOBILE_LABEL: 'MOBILE',
  MCX_GENDER_LABEL: 'GENDER',
  MCX_MARITAL_STATUS_LABEL: 'MARITAL STATUS',
  MCX_DATE_OF_BIRTH_LABEL: 'DATE OF BIRTH',
  MCX_CHANGE_PHOTO_TEXT: 'Change Photo',
  MCX_SAVE_MY_PROFILE_BUTTON: 'SAVE MY PROFILE',
  MCX_FIRST_NAME_PLACEHOLDER: 'Enter first name',
  MCX_LAST_NAME_PLACEHOLDER: 'Enter last name',
  MCX_EMAIL_PLACEHOLDER: 'Enter email address',
  MCX_MOBILE_PLACEHOLDER: 'Enter mobile number',
  MCX_GENDER_PLACEHOLDER: 'Select gender',
  MCX_MARITAL_STATUS_PLACEHOLDER: 'Select marital status',
  //address fields strings
  MCX_SEARCH_LOCATION_LABEL: 'SEARCH LOCATION',
  MCX_ADDRESS_1_LABEL: 'ADDRESS 1',
  MCX_ADDRESS_2_LABEL: 'ADDRESS 2',
  MCX_CITY_LABEL: 'CITY',
  MCX_STATE_LABEL: 'STATE',
  MCX_ZIP_CODE_LABEL: 'ZIP CODE',
  MCX_COUNTRY_LABEL: 'COUNTRY',
  MCX_SEARCH_LOCATION_PLACEHOLDER: 'Search for a near by place',
  MCX_ADDRESS_1_PLACEHOLDER: 'Enter address line 1',
  MCX_ADDRESS_2_PLACEHOLDER: 'Enter address line 2',
  MCX_CITY_PLACEHOLDER: 'Enter city',
  MCX_STATE_PLACEHOLDER: 'Enter state',
  MCX_ZIP_CODE_PLACEHOLDER: 'Enter zip code',
  MCX_COUNTRY_PLACEHOLDER: 'Enter country',
  //find mechanics strings
  MCX_SEARCH_NEARBY_TEXT: 'Search for a near by place',
  MCX_SELECT_AVAILABILITY_TEXT: 'Select availability',
  //appointments strings
  MCX_UPCOMING_APPOINTMENTS_TITLE: 'UPCOMING APPOINTMENTS',
  MCX_OTHER_APPOINTMENTS_TITLE: 'OTHER APPOINTMENTS',
  // refer friend strings
  MCX_REFER_FRIEND_TITLE: 'REFER A FRIEND',
  MCX_REFER_FRIEND_DESCRIPTION:
    'Share the myCANx love and give friends free point to try myCANx services.',
  MCX_REFER_FRIEND_SUBTITLE: 'Send your friends free point',
  MCX_REFER_FRIEND_EARNED_TEXT: 'Earned :',
  MCX_REFER_FRIEND_COPY_LINK: 'https://p5e1.app.link/zyzj9rtg3xb',
  MCX_REFER_FRIEND_INVITE_BUTTON: 'INVITE FRIEND',
  MCX_DOLLOR_SYMBOL: '$',
  //Book appointments screen strings
  MCX_MY_MECHANIC_TITLE: 'MY MECHANIC',
  MCX_SAVE_LOCATION_TEXT: 'Save this location',
  MCX_SELECT_SAVED_LOCATION_LABEL: 'SELECT SAVED LOCATION',
  MCX_SERVICES_TITLE: 'Services Needed',
  MCX_REMOVE_SERVICE_TEXT: 'Remove',
  MCX_SUB_SERVICE_LABEL: 'SUB-SERVICE',
  MCX_ADD_ANOTHER_SERVICE_TEXT: '+ Add Another Service',
  MCX_SCREENING_DATA_TITLE: 'SCREENING DATA',
  MCX_PRICE_MODEL_SERVICE_NAME: 'Service name',
  MCX_PRICE_MODEL_PRICE_RANGE: 'Price range',
  MCX_PRICE_MODEL_BID_RATE: 'Enter bid rate',
  MCX_PRICE_MODEL_TOTAL: 'TOTAL',
  MCX_SORRY_TEXT: 'SORRY',
  MCX_EXPERIENCE_SUFFIX: ' exp',
  MCX_AVAILABILITY_LABEL: 'Availability ',
  MCX_CANCEL_TEXT: 'Cancel',
  MCX_VERSION: 'v1.5.4',
  MCX_PRECISION_LABEL: 'Precise',
  MCX_CREATE_ACCOUNT: 'Create Account',
  // Registration password/create password screen strings
  MCX_CREATE_PASSWORD_TITLE: 'CREATE PASSWORD',
  MCX_PASSWORD_LABEL: 'PASSWORD',
  MCX_PASSWORD_PLACEHOLDER: 'Enter password',
  MCX_CONFIRM_PASSWORD_LABEL: 'CONFIRM PASSWORD',
  MCX_CONFIRM_PASSWORD_PLACEHOLDER: 'Confirm password',
  MCX_PASSWORD_MIN_LENGTH_ERROR: 'Password must be at least 8 characters.',
  MCX_PASSWORD_UPPERCASE_ERROR:
    'Password must contain at least one uppercase letter.',
  MCX_PASSWORD_LOWERCASE_ERROR:
    'Password must contain at least one lowercase letter.',
  MCX_PASSWORD_NUMBER_ERROR: 'Password must contain at least one number.',
  MCX_PASSWORD_SPECIALCHAR_ERROR:
    'Password must contain at least one special character.',
  MCX_CONFIRM_PASSWORD_REQUIRED: 'Confirm your password.',
  MCX_PASSWORDS_MISMATCH: 'Passwords do not match.',
  // Help Screen
  MCX_HELP_TITLE: 'HELP',
  MCX_HELP_FAQ_SECTION: 'F.A.Q',
  // Account Registration Screen
  MCX_ACCOUNT_REGISTRATION_TITLE: 'ACCOUNT REGISTRATION',
  MCX_MY_VEHICLE_TITLE: 'MY VEHICLE',
  MCX_VIN_LABEL: 'VIN',
  MCX_LOAD_VIN_BUTTON: 'LOAD VIN',
  MCX_CONTINUE_BUTTON: 'CONTINUE',
  //Settings screen
  MCX_GPS_SETTINGS_TEXT: 'GPS SETTINGS',
  MCX_NOTIFICATION_TITLE: 'NOTIFICATIONS',
  MCX_EMAIL_TITLE: 'Email',
  MCX_USE_MY_LOCATION_TITLE: 'Use My Location',
  //notification strings
  MCX_MESSAGES_TITLE: 'MESSAGES',
  MCX_PENDING_MESSAGES_TEXT: 'No pending tasks available',
  //drawer title strings
  MCX_LOGOUT_TEXT: 'LOGOUT',
  //Registration screen strings
  MCX_ADDRESS_INFO_TEXT: 'ADDRESS INFORMATION',
  MCX_STREET_ADDRESS_TEXT: 'Enter your street address',
  MCX_STREET_ADDRESS_LABEL: 'Street Address',
  MCX_ENTER_CITY_TEXT: 'Enter your city',
  MCX_ENTER_CITY_LABEL: 'City',
  //Permission screen strings
  MCX_APPROXIMATE_LABEL: 'Approximate',
  MCX_PERMISSION_1_TEXT: 'While using the app',
  MCX_PERMISSION_2_TEXT: 'Only this time',
  MCX_PERMISSION_3_TEXT: "Don't allow",
  MCX_APPOINTMENT_SESSION_TITLE: 'APPOINTMENT SESSION',
  MCX_DISPUTE_INVOICE_TITLE: 'DISPUTE INVOICE',
  MCX_RESET_FAILED: 'Reset Password Failed',
  MCX_ALERADY_REGISTERED_EMAIL:
    'Your email is already registered in another myCANx application. Please sign up for another account, or simply use a different email for this app.',
  MCX_CHECK_MAIL: 'Check Mail',
  MCX_CHECK_INBOX: 'Check your inbox for a password reset link',
  MCX_WENT_WRONG: 'Something went wrong. Please try again',
  MCX_ERROR: 'Error',
  MCX_ENTER_MAIL: 'Please enter your email address',
  MCX_ENTER_VALID_MAIL: 'Please enter a valid email address',
  MCX_OK: 'OK',
  MCX_INCORRECT_MAIL: 'Incorrect Email',
  MCX_EMAIL_NOT_FOUND: "We couldn't find your email address",
  MCX_RESET_PASSWORD: 'Reset Password',
};
// Bottom tabs icons path
export const BottomTabIcons = {
  MCX_DASHBOARD: require('../../assets/bottom_nav_icons/dashboard_con.png'),
  MCX_SCHEDULE: require('../../assets/bottom_nav_icons/service.png'),
  MCX_FIND_MECHANICS: require('../../assets/bottom_nav_icons/find_mechanic.png'),
  MCX_APPOINMENT: require('../../assets/bottom_nav_icons/appointments.png'),
};
export const DashboardIcons = {
  MCX_OIL_CHANGE_ICON: require('../../assets/dashboard_icons/oil.png'),
  MCX_ROADSIDE_ASSISTANCE_ICON: require('../../assets/dashboard_icons/roadside.png'),
  MCX_DIAGNOSTICS_INSPECTION_ICON: require('../../assets/dashboard_icons/diagnostics.png'),
  MCX_REPAIR_ICON: require('../../assets/dashboard_icons/repair.png'),
  MCX_STAR_ICON: require('../../assets/dashboard_icons/red_star.png'),
  MCX_LIGHT_STAR_ICON: require('../../assets/dashboard_icons/light_star.png'),
};
// Common icons path
export const AppCommonIcons = {
  MCX_MYCANX_LOGO_TEXT: require('../../assets/common_icons/mycanx_logo_text.png'),
  MCX_MESSAGE_ICON: require('../../assets/common_icons/message.png'),
  MCX_CHAT_ICON: require('../../assets/common_icons/chat.png'),
  MCX_MENU_ICON: require('../../assets/common_icons/menu.png'),
  MCX_APP_ICON: require('../../assets/mycanx_logo.png'),
  MCX_USER_PROFILE_PIC: require('../../assets/dashboard_icons/user_profile_pic.png'),
  MCX_BACKGROUND_IMAGE: require('../../assets/dashboard_icons/main_bg_image.jpg'),
  MCX_ARROW_RIGHT: require('../../assets/common_icons/arrow_right.png'),
  MCX_ARROW_RIGHT_RED: require('../../assets/common_icons/right_arrow_red.png'),
  MCX_ARROW_FORWARD: require('../../assets/common_icons/arrow_forward.png'),
  MCX_CARD_ICON_1: require('../../assets/common_icons/mastercard.png'),
  MCX_DELETE_ICON: require('../../assets/common_icons/delete.png'),
  MCX_IMAGE_UPLOAD_ICON: require('../../assets/common_icons/image.png'),
  MCX_REFER_FRIEND_ICON: require('../../assets/common_icons/refer_a_friend.png'),
  MCX_NO_MESSAGES_ICON: require('../../assets/common_icons/no_messages.png'),
  //drawerIcons
  MCX_PROFILE_ICON: require('../../assets/drawer_icons/profile.png'),
  MCX_SETTINGS_ICON: require('../../assets/drawer_icons/settings.png'),
  MCX_HELP_ICON: require('../../assets/drawer_icons/help.png'),
  MCX_REFER_FRIEND_ICON_1: require('../../assets/drawer_icons/refer_logo.png'),
  MCX_WORK_HISTORY_ICON: require('../../assets/drawer_icons/terms.png'),
  MCX_MESSAGES_ICON: require('../../assets/drawer_icons/messages.png'),
  MCX_SCHEDULE_SERVICE_ICON: require('../../assets/bottom_nav_icons/service.png'),
  MCX_FIND_MECHANIC_ICON: require('../../assets/bottom_nav_icons/find_mechanic.png'),
  MCX_DASHBOARD_ICON: require('../../assets/bottom_nav_icons/dashboard_con.png'),
  //Exception Icons
  MCX_EXCEPTION_NO_DATA_FOUND_ICON: require('../../assets/exception_icons/execption_no_data_found.png'),
  MCX_OIL_ICON: require('../../assets/images/oil-icon.jpg'),
};
// Common component names
export const RouteNames = {
  // Bottom tabs names
  MCX_DASHBOARD: 'dashboard',
  MCX_SCHEDULE: 'schedule',
  MCX_FIND_MECHANIC: 'findMechanic',
  MCX_APPOINTMENTS: 'appointments',
  //schedule service component names

  MCX_MY_LOCATION: 'My Location',
  //menu item names
  MCX_PROFILE_INDEX: 'profile',
  MCX_PRIVACY_POLICY_INDEX: 'privacy_policy',
  MCX_TERMS_CONDITIONS_INDEX: 'terms_conditions',
  MCX_REFER_FRIEND_INDEX: 'refer_friend',
  MCX_HELP_INDEX: 'help',
  MCX_SETTINGS_INDEX: 'settings',

  //navigation strings
  MCX_NAV_USER_PROFILE: 'UserProfile',
  MCX_NAV_EDIT_PROFILE: 'EditProfile',
  MCX_NAV_EDIT_PAYMENT: 'EditPaymentPage',
  MCX_NAV_VEHICLE_EDIT: 'VehicleEditPage',
  MCX_NAV_PRIVACY_POLICY: 'PrivacyPolicy',
  MCX_NAV_TERMS_CONDITIONS: 'TermsConditions',
  MCX_NAV_REFER_FRIEND: 'ReferFriend',
  MCX_NAV_HELP: 'Help',
  MCX_HELP_DETAILS: 'HelpDetails',
  MCX_NAV_SETTINGS: 'SettingsScreen',
  MCX_NAV_MESSAGES_INDEX: 'index',
  MCX_NAV_ACCOUNT_REGISTRATION: 'AccountRegistration',
  MCX_NAV_LOGIN_SCREEN: 'LoginMainScreen',
  MCX_NAV_DashBoard: 'DashBoard',
  MCX_NAV_MESSAGES: 'MessagesScreen',
  MCX_NAV_PENDING: 'PendingScreen',
  MCX_NAV_MESSAGE_DETAILS: 'MessageDetails',
  MCX_NAV_EMPTY_MESSAGE_SCREEN: 'EmptyMessages',
  MCX_NAV_SPLASH: 'Splash',
  MCX_NAV_LoginMainScreen: 'LoginMainScreen',
  MCX_NAV_AppLoginPageScreen: 'AppLoginPageScreen',
  MCX_NAV_ResetPasswordScreen: 'ResetPasswordScreen',
  MCX_NAV_MechanicProfilePage: 'MechanicProfilePage',
  MCX_NAV_BookAppointmentScreen: 'BookAppointmentScreen',
  MCX_NAV_AppointmentDetails: 'AppointmentDetails',
  MCX_NAV_WORK_HISTORY: 'ServiceHistory',
  MCX_NAV_CHAT_LIST: 'ChatListScreen',
  MCX_NAV_CHAT_ROOM: 'ChatRoomScreen',
  MCX_NAV_NETWORK_FAILED: 'NetworkFailedPage',
} as const;
// Common error messages
export const ExceptionStrings = {
  MCX_NO_VEHICLE_ERROR: 'Something went wrong, please try again later.',
  MCX_EXCEPTION_NO_VEHICLE_FOUND_TEXT: 'No vehicles available',
  MCX_EXCEPTION_NO_LOCATION_FOUND_TEXT: 'No locations available',
  MCX_EXCEPTION_NO_SERVICE_FOUND_TEXT: 'No services available',
  MCX_EXCEPTION_NO_SCREENING_QUESTIONS_TEXT: 'No screening questions available',
  MCX_EXCEPTION_NO_DATA_FOUND_TEXT: 'NO DATA FOUND',
  // VIN validation messages
  MCX_VIN_ERROR_TITLE: 'VIN Error',
  MCX_VIN_ERROR_FETCH_FAILED: 'Failed to fetch VIN data',
  MCX_VIN_ERROR_INVALID_DATA: 'Invalid VIN data',
  MCX_VIN_ERROR_MODEL_YEAR:
    'The Model Year decoded for this VIN may be incorrect',
  MCX_VIN_SUCCESS_TITLE: 'Success',
  MCX_VIN_SUCCESS_LOADED: 'VIN loaded successfully',
  MCX_VIN_SAVE_SUCCESS: 'Vehicle information saved successfully!',
  MCX_VIN_SAVE_ERROR: 'Failed to save vehicle information',
  MCX_VIN_USER_ERROR:
    'User ID not found. Please start registration from step 1.',
  MCX_VIN_DATA_ERROR:
    'User Data not found. Please start registration from step 1.',
  MCX_EXCEPTION_NO_UPCOMING_APPOINTMENTS_TEXT: 'NO DATA FOUND',
  MCX_EXCEPTION_NO_OTHER_APPOINTMENTS_TEXT: 'NO DATA FOUND',
  MCX_EXCEPTION_NO_MESSAGE_DISPLAY: 'No messages to display',
  MCX_EXCEPTION_ENTER_VALID_VIN_LABEL: 'Please enter a VIN number',
  MCX_EXCEPTION_ERROR_LABEL: 'Error',
};
