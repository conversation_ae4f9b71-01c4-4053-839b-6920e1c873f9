import {
  ref,
  query,
  orderByChild,
  orderByKey,
  equalTo,
  get,
  set,
  update,
  push,
  remove,
} from '@react-native-firebase/database';
import storage from '@react-native-firebase/storage';
import {getApp} from '@react-native-firebase/app';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../globals';
import {DropdownOption} from '../configs/types';

interface VehicleData {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning?: string;
}

type Location = {
  latitude: number;
  longitude: number;
};

interface WorkRequestData {
  alert: string;
  'created-time': number;
  customer: string;
  latitude: number;
  longitude: number;
  mechanic: string;
  notes: {
    images: string;
    'notes-content': string;
  };
  'request-address': {
    address_array: {
      address1: string;
      address2?: string;
      city?: string;
      country?: string;
      state?: string;
      zipcode?: string;
    };
    formatted_address: string;
    location: {
      latitude: number;
      longitude: number;
    };
  };
  'request-date': string;
  'request-time-range': string;
  'request-type': string;
  'screening-FAQ': Record<string, any>;
  services: Record<
    string,
    {
      'customer-bid': string[];
      'mechanic-bid': string[];
      'service-type': string;
      'sub-services': string[];
    }
  >;
  status: string;
  vehicle: string;
}

export const VehicleService = {
  getVehicles: async (customerId: string): Promise<DropdownOption[]> => {
    try {
      const snapshot = await get(
        ref(getApp().database(), `customer/${customerId}/myvehicles`),
      );
      const vehicles = snapshot.val();
      if (vehicles) {
        return Object.keys(vehicles).map(key => {
          const vehicle = vehicles[key];
          const label =
            vehicle.name ||
            `${vehicle.make || ''} ${vehicle.model || ''}`.trim() ||
            key;
          return {label, value: key};
        });
      }
      return [];
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      return [];
    }
  },

  addVehicleInfo: async (customerId: string, vehicleData: VehicleData) => {
    try {
      const newRef = push(
        ref(getApp().database(), `customer/${customerId}/myvehicles`),
      );
      await set(newRef, vehicleData);
      return newRef;
    } catch (error) {
      console.error('Error adding vehicle:', error);
      throw error;
    }
  },

  removeVehicleInfo: async (customerId: string, vehicleId: string) => {
    try {
      return await remove(
        ref(
          getApp().database(),
          `customer/${customerId}/myvehicles/${vehicleId}`,
        ),
      );
    } catch (error) {
      console.error('Error removing vehicle:', error);
      throw error;
    }
  },

  getVehicleServices: () => {
    return ref(getApp().database(), 'service');
  },

  getServiceIdNameMap: async (): Promise<Record<string, string>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'service'));
      const serviceData = snapshot.val();
      const map: Record<string, string> = {};
      if (serviceData) {
        Object.keys(serviceData).forEach(key => {
          const service = serviceData[key];
          map[key] = service.name || key;
        });
      }
      return map;
    } catch (error) {
      console.error('Error fetching service map:', error);
      return {};
    }
  },
  getMultiplePendingRequestRef: (customerId: string) => {
    return query(
      ref(getApp().database(), 'multiple-request'),
      orderByChild('customer'),
      equalTo(customerId),
    );
  },

  getSubServiceIdNameMap: async (): Promise<Record<string, string>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'sub-service'));
      const subServiceData = snapshot.val();
      const map: Record<string, string> = {};
      if (subServiceData) {
        Object.keys(subServiceData).forEach(key => {
          const subService = subServiceData[key];
          map[key] = subService.name || key;
        });
      }
      return map;
    } catch (error) {
      console.error('Error fetching sub-service map:', error);
      return {};
    }
  },

  getSubServicePriceData: async (): Promise<Record<string, any>> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'sub-service'));
      const subServiceData = snapshot.val();
      return subServiceData || {};
    } catch (error) {
      console.error('Error fetching sub-service price data:', error);
      return {};
    }
  },

  getPendingRequestData: (customerId: string) => {
    return query(
      ref(getApp().database(), `customer/${customerId}/work-requests`),
      orderByChild('status'),
      equalTo('pending'),
    );
  },

  getMultiplePendingRequest: async (): Promise<Record<string, any>> => {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) {
      throw new Error('Customer ID not found');
    }
    const db = getApp().database();
    const queryRef = query(
      ref(db, 'multiple-request'),
      orderByChild('customer'),
      equalTo(customerId),
    );
    const snapshot = await get(queryRef);
    return snapshot.val() || {};
  },

  getAppointments: (customerId: string) => {
    return query(
      ref(getApp().database(), `customer/${customerId}/appointments`),
      orderByChild('status'),
      equalTo('new'),
    );
  },

  getLoggedMechanics: () => {
    return query(
      ref(getApp().database(), 'mechanic'),
      orderByChild('login-status'),
      equalTo(true),
    );
  },

  getPreScreeningQuestions: () => {
    return ref(getApp().database(), 'screening-questions');
  },

  getAlertConfirmation: async (): Promise<DropdownOption[]> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'alert'));
      const alertData = snapshot.val();
      if (alertData) {
        const options = Object.keys(alertData).map(key => {
          const alertItem = alertData[key];
          const alertValue =
            alertItem.alert || alertItem.label || alertItem.name || key;
          const numValue = parseInt(alertValue, 10) || 0;
          let label: string;
          if (numValue >= 60) {
            const hours = numValue / 60;
            label = `${hours} hour${hours !== 1 ? 's' : ''} before appointment`;
          } else {
            label = `${alertValue} minutes before appointment`;
          }
          return {label, value: key, sortValue: numValue};
        });
        return options.sort((a, b) => a.sortValue - b.sortValue);
      }
      return [];
    } catch (error) {
      console.error('Error fetching alert confirmations:', error);
      return [];
    }
  },

  updateCustomerLocation: async (customerId: string, location: Location) => {
    try {
      return await set(
        ref(getApp().database(), `customer/${customerId}/current-location`),
        {latitude: location.latitude, longitude: location.longitude},
      );
    } catch (error: any) {
              // Silently handle permission errors - don't show to user
              if (error?.code !== 'database/permission-denied') {
                console.error('Error updating location in DB:', error);
              }
            }
  },

  getCustomerLocation: async () => {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    const db = getApp().database();
    return ref(db, `customer/${customerId}/current-location`);
  },

  createWorkRequest: async (
    workRequestData: WorkRequestData,
  ): Promise<string> => {
    try {
      const db = getApp().database();
      const workRequestRef = push(ref(db, 'work-request'));
      await set(workRequestRef, workRequestData);

      const workRequestId = workRequestRef.key || 'unknown';

      await Promise.all([
        set(
          ref(
            db,
            `customer/${workRequestData.customer}/work-requests/${workRequestId}/status`,
          ),
          'new',
        ),
        set(
          ref(
            db,
            `mechanic/${workRequestData.mechanic}/work-requests/${workRequestId}/status`,
          ),
          'new',
        ),
      ]);

      return workRequestId;
    } catch (error) {
      console.error('Error creating work request:', error);
      throw error;
    }
  },
  updateWorkRequestStatus: async (
    workRequestId: string,
    status: string,
  ): Promise<void> => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) throw new Error('Customer ID not found');

      await Promise.all([
        set(
          ref(getApp().database(), `work-request/${workRequestId}/status`),
          status,
        ),
        set(
          ref(
            getApp().database(),
            `customer/${customerId}/work-requests/${workRequestId}/status`,
          ),
          status,
        ),
      ]);
    } catch (error) {
      console.error('Error updating work request status:', error);
      throw error;
    }
  },

  getWorkRequests: (customerId: string) => {
    return query(
      ref(getApp().database(), 'work-request'),
      orderByChild('customer'),
      equalTo(customerId),
    );
  },

  getWorkRequestById: (workRequestId: string) => {
    return ref(getApp().database(), `work-request/${workRequestId}`);
  },

  uploadImageToStorage: async (
    imageUri: string,
    folder: string = 'work-request-images',
  ): Promise<string> => {
    try {
      const filename = `${Date.now()}_${Math.random()
        .toString(36)
        .substring(7)}.jpg`;
      const reference = storage().ref(`${folder}/${filename}`);

      await reference.putFile(imageUri);
      const downloadURL = await reference.getDownloadURL();

      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  },

  uploadMultipleImages: async (
    imageUris: string[],
    folder: string = 'work-request-images',
  ): Promise<string[]> => {
    try {
      const uploadPromises = imageUris.map(uri =>
        VehicleService.uploadImageToStorage(uri, folder),
      );
      const downloadURLs = await Promise.all(uploadPromises);
      return downloadURLs;
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      throw error;
    }
  },

  fetchSavedLocations: async (customerId: string): Promise<any[]> => {
    try {
      const snapshot = await get(
        ref(getApp().database(), `customer/${customerId}/saved-locations`),
      );
      const locations = snapshot.val();
      if (locations) {
        return Object.keys(locations).map(key => ({
          id: key,
          ...locations[key],
        }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      return [];
    }
  },

  saveLocation: async (
    customerId: string,
    locationData: {
      name: string;
      address: string;
      coordinates: {latitude: number; longitude: number};
    },
  ): Promise<string> => {
    try {
      const newLocationRef = push(
        ref(getApp().database(), `customer/${customerId}/saved-locations`),
      );
      await set(newLocationRef, {
        name: locationData.name,
        address: locationData.address,
        coordinates: locationData.coordinates,
      });
      return newLocationRef.key || 'unknown';
    } catch (error) {
      console.error('Error saving location:', error);
      throw error;
    }
  },

  updateSavedLocation: async (
    customerId: string,
    locationId: string,
    locationData: {
      name: string;
      address: string;
      coordinates: {latitude: number; longitude: number};
    },
  ): Promise<void> => {
    try {
      await update(
        ref(
          getApp().database(),
          `customer/${customerId}/saved-locations/${locationId}`,
        ),
        {
          name: locationData.name,
          address: locationData.address,
          coordinates: locationData.coordinates,
        },
      );
    } catch (error) {
      console.error('Error updating saved location:', error);
      throw error;
    }
  },

  deleteSavedLocation: async (
    customerId: string,
    locationId: string,
  ): Promise<void> => {
    try {
      await remove(
        ref(
          getApp().database(),
          `customer/${customerId}/saved-locations/${locationId}`,
        ),
      );
    } catch (error) {
      console.error('Error deleting saved location:', error);
      throw error;
    }
  },

  fetchAppointmentDetails: (appointmentId: string) => {
    return query(
      ref(getApp().database(), 'appointment'),
      orderByKey(),
      equalTo(appointmentId),
    );
  },
  getAllWorkRequest: async () => {
    const customerId = (await AsyncStorage.getItem(GC_CUSTOMER_ID)) as string;
    return query(
      ref(getApp().database(), 'customer')
        .child(customerId)
        .child('work-requests')
        .orderByChild('status')
        .equalTo('accepted'),
    );
  },

  fetchWorkRequest: (workRequestId: string) => {
    return query(
      ref(getApp().database(), 'work-request'),
      orderByKey(),
      equalTo(workRequestId),
    );
  },

  fetchSubServiceName: (subServiceId: string) => {
    return query(
      ref(getApp().database(), 'sub-service'),
      orderByKey(),
      equalTo(subServiceId),
    );
  },

  fetchServiceName: (serviceId: string) => {
    return query(
      ref(getApp().database(), 'service'),
      orderByKey(),
      equalTo(serviceId),
    );
  },
  fetchInvoiceGenerated: (wrkReqId: string) => {
     return query(
      ref(getApp().database(), 'invoice')
      .orderByKey()
      .equalTo(wrkReqId)
     )
  },
  getMechanicDetail: (mechanicId: string) => {
    return ref(getApp().database(), `mechanic/${mechanicId}`);
  },

  fetchUserInformation: (customerId: string) => {
    return ref(getApp().database(), `customer/${customerId}`);
  },

  fetchMechanicAvailability: async (
    latitude: number,
    longitude: number,
    serviceIds: string[],
  ): Promise<any> => {
    try {
      
      const mechanicsSnapshot = await get(
        query(
          ref(getApp().database(), 'mechanic'),
          orderByChild('login-status'),
          equalTo(true),
        ),
      );
      const mechanics = mechanicsSnapshot.val();

      if (!mechanics) {
        return {};
      }

      const availableMechanics = Object.keys(mechanics).filter(mechanicId => {
        const mechanic = mechanics[mechanicId];
        
        // Check if account is disabled
        if (mechanic['account-disabled']) {
          return false;
        }

        // Check location - try both 'location' and 'currentLocation' fields
        const mechanicLocation = mechanic.location || mechanic.currentLocation;
        if (mechanicLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
          const distance = VehicleService.calculateDistance(
            latitude,
            longitude,
            mechanicLocation.latitude,
            mechanicLocation.longitude,
          );         
        // Convert to miles (1 km = 0.621371 miles) and use 50 mile radius
          const distanceInMiles = distance * 0.621371;
          if (distanceInMiles > 50) {
            return false;
          }
        } else {
          return false;
        }

        // Check services
        if (mechanic.services && serviceIds.length > 0) {
          const mechanicServices = Object.keys(mechanic.services);
          const hasRequiredService = serviceIds.some(serviceId =>
            mechanicServices.includes(serviceId),
          );
          if (!hasRequiredService) {
            return false;
          }
        }
        return true;
      });
      return availableMechanics.reduce((acc, id) => ({...acc, [id]: true}), {});
    } catch (error) {
      console.error('Error fetching mechanic availability:', error);
      return {};
    }
  },

  fetchCustomerPaymentMethod: async (customerId: string): Promise<any> => {
    try {
      const snapshot = await get(
        query(
          ref(getApp().database(), `saved-cards/${customerId}`),
          orderByChild('status'),
          equalTo('saved'),
        ),
      );
      return snapshot.val();
    } catch (error) {
      console.error('Error fetching customer payment method:', error);
      return null;
    }
  },

  calculateDistance: (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number => {
    const R = 6371;
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  },

  createMultipleWorkRequest: () => {
    return ref(getApp().database(), 'multiple-request');
  },

  updateMultipleWorkRequest: async (
    mechanicArray: any,
    workRequestId: string,
    notes: string,
    images: string,
    alertConfirmation: string,
    requestType: string,
    requestDate: string,
    requestTimeRange: string,
    service: any,
    vehicle: string,
    requestAddress: any,
    screeningFAQ: any,
    userId: string,
  ): Promise<void> => {
    try {
      const createdTime = Math.floor(Date.now() / 1000);
      const status = 'work-request';
      await set(ref(getApp().database(), `multiple-request/${workRequestId}`), {
        'created-time': createdTime,
        'work-request-id': workRequestId,
        mechanic: mechanicArray,
        customer: userId,
        status: 'open',
      });

      await set(
        ref(getApp().database(), `multiple-request-data/${workRequestId}`),
        {
          alert: alertConfirmation,
          'created-time': createdTime,
          latitude: requestAddress.location.latitude,
          longitude: requestAddress.location.longitude,
          notes: {
            images: images,
            'notes-content': notes,
          },
          'request-type': requestType,
          'request-date': requestDate,
          'request-time-range': requestTimeRange,
          status: status,
          services: service,
          customer: userId,
          vehicle: vehicle,
          'request-address': requestAddress,
          'screening-FAQ': screeningFAQ,
          isApproved: false,
        },
      );
    } catch (error) {
      console.error('Error updating multiple work request:', error);
      throw error;
    }
  },

  getDisputeQuestions: async (): Promise<any[]> => {
    try {
      const snapshot = await get(ref(getApp().database(), 'dispute-questions'));
      const questions = snapshot.val();
      if (questions) {
        return Object.values(questions).map((q: any) => ({
          ...q,
          isChecked: false,
          input: q.type === 'entry' ? '' : undefined,
        }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching dispute questions:', error);
      return [];
    }
  },

  updateDisputeToWorkRequest: async (
    workRequestId: string,
    disputeResponse: any[],
  ) => {
    const db = getApp().database();
    const disputeRef = ref(db, `work-request/${workRequestId}/dispute`);
    await set(disputeRef, disputeResponse);
  },

  updateLog: (workRequestId: string) => {
    const db = getApp().database();
    return ref(db, `log/${workRequestId}`);
  },

  updateRightNow: async (workRequestId: string, mechanicId: string) => {
    const db = getApp().database();
    await set(ref(db, `right-now-request/${workRequestId}`), {
      'work-request-id': workRequestId,
      mechanic: mechanicId, // Add this to associate with the mechanic
    });
  },
    refSubService:  () => {
      const db = getApp().database();
      return ref(db,  'sub-service');
    },
  updateNotificationToken: async (token: string) => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) return;

      const notificationDict = {
        isEnable: true,
        'fcm-token': token,
      };

      return await set(
        ref(
          getApp().database(),
          `customer/${customerId}/settings/notification`,
        ),
        notificationDict,
      );
    } catch (error) {
      console.error('Error updating notification token:', error);
      throw error;
    }
  },

  monthNames: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],

uploadImage: async (uri: string, workRequestId: string, index: number, folder: string): Promise<any> => {
  try {
    // Use the correct path structure matching Ionic
    const filename = `${folder}/${workRequestId}_${index}`;
    const reference = storage().ref(filename);
    // Upload the file
    const task = reference.putFile(uri);

    // Monitor upload progress
    task.on('state_changed', (snapshot) => {
      const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
    });

    const snapshot = await task;
    return snapshot;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
},

getDownloadURL: async (path: string): Promise<string> => {
  try {
    const reference = storage().ref(path);
    const url = await reference.getDownloadURL();
    return url;
  } catch (error) {
    console.error('Error getting download URL:', error);
    throw error;
  }
},

// FIX: Correct method to update image path in multiple-request-data
updateMultipleRequestImagePath: async (imageUrls: string[], workRequestId: string): Promise<void> => {
  try {

    // Update the images field directly in the notes object, matching Ionic implementation
    await set(
      ref(getApp().database(), `multiple-request-data/${workRequestId}/notes/images`),
      imageUrls
    );

  } catch (error) {
    console.error('Error updating multiple request with images:', error);
    throw error;
  }
},

// Add method for single mechanic bookings (book-appointment page)
updateBookAppointmentImagePath: async (imageUrls: string[], workRequestId: string): Promise<void> => {
  try {
    // Update the images field directly in the notes object, matching Ionic implementation
    await set(
      ref(getApp().database(), `work-request/${workRequestId}/notes/images`),
      imageUrls
    );
  } catch (error) {
    console.error('Error updating single work request with images:', error);
    throw error;
  }
},

// Add method for appointment cancellation images
updateCancelAppointmentImagePath: async (imageUrls: string[], appointmentId: string): Promise<void> => {
  try {
    // Update the images field in the cancelled-state object, matching Ionic implementation
    // Join URLs with comma to match Ionic behavior
    const imageUrlString = imageUrls.join(',');
    await set(
      ref(getApp().database(), `appointment/${appointmentId}/cancelled-state/images`),
      imageUrlString
    );
  } catch (error) {
    console.error('Error updating appointment cancellation with images:', error);
    throw error;
  }
},

fetchServiceTaxBasedOnState: (state: string) => {
  return ref(getApp().database(), `mycanx-service/state-tax/${state}`);
},

};
