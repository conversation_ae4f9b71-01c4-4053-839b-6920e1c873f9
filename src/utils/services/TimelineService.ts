import { get } from '@react-native-firebase/database';
import { MessageService } from './MessageService';
import { VehicleService } from './VehicleService';

export interface TimelineImageItem {
  url: string;
  id: string;
  isLoading?: boolean;
  isLoaded?: boolean;
  error?: string;
}

export interface TimelineItem {
  title: string;
  content?: string;
  images?: TimelineImageItem[];
  icon: string;
  time: { date: string; time: string } | string;
  'created-date': number;
  tagline: string;
  mechanic: any;
}

export class TimelineService {
  static async loadTimeline(workRequestId: string): Promise<TimelineItem[]> {
    const timeline: TimelineItem[] = [];
    try {
      const workRequestRef = await MessageService.fetchWorkRequest(workRequestId);
      const workRequestSnapshot = await get(workRequestRef);
      const workRequestData = workRequestSnapshot.val();
      if (workRequestData) {
        let mechanic = {};
        if (workRequestData.mechanic) {
          const mechanicRef = VehicleService.getMechanicDetail(workRequestData.mechanic);
          const mechanicSnapshot = await get(mechanicRef);
          const mechanicData = mechanicSnapshot.val();
          mechanic = mechanicData || {};
        }
        if (workRequestData.sessionId) {
          const sessionId = workRequestData.sessionId;
          const sessionRef = await MessageService.fetchSessionDetail(sessionId);
          const sessionSnapshot = await get(sessionRef);
          const sessionData = sessionSnapshot.val();
          if (sessionData) {
            if (sessionData['cancelled-state']) {
              timeline.push({
                title: 'Cancelled by ' + (sessionData['cancelled-state']['cancelled-by'] === 'customer' ? 'Me' : 'Mechanic'),
                content: sessionData['cancelled-state'].notes || undefined,
                images: this.processImages(sessionData['cancelled-state'].images || undefined),
                icon: 'calendar',
                time: sessionData['cancelled-state']['cancelled-date'] ? this.formatSessionDate(sessionData['cancelled-state']['cancelled-date']) : { date: '00/00/0000', time: '00:00 PM' },
                'created-date': sessionData['cancelled-state']['cancelled-date'],
                tagline: 'Session Cancelled',
                mechanic: mechanic,
              });
            }
            timeline.push({
              title: 'Session',
              content: sessionData.notes ? sessionData.notes['notes-content'] : undefined,
              images: this.processImages(sessionData.notes ? sessionData.notes.images : undefined),
              icon: 'calendar',
              time: sessionData['created-time'] ? this.formatSessionDate(sessionData['created-time']) : { date: '00/00/0000', time: '00:00 PM' },
              'created-date': sessionData['created-time'],
              tagline: 'Session',
              mechanic: mechanic,
            });
          }
        }
        if (workRequestData.appointmentId) {
          const appointmentId = workRequestData.appointmentId;
          const appointmentRef = await MessageService.fetchAppointmentDetails(appointmentId);
          const appointmentSnapshot = await get(appointmentRef);
          const appointmentData = appointmentSnapshot.val();
          if (appointmentData) {
            if (appointmentData['cancelled-state']) {
              timeline.push({
                title: 'Cancelled by ' + (appointmentData['cancelled-state']['cancelled-by'] === 'customer' ? 'Me' : 'Mechanic'),
                content: appointmentData['cancelled-state'].notes || undefined,
                images: this.processImages(appointmentData['cancelled-state'].images || undefined),
                icon: 'calendar',
                time: appointmentData['cancelled-state']['cancelled-date'] ? this.formatSessionDate(appointmentData['cancelled-state']['cancelled-date']) : { date: '00/00/0000', time: '00:00 PM' },
                'created-date': appointmentData['cancelled-state']['cancelled-date'],
                tagline: 'Appointment Cancelled',
                mechanic: mechanic,
              });
            }
            timeline.push({
              title: 'Appointment',
              content: appointmentData['appointment-notes'] ? appointmentData['appointment-notes']['notes-content'] : undefined,
              images: this.processImages(appointmentData['appointment-notes'] ? appointmentData['appointment-notes'].images : undefined),
              icon: 'calendar',
              time: appointmentData['created-time'] ? this.formatSessionDate(appointmentData['created-time']) : { date: '00/00/0000', time: '00:00 PM' },
              'created-date': appointmentData['created-time'],
              tagline: 'Appointment',
              mechanic: mechanic,
            });
          }
        }
        timeline.push({
          title: 'Work Request',
          content: workRequestData.notes ? workRequestData.notes['notes-content'] : undefined,
          images: this.processImages(workRequestData.notes ? workRequestData.notes.images : undefined),
          icon: 'calendar',
          time: workRequestData['created-time'] ? this.formatSessionDate(workRequestData['created-time']) : { date: '00/00/0000', time: '00:00 PM' },
          'created-date': workRequestData['created-time'],
          tagline: workRequestData.status === 'completed' ? 'Work Complete' : 'Work Request',
          mechanic: mechanic,
        });
      }
    } catch (error) {
      console.error('Error loading timeline:', error);
    }
    return timeline.sort((a, b) => (a['created-date'] || 0) - (b['created-date'] || 0));
  }
  /**
   * Convert string image URLs to TimelineImageItem with loading states
   */
  private static processImages(images: any): TimelineImageItem[] {
    if (!images) return [];
    
    const imageArray = Array.isArray(images) ? images : [images];
    
    return imageArray.map((url: string, index: number) => ({
      url: typeof url === 'string' ? url : '',
      id: `timeline_image_${Date.now()}_${index}`,
      isLoading: true,
      isLoaded: false,
      error: undefined,
    })).filter(img => img.url);
  }

  static formatSessionDate(date: number) {
    date = date * 1000;
    let temptime: { date: string; time: string } = { date: '', time: '' };
    temptime.date = new Date(date).toLocaleString('en-US', { year: 'numeric', month: 'numeric', day: 'numeric' });
    temptime.time = new Date(date).toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
    return temptime;
  }
}
