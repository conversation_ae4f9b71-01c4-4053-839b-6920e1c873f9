/**
 * Dashboard Component with Modern Styling
 *
 * Features modern glassmorphism design with enhanced visual appeal.
 * Toggle 'useModernStyles' constant to switch between modern and original styling.
 *
 * Modern features:
 * - Glassmorphism effects with semi-transparent backgrounds
 * - Enhanced shadows and rounded corners
 * - Improved typography hierarchy
 * - Better color palette and gradients
 * - Smooth interactive states
 */
import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ImageBackground,
  TouchableOpacity,
  PermissionsAndroid,
   Platform,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import type {RootStackParamList} from '../../utils/configs/types';
import {AppCommonIcons, RouteNames} from '../../utils/constants/AppStrings';
import {Colors} from '../../utils/constants/Theme';
import {wp} from '../../utils/ResponsiveParams';
import MechanicCard from '../../components/cardstyles/MechanicCard';
import {VehicleService} from '../../utils/services/VehicleService';
import {get, onValue, ref, set, remove} from '@react-native-firebase/database';
import {getDatabase} from '@react-native-firebase/database';
import {useAuth} from '../../utils/configs/AuthContext';
import LoaderOverlay from '../../components/common/LoaderOverlay';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_FCM_TOKEN} from '../../utils/globals';
import NetInfo from '@react-native-community/netinfo';
import auth from '@react-native-firebase/auth';
import LoaderSpin from '../../components/common/LoaderSpin';
import Geolocation from 'react-native-geolocation-service';
import { GC_MILES } from '../../utils/globals';
import {normalizeMechanicRating} from '../../utils/helpers/RatingUtils';
import CustomAlert from '../../components/common/CustomAlert';
interface ServiceItem {
  key: string;
  name: string;
  tools: Record<string, string>;
  subservice?: string[];
}

const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 6371;
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const isValidLocation = (location: {lat: number, lng: number} | null) => {
  if (!location) return false;
  // Exclude common default coordinates
  const defaultCoords = [
    {lat: 37.4239559, lng: -122.0856973}, // Google HQ
    {lat: 0, lng: 0}, // Null island
  ];

  return !defaultCoords.some(coord =>
    Math.abs(coord.lat - location.lat) < 0.001 &&
    Math.abs(coord.lng - location.lng) < 0.001
  );
};

const DashBoard: React.FC = () => {
  const {user} = useAuth();
  const customerId = user?.uid;
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [alertButtons, setAlertButtons] = useState<{text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive'}[]>([]);
  const showCustomAlert = (
    title: string,
    message: string,
    buttons: {text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive'}[] = [],
  ) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons);
    setAlertVisible(true);
  };
  const hideAlert = () => setAlertVisible(false);
  const [enquiryTab, setEnquiryTab] = useState<
    {key: string; count: number; label: string}[]
  >([]);
  const [serviceList, setServiceList] = useState<ServiceItem[]>([]);
  const [mechanicList, setMechanicList] = useState<any>({});
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [customerLocation, setCustomerLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [pendingCount, setPendingCount] = useState(0);
  const [appointmentsCount, setAppointmentsCount] = useState(0);
  const [multipleCount, setMultipleCount] = useState(0);
  const [favoriteMechanic, setFavoriteMechanic] = useState<string[]>([]);
  const [isInitialRender, setIsInitialRender] = useState(true);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
const filteredMechanics = useMemo(() => {


  // Step 1: Filter by account status and services (like Ionic's filterBasedService)
  let filtered = Object.keys(mechanicList)
    .filter(key => {
      const mechanic = mechanicList[key];

      // Filter out disabled accounts (matches Ionic)
      if (mechanic['account-disabled'] === true) {
        return false;
      }

      // Filter mechanics WITHOUT location data (matches Ionic)
      if (!mechanic.location) {
        return false;
      }
      if (!mechanic.location.latitude || !mechanic.location.longitude) {
        return false;
      }

      // Service-based filtering (matches Ionic's filterBasedService)
      if (selectedService && mechanic.services) {
        const hasService = mechanic.services[selectedService];
        if (!hasService) {
          console.log(`Mechanic ${key} doesn't have service ${selectedService}`);
        }
        return hasService;
      }

      return true;
    })
    .map(key => ({id: key, ...mechanicList[key]}));


  // Step 2: Filter by location ONLY if customerLocation exists (matches Ionic)
  if (customerLocation) {

    filtered = filtered.filter(mechanic => {
      // Already filtered mechanics without location above
      const distance = calculateDistance(
        customerLocation.lat,
        customerLocation.lng,
        mechanic.location.latitude,
        mechanic.location.longitude,
      );

      // Ionic uses GC_MILES which is 50 miles
      const distanceInMiles = distance * 0.621371;

      if (distanceInMiles <= GC_MILES) {
      
        return true;
      } else {
       
        return false;
      }
    });

    // Step 3: Sort by distance (matches Ionic)
    filtered = filtered.sort((a, b) => {
      const distA = calculateDistance(
        customerLocation.lat,
        customerLocation.lng,
        a.location.latitude,
        a.location.longitude,
      );
      const distB = calculateDistance(
        customerLocation.lat,
        customerLocation.lng,
        b.location.latitude,
        b.location.longitude,
      );
      return distA - distB;
    });
  } else {
    console.log('No customer location - showing all mechanics (service filtered only)');
    // Ionic behavior: if no location, show all mechanics that match service
  }

  return filtered;
}, [mechanicList, selectedService, customerLocation]);
  // Update enquiryTab when counts change
  useEffect(() => {
    setEnquiryTab([
      {
        key: 'appointments',
        count: appointmentsCount,
        label: 'Upcoming Appointments',
      },
      {
        key: 'pendingRequests',
        count: pendingCount + multipleCount,
        label: 'Pending Requests',
      },
    ]);
  }, [pendingCount, appointmentsCount, multipleCount]);

  useEffect(() => {
    if (!customerId) {
      return;
    }

    // Network check
    const unsubscribe = NetInfo.addEventListener(state => {
      if (!state.isConnected) {
        showCustomAlert('Network Failed', 'Please check your internet connection.', [
          {text: 'OK', onPress: hideAlert},
        ]);
        navigation.navigate('NetworkFailedPage' as never);
      }
    });

    const checkEmailVerification = async () => {
      const currentUser = auth().currentUser;
      if (currentUser && !currentUser.emailVerified) {
        showCustomAlert('Warning', 'Please verify your email.', [
          {
            text: 'RESEND',
            onPress: () => {
              currentUser.sendEmailVerification();
              hideAlert();
            },
          },
          {text: 'OK', onPress: hideAlert},
        ]);
      }
    };

    const fetchFavoriteMechanic = () => {
      const favRef = VehicleService.fetchUserInformation(customerId).child('myfavorites');
      onValue(favRef, snapshot => {
        const favs = snapshot.val() || {};
        setFavoriteMechanic(Object.keys(favs));
      });
    };

  const fetchData = async () => {
  try {
    const servicesSnapshot = await get(VehicleService.getVehicleServices());
    const services = servicesSnapshot.val() || {};
    const serviceArray = Object.keys(services)
      .map(key => ({
        key,
        ...services[key],
      }))
      .reverse() as ServiceItem[];
    setServiceList(serviceArray);

    // Pre-select "Oil Change" service like Ionic does
    const oilChangeService = serviceArray.find(s => s.name === 'Oil Change');
    if (oilChangeService) {
      setSelectedService(oilChangeService.key);;
    } else if (serviceArray.length > 0) {
      // Fallback to first service if Oil Change not found
      setSelectedService(serviceArray[0]?.key);
    }

    // Services loaded
  } catch (error) {
    console.error('Error fetching services:', error);
    setServiceList([]);
  }
};

    const fetchMechanicList = async () => {
      try {
        const mechanicsSnapshot = await get(VehicleService.getLoggedMechanics());
        const mechanics = mechanicsSnapshot.val() || {};
        setMechanicList(mechanics);
        // Mechanics loaded
      } catch (error) {
        console.error('Error fetching mechanics:', error);
        setMechanicList({});
        // Mechanics loaded
      } finally {
        setLoading(false);
      }
    };

    const saveFCMToken = async () => {
      try {
        if (customerId) {
          const fcmToken = await AsyncStorage.getItem(GC_FCM_TOKEN);
          if (fcmToken) {
            await VehicleService.updateNotificationToken(fcmToken);
          } else {
            console.log('No FCM token found in AsyncStorage');
          }
        } else {
          console.log('No customer ID found, skipping FCM token update');
        }
      } catch (error) {
        console.error('Error updating FCM token:', error);
      }
    };

    fetchData();
    fetchMechanicList();
    saveFCMToken();
    checkEmailVerification();
    fetchFavoriteMechanic();
    //setCustomerLocation({lat: 0, lng: 0});

    // Set up real-time listeners
    const pendingRef = VehicleService.getPendingRequestData(customerId);
    const appointmentsRef = VehicleService.getAppointments(customerId);
    const multipleRef = VehicleService.getMultiplePendingRequestRef(customerId);

    const pendingListener = onValue(pendingRef, snapshot => {
      const data = snapshot.val() || {};
      const count = Object.keys(data).length;
      setPendingCount(count);
    });
    const appointmentsListener = onValue(appointmentsRef, snapshot => {
      const data = snapshot.val() || {};
      const count = Object.keys(data).length;
      setAppointmentsCount(count);
    });
    const multipleListener = onValue(multipleRef, snapshot => {
      const data = snapshot.val() || {};
      const count = Object.values(data).filter(
        (req: any) => req.status === 'open',
      ).length;
      setMultipleCount(count);
    });

    // Cleanup listeners on unmount
    return () => {
      pendingListener();
      appointmentsListener();
      multipleListener();
      unsubscribe();
    };
  }, [customerId, navigation]);
useEffect(() => {
  const requestLocationPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          getCurrentLocation();
        } else {
          // Don't set location if permission denied - matches Ionic
          console.log('Location permission denied');
        }
      } catch (error) {
        console.error('Error requesting location permission:', error);
      }
    } else {
      getCurrentLocation();
    }
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      async position => {
        const newLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };

        // Validate location before using it (matches Ionic behavior)
        if (isValidLocation(newLocation)) {
          // Store in AsyncStorage like Ionic stores in localStorage
          await AsyncStorage.setItem('GC_FETCH_LOCATION', JSON.stringify({
            latitude: newLocation.lat,
            longitude: newLocation.lng
          }));

          setCustomerLocation(newLocation);

          // Update customer location in DB like Ionic does
          // Only update if user is authenticated and has proper permissions
          if (user?.uid) {
            try {
              await VehicleService.updateCustomerLocation(user.uid, {
                latitude: newLocation.lat,
                longitude: newLocation.lng
              });
            } catch (error: any) {
              // Silently handle permission errors - don't show to user
              if (error?.code !== 'database/permission-denied') {
                console.error('Error updating location in DB:', error);
              }
            }
          }
        } else {
          console.log('Invalid location received, not setting customer location');
          // Load from AsyncStorage if available (like Ionic loads from localStorage)
          loadLocationFromStorage();
        }
      },
      error => {
        console.error('Error getting location:', error);
        // Load from AsyncStorage if available (like Ionic loads from localStorage)
        loadLocationFromStorage();
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  const loadLocationFromStorage = async () => {
    try {
      const storedLocation = await AsyncStorage.getItem('GC_FETCH_LOCATION');
      if (storedLocation) {
        const parsed = JSON.parse(storedLocation);
        const location = {
          lat: parsed.latitude,
          lng: parsed.longitude
        };
        // Validate stored location too
        if (isValidLocation(location)) {
          setCustomerLocation(location);
        } else {
          console.log('Invalid stored location, not setting customer location');
        }
      }
    } catch (error) {
      console.error('Error loading location from storage:', error);
    }
  };

  // Try to load from storage first, then request current location
  loadLocationFromStorage();
  requestLocationPermission();
}, [user?.uid]);
  const renderHeader = () => (
    <View style={styles.mainContentContainer}>
      <View style={styles.enquirySection}>
        {enquiryTab.map(item => (
          <TouchableOpacity
            key={item.key}
            style={styles.enquiryFeatureBox}
            activeOpacity={useModernStyles ? 0.8 : 0.7}
            onPress={() => {
              if (item.key === 'appointments') {
                navigation.navigate(RouteNames.MCX_APPOINTMENTS as never);
              } else if (item.key === 'pendingRequests') {
                const drawerNavigation = navigation.getParent();
                drawerNavigation?.navigate(RouteNames.MCX_NAV_MESSAGES, {
                  initialTab: 3,
                });
              }
            }}>
             <Text style={styles.enquiryCount}>{item.count}</Text>
            <View style={styles.enquiryTextContainer}>
              <Text
                style={styles.enquiryLabel}
                numberOfLines={2}
                adjustsFontSizeToFit>
                {item.label}
              </Text>
            </View>
          </TouchableOpacity>

        ))}
      </View>

      <View style={styles.userFeatureSection}>
        {serviceList.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.userFeatureBox,
              selectedService === item.key && styles.selectedServiceBox,
            ]}
            activeOpacity={useModernStyles ? 0.85 : 0.7}
            onPress={() => setSelectedService(item.key)}>
            {/* <Image source={item.icon} style={styles.featureIcon} /> */}
            <Text
              style={[
                styles.featureLabel,
                selectedService === item.key && styles.selectedFeatureLabel,
              ]}
              numberOfLines={2}>
              {item.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <ImageBackground
      style={styles.container}
      source={AppCommonIcons.MCX_BACKGROUND_IMAGE}
      resizeMode="cover">
      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
      {useModernStyles && (
        <View
          style={{
            ...StyleSheet.absoluteFillObject,
            backgroundColor: modernColors.modernOverlay,
          }}
        />
      )}
      <LoaderOverlay 
        visible={loading || (Object.keys(mechanicList).length === 0 && !isInitialRender)} 
      />
      {Object.keys(mechanicList).length > 0 && (
        <FlatList
          data={filteredMechanics}
          keyExtractor={item => item.id.toString()}
          renderItem={({item, index}) => {
            let availability = 'Open';
            if (item.availability === true) {
              availability = 'Open';
            } else if (item.availability === false) {
              availability = 'Appointment';
            } else if (typeof item.availability === 'string') {
              availability = item.availability;
            } else if (item.availability === undefined) {
              availability = 'Appointment';
            }
            return (
              <>
                <MechanicCard
                  id={item.id}
                  name={
                    item.name ||
                    `${item['first-name'] || ''} ${item['last-name'] || ''}`.trim()
                  }
                  address={
                    item.address ||
                    [item.city, item.state].filter(Boolean).join(', ')
                  }
                  userRating={normalizeMechanicRating(item.userRating || item['mechanic-rating'])}
                  ratingOutOf={5}
                  availability={availability}
                  isFavorite={favoriteMechanic.includes(item.id)}
                  onFavoriteToggle={async (mechanicId) => {
                    try {
                      if (!customerId) {
                        showCustomAlert('Error', 'Unable to update favorite. Please login again.');
                        return;
                      }
                      const db = getDatabase();
                      const favRef = ref(db, `customer/${customerId}/myfavorites/${mechanicId}`);
                      if (favoriteMechanic.includes(mechanicId)) {
                        await remove(favRef);
                        showCustomAlert('Success', 'Removed from favorite.');
                      } else {
                        await set(favRef, true);
                        showCustomAlert('Success', 'Added to favorite.');
                      }
                    } catch (error) {
                      console.error('Error toggling favorite:', error);
                      showCustomAlert('Error', 'Failed to update favorite. Please try again.');
                    }
                  }}
                  showFavoriteIcon={true}
                  cardStyle={styles.mechanicCard}
                  imageUrl={item.imageUrl || item.image}
                  onCardPress={() => {
                    const stackNavigation = navigation.getParent()?.getParent();
                    stackNavigation?.navigate(
                      RouteNames.MCX_NAV_MechanicProfilePage,
                      {
                        mechanic: item,
                        mechanics: filteredMechanics,
                        currentIndex: index,
                      },
                    );
                  }}
                />
                <View style={styles.divider} />
              </>
            );
          }}
          ListHeaderComponent={renderHeader}
          style={styles.mechanicList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Sorry, No mechanics</Text>
              <Text style={styles.emptySubText}>
                Filter depends on your current location. We are working hard to reach mechanic in your location and we are sorry for the inconvenience.
              </Text>
            </View>
          }
        />
      )}
    </ImageBackground>
  );
};
const isSmallDevice = wp(100) < 360;

// Modern styling toggle - set to true for modern styles, false for original
// Change this value to easily switch between modern and original styling
const useModernStyles = true;

// Modern color palette with glassmorphism effects
const modernColors = {
  glassmorphismPrimary: 'rgba(255, 255, 255, 0.1)',
  glassmorphismSecondary: 'rgba(255, 255, 255, 0.15)',
  glassmorphismCard: 'rgba(255, 255, 255, 0.2)',
  modernGradient1: 'rgba(161, 0, 0, 0.8)',
  modernGradient2: 'rgba(161, 0, 0, 0.6)',
  modernShadow: 'rgba(0, 0, 0, 0.15)',
  modernBorder: 'rgba(255, 255, 255, 0.3)',
  modernText: '#ffffff',
  modernTextSecondary: 'rgba(255, 255, 255, 0.9)',
  modernOverlay: 'rgba(0, 0, 0, 0.3)',
  // Mechanic card specific colors (white background for better readability)
  mechanicCardBg: '#ffffff',
  mechanicCardText: '#333333',
  mechanicCardBorder: 'rgba(0, 0, 0, 0.1)',
};

// Modern styles with glassmorphism effects
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContentContainer: {
    flex: 1,
    paddingTop: 16,
  },
  enquirySection: {
    flexDirection: 'row',
    backgroundColor: modernColors.glassmorphismPrimary,
    borderRadius: 16,
    alignContent: 'center',
    padding: 5,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  enquiryFeatureBox: {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  minWidth: wp(42),
  justifyContent: 'flex-start',
  backgroundColor: modernColors.modernGradient1,
  borderRadius: 12,
  minHeight: isSmallDevice ? 70 : 80,
  borderWidth: 1,
  borderColor: modernColors.modernBorder,
  paddingHorizontal: isSmallDevice ? 12 : 16,
  paddingVertical: isSmallDevice ? 12 : 16,
  marginHorizontal: 4,
  shadowColor: modernColors.modernShadow,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 4,
},

enquiryTextContainer: {
  flex: 1,
  flexShrink: 1,        // ✅ allows wrapping when text is long
  marginLeft: isSmallDevice ? 6 : 8,
  justifyContent: 'center',
  alignItems: 'flex-start',
},

enquiryLabel: {
  color: modernColors.modernText,
  fontSize: isSmallDevice ? wp(2.9) : wp(3.2),
  fontWeight: '500',
  textAlign: 'left',
  flexWrap: 'wrap',
  lineHeight: isSmallDevice ? wp(4) : wp(4.4),
  letterSpacing: 0.2,
  includeFontPadding: false,
  textAlignVertical: 'center',
  width: '100%',         // ✅ ensures text uses available space
},
 
  enquiryCount: {
    color: modernColors.modernText,
    fontSize: isSmallDevice ? wp(6) : wp(7),
    fontWeight: '800',
    minWidth: isSmallDevice ? wp(6) : wp(7),
    textAlign: 'center',
    letterSpacing: 0.5,
  },
 
  userFeatureSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between', // Ensures even distribution for 2x2 grid
    backgroundColor: modernColors.glassmorphismPrimary,
    borderRadius: 16,
    padding: 12, // Increased padding for better spacing
    marginBottom: 16,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  userFeatureBox: {
    width: '48%', // Responsive width for 2-column grid (2x2 layout on mobile)
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: isSmallDevice ? wp(22) : wp(24),
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    paddingVertical: isSmallDevice ? wp(3) : wp(4),
    paddingHorizontal: isSmallDevice ? wp(2) : wp(3),
    marginBottom: 8,
    marginHorizontal: '1%', // Responsive horizontal margin
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  featureLabel: {
    color: modernColors.modernText,
    fontSize: isSmallDevice ? wp(3.2) : wp(3.5),
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: isSmallDevice ? wp(4) : wp(4.2),
    letterSpacing: 0.3,
  },
  selectedServiceBox: {
    backgroundColor: modernColors.modernGradient1,
    borderColor: modernColors.modernGradient2,
    borderWidth: 2,
    transform: [{scale: 1.02}],
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 10,
    elevation: 12,
  },
  selectedFeatureLabel: {
    color: modernColors.modernText,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  mechanicList: {
    marginTop: 4,
  },
  mechanicCard: {
    backgroundColor: modernColors.mechanicCardBg,
    borderRadius: 16,
    padding: 20,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: modernColors.mechanicCardBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
  },
  flatListContent: {
    flexGrow: 1,
    paddingBottom: 120, // Increased padding to account for modern bottom bar and ensure last card is visible
    paddingHorizontal: 16,
  },
  divider: {
    height: 1,
    backgroundColor: 'black',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
});

// Original styles (preserved for easy reversion)
const originalStyles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 100, // Add padding to account for bottom bar height
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  mainContentContainer: {
    flex: 1,
    paddingTop: 10,
  },
  enquirySection: {
    flexDirection: 'row',
    backgroundColor: Colors.SECONDARY,
    borderRadius: 2,
    alignContent: 'center',
    padding: 6,
    marginBottom: 8,
  },
  enquiryFeatureBox: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    minHeight: isSmallDevice ? 55 : 65,
    borderWidth: 1,
    borderColor: Colors.COMMON_WHITE_SHADE,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: isSmallDevice ? 6 : 8,
    paddingVertical: isSmallDevice ? 6 : 8,
  },
  enquiryCount: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(7) : wp(8),
    fontWeight: 'bold',
    minWidth: isSmallDevice ? wp(7) : wp(8),
    textAlign: 'center',
  },
  enquiryTextContainer: {
    flex: 1,
    marginLeft: isSmallDevice ? 6 : 8,
    justifyContent: 'center',
  },
  enquiryLabel: {
    color: modernColors.modernText,
    fontSize: isSmallDevice ? wp(2.8) : wp(3.2),
    fontWeight: '600',
    textAlign: 'left',
    flexWrap: 'wrap',
    lineHeight: isSmallDevice ? wp(3.5) : wp(4),
    letterSpacing: 0.2,
    maxWidth: '100%',
  },
  userFeatureSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between', // Even distribution for 2x2 grid
    backgroundColor: Colors.SECONDARY,
    borderRadius: 4,
    padding: 8, // Increased padding for better spacing
    marginBottom: 12,
  },
  userFeatureBox: {
    width: '48%', // Responsive width for 2-column grid
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: isSmallDevice ? wp(20) : wp(22),
    borderWidth: 1,
    borderColor: Colors.SECONDARY,
    paddingVertical: isSmallDevice ? wp(2.5) : wp(3),
    paddingHorizontal: isSmallDevice ? wp(1.5) : wp(2),
    marginBottom: 6,
    marginHorizontal: '1%',
  },
  featureIcon: {
    width: isSmallDevice ? wp(9) : wp(10),
    height: isSmallDevice ? wp(9) : wp(10),
    marginBottom: isSmallDevice ? wp(1.5) : wp(2),
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  featureLabel: {
    color: '#fff',
    fontSize: isSmallDevice ? wp(2.8) : wp(3),
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: isSmallDevice ? wp(3.5) : wp(3.8),
  },
  selectedServiceBox: {
    backgroundColor: 'rgba(234, 14, 14, 0.8)',
    borderColor: '#ea0e0e80',
    borderWidth: 2,
  },
  selectedFeatureLabel: {
    color: '#fff',
    fontWeight: '700',
  },
  mechanicList: {
    marginTop: 2,
  },
  mechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 2,
    padding: 16,
    marginBottom: 2,
  },
  flatListContent: {
    flexGrow: 1,
    paddingBottom: 80, // Increased padding to account for bottom bar
    paddingHorizontal: 12,
  },
  divider: {
    height: 1,
    backgroundColor: 'black',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
});

// Style selection based on toggle
const styles = useModernStyles ? modernStyles : originalStyles;

export default DashBoard;