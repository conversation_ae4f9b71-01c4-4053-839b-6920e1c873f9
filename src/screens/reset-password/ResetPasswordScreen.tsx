import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Colors, Fonts} from '../../utils/constants/Theme';
import {AppStrings} from '../../utils/constants/AppStrings';
import AuthService from '../../services/authService';
import AppBackground from '../../components/ui/AppBackground';
import AppBar from '../../components/common/AppBar';
import LoaderOverlay from '../../components/common/LoaderOverlay';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CustomAlert from '../../components/common/CustomAlert';

const ResetPasswordScreen: React.FC = () => {
  const navigation = useNavigation();
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const validateEmail = (value: string) => /\S+@\S+\.\S+/.test(value);

  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const onBlurEmail = () => {
    if (!email) {
      setEmailError(''); // Clear if empty
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError(''); // Clear when valid
    }
  };

  const onSubmit = async () => {
    if (!email) {
      showCustomAlert(AppStrings.MCX_RESET_PASSWORD, AppStrings.MCX_ENTER_MAIL);
      return;
    }
    if (!validateEmail(email)) {
      setEmailError(AppStrings.MCX_ENTER_VALID_MAIL);
      return;
    }
    setSubmitting(true);
    setIsLoading(true);
    try {
      // 1) Check if email exists in mechanic app
      const mechExists = await AuthService.isEmailExistInMechanic(email);
      if (mechExists) {
        showCustomAlert(
          AppStrings.MCX_RESET_FAILED,
          AppStrings.MCX_ALERADY_REGISTERED_EMAIL,
          [{text: AppStrings.MCX_OK, onPress: () => navigation.goBack()}],
        );
        return;
      }

      // 2) Check if email exists in customer app
      const custExists = await AuthService.isEmailExistInCustomer(email);
      if (!custExists) {
        showCustomAlert(
          AppStrings.MCX_INCORRECT_MAIL,
          AppStrings.MCX_EMAIL_NOT_FOUND,
        );
        return;
      }

      // 3) Send reset password email
      await AuthService.resetPassword(email);
      showCustomAlert(AppStrings.MCX_CHECK_MAIL, AppStrings.MCX_CHECK_INBOX, [
        {text: AppStrings.MCX_OK, onPress: () => navigation.goBack()},
      ]);
      setIsLoading(false);
    } catch (error: any) {
      console.log('error', error.message);
      showCustomAlert(
        AppStrings.MCX_RESET_FAILED,
        error?.message || AppStrings.MCX_WENT_WRONG,
      );
      setIsLoading(false);
      navigation.navigate('AppLoginPageScreen' as never);
    } finally {
      setSubmitting(false);
      setIsLoading(false);
    }
  };
  const handleBackPress = () => {
    navigation.navigate('AppLoginPageScreen' as never);
  };

  return (
    <SafeAreaView style={modernStyles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}>
        <ScrollView
          bounces={false}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: insets.bottom,
          }}>
          <AppBackground />

          <AppBar
            showBackButton={true}
            showMailIcon={false}
            showChatIcon={false}
            showMenuIcon={false}
            showLogo={true}
            onBackPress={handleBackPress}
          />

          <View style={modernStyles.titleWrap}>
            <Text style={modernStyles.title}>
              {AppStrings.MCX_PASSWORD_RESET_TITLE}
            </Text>
          </View>

          {emailError ? (
            <Text style={modernStyles.errorText}>{emailError}</Text>
          ) : null}

          <View style={modernStyles.inputView}>
            <TextInput
              style={[
                modernStyles.input,
                emailError && modernStyles.inputError,
              ]}
              placeholder={AppStrings.MCX_ENTER_EMAIL}
              placeholderTextColor="rgba(255, 255, 255, 0.6)"
              value={email}
              onChangeText={text => {
                setEmail(text);
                if (emailError) setEmailError('');
              }}
              onBlur={onBlurEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TouchableOpacity
              style={[modernStyles.submitBtn, submitting && {opacity: 0.7}]}
              onPress={onSubmit}
              disabled={submitting}>
              <Text style={modernStyles.submitText}>
                {AppStrings.MCX_RESET_YOUR_PASSWORD}
              </Text>
            </TouchableOpacity>
          </View>

          <CustomAlert
            visible={alertVisible}
            title={alertTitle}
            message={alertMessage}
            onDismiss={hideAlert}
            buttons={alertButtons}
          />
        </ScrollView>
      </KeyboardAvoidingView>

      <LoaderOverlay visible={isLoading} />
    </SafeAreaView>
  );
};
export default ResetPasswordScreen;

// Modern themed styles with background image support
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleWrap: {
    backgroundColor: 'rgba(161, 0, 0, 0.95)',
    //borderRadius: 12,
    marginBottom: 24,
    //marginHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#A10000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  title: {
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  input: {
    width: '100%',
    backgroundColor: 'rgba(31, 36, 41, 0.85)',
    borderRadius: 12,
    height: 56,
    paddingHorizontal: 18,
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 20,
    fontSize: 15,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.15)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 3,
  },
  inputError: {
    borderColor: '#EF4444',
    borderWidth: 2,
  },
  submitBtn: {
    width: '100%',
    backgroundColor: '#A10000',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 12,
    shadowColor: '#A10000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
  },
  submitText: {
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  errorText: {
    color: '#FEE2E2',
    backgroundColor: 'rgba(239, 68, 68, 0.9)',
    alignSelf: 'flex-start',
    marginBottom: 12,
    marginLeft: 20,
    marginRight: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    fontSize: 13,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
    overflow: 'hidden',
  },
  inputView: {
    marginLeft: 20,
    marginRight: 20,
  },
});

// Original styles preserved for easy rollback
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleWrap: {
    backgroundColor: '#a10000',
    borderRadius: 2,
    marginBottom: 16,
    paddingVertical: 10,
    alignItems: 'center',
  },
  title: {
    color: '#fff',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 16,
  },
  input: {
    width: '100%',
    backgroundColor: '#1f2429',
    borderRadius: 2,
    height: 48,
    paddingHorizontal: 14,
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 18,
    fontSize: 14,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#2c3238',
  },
  submitBtn: {
    width: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 4.5,
    elevation: 5,
  },
  submitText: {
    color: '#fff',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 14,
  },
  errorText: {
    color: 'red',
    alignSelf: 'flex-start',
    marginBottom: 4,
    marginLeft: 20,
  },
  inputView: {
    marginLeft: 20,
    marginRight: 20,
  },
});
