/**
 * MechanicProfilePage with Modern Styling
 *
 * Features modern glassmorphism design with enhanced visual appeal.
 * Toggle 'useModernStyles' constant to switch between modern and original styling.
 */
import React, {useEffect, useState} from 'react';
import {VehicleService} from '../../../utils/services/VehicleService';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import StarRating from '../../../components/common/StarRating';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Colors} from '../../../utils/constants/Theme';
import {AppCommonIcons, AppStrings} from '../../../utils/constants/AppStrings';
import type {
  MechanicItem,
  RootStackParamList,
} from '../../../utils/configs/types';
import {useAuth} from '../../../utils/configs/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../../../utils/globals';
import {ref, set, remove, onValue} from '@react-native-firebase/database';
import {getDatabase} from '@react-native-firebase/database';
import CustomAlert from '../../../components/common/CustomAlert';

import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';
import {normalizeMechanicRating} from '../../../utils/helpers/RatingUtils';

type ParamList = {
  MechanicProfilePage: {
    mechanic: MechanicItem;
    mechanics?: MechanicItem[];
    currentIndex?: number;
    selectedAvailability?: string;
  };
};

const MechanicProfilePage: React.FC = () => {
  const route = useRoute<RouteProp<ParamList, 'MechanicProfilePage'>>();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {mechanic, mechanics, currentIndex, selectedAvailability} =
    route.params;
    console.log("mechanic:",mechanic)
    console.log("mechanic['mechanic-rating']:", mechanic['mechanic-rating'])
    console.log("mechanic keys:", Object.keys(mechanic || {}))
  const insets = useSafeAreaInsets();

  const getFullName = () => {
    if (mechanic['first-name'] && mechanic['last-name']) {
      return `${mechanic['first-name']} ${mechanic['last-name']}`;
    }
    return mechanic.name || 'Unknown Mechanic';
  };

  const getLocationString = () => {
    const parts = [];
    if (mechanic.address1) parts.push(mechanic.address1);
    if (mechanic.address2) parts.push(mechanic.address2);
    if (mechanic.city) parts.push(mechanic.city);
    if (mechanic.state) parts.push(mechanic.state);
    if (mechanic.country) parts.push(mechanic.country);
    if (mechanic.zipcode) parts.push(mechanic.zipcode);

    if (parts.length > 0) {
      return parts.join(', ');
    }

    if (mechanic.locationDetails && mechanic.locationDetails.length > 0) {
      return mechanic.locationDetails.join(', ');
    }

    return 'Location not available';
  };

  const getCancellationRate = () => {
    const cancelledTotal = mechanic.counts?.['cancelled-total'] ?? 0;
    const appointmentCount = mechanic.counts?.['appointment-count'] ?? 0;

    if (appointmentCount === 0) return '0%';

    const rate = (cancelledTotal / appointmentCount) * 100;
    return `${Math.round(rate)}%`;
  };

  const [serviceIdNameMap, setServiceIdNameMap] = useState<
    Record<string, string>
  >({});
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  const {user} = useAuth();
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      const fetchServiceMap = async () => {
        const map = await VehicleService.getServiceIdNameMap();
        setServiceIdNameMap(map);
      };
      fetchServiceMap();
    };

    checkNetworkAndLoad();
  }, [navigation]);

  // Fetch favorite status
  useEffect(() => {
    const customerId = user?.uid || null;
    const mechanicId = mechanic.id || mechanic.indexKey || null;

    if (!customerId || !mechanicId) {
      return;
    }

    const favRef = VehicleService.fetchUserInformation(customerId).child('myfavorites').child(mechanicId);
    const unsubscribe = onValue(favRef, (snapshot) => {
      const isFav = snapshot.exists() && snapshot.val() === true;
      setIsFavorite(isFav);
    });

    return () => unsubscribe();
  }, [user, mechanic.id, mechanic.indexKey]);

  const goToMechanic = (index: number) => {
    if (!mechanics || index < 0 || index >= mechanics.length) {
      return;
    }
    setIsNavigating(true);
    setTimeout(() => {
      navigation.navigate('MechanicProfilePage', {
        mechanic: mechanics[index],
        mechanics,
        currentIndex: index,
        selectedAvailability,
      });
      setIsNavigating(false);
    }, 500); // Simulate loading time
  };

  /**
   * Toggle favorite mechanic - matches Ionic's toggleFavorite behavior
   */
  const toggleFavorite = async () => {
    try {
      const customerId = user?.uid;
      const mechanicId = mechanic.id || mechanic.indexKey;

      if (!customerId || !mechanicId) {
        showCustomAlert('Error', 'Unable to update favorite. Please try again.');
        return;
      }

      const db = getDatabase();
      const favRef = ref(db, `customer/${customerId}/myfavorites/${mechanicId}`);

      if (isFavorite) {
        // Remove from favorites
        await remove(favRef);
        showCustomAlert('Success', 'Removed from favorite.');
      } else {
        // Add to favorites
        await set(favRef, true);
        showCustomAlert('Success', 'Added to favorite.');
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      showCustomAlert('Error', 'Failed to update favorite. Please try again.');
    }
  };

  const getRating = () => {
    const rating = mechanic['mechanic-rating'] ?? mechanic['rating'] ?? 0;
    return typeof rating === 'number' ? rating : 0;
  };

  return (
    <>
      <View style={[modernStyles.container, {paddingBottom: insets.bottom}]}>
        <StatusBar barStyle="light-content" backgroundColor="#1a2332" />
        {/* Fixed Header */}
        <SafeAreaView
          style={[modernStyles.headerSafeArea, {paddingTop: insets.top}]}>
          <View style={modernStyles.header}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={modernStyles.backButton}>
              <Image
                source={AppCommonIcons.MCX_ARROW_RIGHT}
                style={modernStyles.backIcon}
              />
            </TouchableOpacity>
            <Text style={modernStyles.headerTitle}>
              {AppStrings.MCX_MECHANIC_DETAIL_TITLE}
            </Text>
            <View style={modernStyles.headerSpacer} />
          </View>
        </SafeAreaView>

        {/* Scrollable Content */}
        <ScrollView
          style={modernStyles.scrollContainer}
          bounces={false}
          contentContainerStyle={modernStyles.scrollContent}
          showsVerticalScrollIndicator={false}>
          <View style={modernStyles.section}>
            <Text style={modernStyles.sectionTitle}>MY MECHANIC</Text>
            <View style={modernStyles.mechanicCard}>
              <View style={modernStyles.mechanicHeader}>
                <View style={modernStyles.imageWrapper}>
                  <Image
                    source={
                      mechanic.imageUrl
                        ? {uri: mechanic.imageUrl}
                        : AppCommonIcons.MCX_USER_PROFILE_PIC
                    }
                    style={modernStyles.mechanicImage}
                  />
                  <View style={modernStyles.imageBorder} />
                </View>
                <View style={modernStyles.mechanicInfo}>
                  <View style={modernStyles.nameAndFavoriteRow}>
                    <Text style={modernStyles.mechanicName}>{getFullName()}</Text>
                    <TouchableOpacity
                      style={modernStyles.favoriteButton}
                      onPress={toggleFavorite}
                    >
                      <Icon
                        name={isFavorite ? 'favorite' : 'favorite-border'}
                        size={24}
                        color={isFavorite ? Colors.PRIMARY : Colors.COMMON_GREY_SHADE_LIGHT}
                      />
                    </TouchableOpacity>
                  </View>
                  <View style={modernStyles.ratingContainer}>
                    <View style={modernStyles.ratingBadge}>
                      <StarRating
                        rating={normalizeMechanicRating(getRating())}
                        maxRating={5}
                        size={16}
                        useImages={false}
                        showEmptyStars={true}
                        color="#8f0a19"
                        emptyColor="#e7b7b7ff"
                      />
                      {getRating() > 0 && (
                        <Text style={modernStyles.ratingText}>
                          {normalizeMechanicRating(getRating())}
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
              </View>

              <View style={modernStyles.infoGrid}>
                <View style={modernStyles.infoItem}>
                  <View style={modernStyles.infoIconContainer}>
                    <Text style={modernStyles.infoEmoji}>💼</Text>
                  </View>
                  <Text style={modernStyles.infoLabel}>
                    {AppStrings.MCX_MECHANIC_EXPERIENCE_LABEL}
                  </Text>
                  <Text style={modernStyles.infoValue}>
                    {mechanic.experience || 'N/A'}
                  </Text>
                </View>
                <View style={modernStyles.infoItem}>
                  <View style={modernStyles.infoIconContainer}>
                    <Text style={modernStyles.infoEmoji}>🏆</Text>
                  </View>
                  <Text style={modernStyles.infoLabel}>
                    {AppStrings.MCX_MECHANIC_CERTIFICATIONS_LABEL}
                  </Text>
                  <Text style={modernStyles.infoValue}>
                    {mechanic.certificatesCount ?? mechanic.certifications ?? 0}
                  </Text>
                </View>
                <View style={modernStyles.infoItem}>
                  <View style={modernStyles.infoIconContainer}>
                    <Text style={modernStyles.infoEmoji}>⏰</Text>
                  </View>
                  <Text style={modernStyles.infoLabel}>
                    {AppStrings.MCX_MECHANIC_AVAILABILITY_TITLE}
                  </Text>
                  <View
                    style={[
                      modernStyles.availabilityBadge,
                      {
                        backgroundColor: mechanic.availability
                          ? '#E8F5E9'
                          : '#FFEBEE',
                      },
                    ]}>
                    <View
                      style={[
                        modernStyles.availabilityDot,
                        {
                          backgroundColor: mechanic.availability
                            ? '#4CAF50'
                            : '#f44336',
                        },
                      ]}
                    />
                    <Text
                      style={[
                        modernStyles.availabilityText,
                        {color: mechanic.availability ? '#2E7D32' : '#C62828'},
                      ]}>
                      {mechanic.availability ? 'Open' : 'Appointment'}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={modernStyles.locationBox}>
                <View style={modernStyles.locationHeader}>
                  <Text style={modernStyles.locationIcon}>📍</Text>
                  <Text style={modernStyles.locationTitle}>
                    {AppStrings.MCX_MECHANIC_LOCATION_TITLE}
                  </Text>
                </View>
                <Text style={modernStyles.locationText}>
                  {getLocationString()}
                </Text>
              </View>
            </View>
          </View>

          <View style={modernStyles.statsSection}>
            <Text style={modernStyles.sectionTitle}>
              {AppStrings.MCX_MECHANIC_PERFORMANCE_METRICS_TITLE}
            </Text>
            <View style={modernStyles.statsGrid}>
              <View style={modernStyles.statBox}>
                <View style={modernStyles.statIconContainer}>
                  <Text style={modernStyles.statEmoji}>📊</Text>
                </View>
                <Text style={modernStyles.statValue}>
                  {getCancellationRate()}
                </Text>
                <Text style={modernStyles.statLabel}>
                  {AppStrings.MCX_MECHANIC_CANCELLATION_RATE_LABEL}
                </Text>
              </View>
              <View style={[modernStyles.statBox, modernStyles.statBoxMiddle]}>
                <View style={modernStyles.statIconContainer}>
                  <Text style={modernStyles.statEmoji}>⚡</Text>
                </View>
                <Text style={modernStyles.statValue}>
                  {mechanic.counts?.['rightnow-count'] ??
                    mechanic.byAvailability ??
                    0}
                </Text>
                <Text style={modernStyles.statLabel}>
                  {AppStrings.MCX_MECHANIC_BY_AVAILABILITY_LABEL}
                </Text>
              </View>
              <View style={modernStyles.statBox}>
                <View style={modernStyles.statIconContainer}>
                  <Text style={modernStyles.statEmoji}>📅</Text>
                </View>
                <Text style={modernStyles.statValue}>
                  {mechanic.counts?.['appointment-count'] ??
                    mechanic.byAppointment ??
                    0}
                </Text>
                <Text style={modernStyles.statLabel}>
                  {AppStrings.MCX_MECHANIC_TOTAL_APPOINTMENTS_LABEL}
                </Text>
              </View>
            </View>
          </View>

          <View style={modernStyles.section}>
            <Text style={modernStyles.sectionTitle}>
              {AppStrings.MCX_MECHANIC_SERVICES_TITLE}
            </Text>
            <View style={modernStyles.servicesContainer}>
              {mechanic.services &&
              Object.keys(mechanic.services).length > 0 ? (
                <View style={modernStyles.servicesWrapper}>
                  {Object.keys(mechanic.services).map((key, idx) => (
                    <View key={idx} style={modernStyles.serviceChip}>
                      <Text style={modernStyles.serviceIcon}>🔧</Text>
                      <Text style={modernStyles.serviceText}>
                        {serviceIdNameMap[key] ||
                          mechanic.services![key]['service-type'] ||
                          key}
                      </Text>
                    </View>
                  ))}
                </View>
              ) : (
                <View style={modernStyles.noDataContainer}>
                  <Text style={modernStyles.noDataIcon}>🔧</Text>
                  <Text style={modernStyles.noDataText}>
                    {AppStrings.MCX_MECHANIC_NO_SERVICES_TEXT}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* <View style={modernStyles.section}>
          <Text style={modernStyles.sectionTitle}>
            {AppStrings.MCX_MECHANIC_PERSONAL_INFO_TITLE}
          </Text>
          <View style={modernStyles.infoCard}>
            {mechanic.gender && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>👤</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_GENDER_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>{mechanic.gender}</Text>
              </View>
            )}
            {mechanic.dateofbirth && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>🎂</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_DOB_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>
                  {mechanic.dateofbirth}
                </Text>
              </View>
            )}
            {mechanic.email && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>✉️</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_EMAIL_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>{mechanic.email}</Text>
              </View>
            )}
            {mechanic.mobile && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>📱</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_MOBILE_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>{mechanic.mobile}</Text>
              </View>
            )}
          </View>
        </View> */}

          {/* <View style={modernStyles.section}>
          <Text style={modernStyles.sectionTitle}>
            {AppStrings.MCX_MECHANIC_PROFESSIONAL_DETAILS_TITLE}
          </Text>
          <View style={modernStyles.infoCard}>
            {mechanic['provider-type'] && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>🏢</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_PROVIDER_TYPE_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>
                  {mechanic['provider-type']}
                </Text>
              </View>
            )}
            {mechanic['registration-status'] && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>📋</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_REGISTRATION_STATUS_LABEL}
                  </Text>
                </View>
                <Text style={modernStyles.infoRowValue}>
                  {mechanic['registration-status']}
                </Text>
              </View>
            )}
            {mechanic['login-status'] !== undefined && (
              <View style={modernStyles.infoRow}>
                <View style={modernStyles.infoRowLeft}>
                  <Text style={modernStyles.infoRowIcon}>🟢</Text>
                  <Text style={modernStyles.infoRowLabel}>
                    {AppStrings.MCX_MECHANIC_STATUS_LABEL}
                  </Text>
                </View>
                <View
                  style={[
                    modernStyles.statusBadge,
                    {
                      backgroundColor: mechanic['login-status']
                        ? '#E8F5E9'
                        : '#FFEBEE',
                    },
                  ]}>
                  <Text
                    style={[
                      modernStyles.statusText,
                      {color: mechanic['login-status'] ? '#2E7D32' : '#C62828'},
                    ]}>
                    {mechanic['login-status'] ? 'Online' : 'Offline'}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View> */}

          <View style={modernStyles.section}>
            <Text style={modernStyles.sectionTitle}>REVIEWS</Text>
            <View style={modernStyles.reviewsContainer}>
              <Image
                source={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
                style={modernStyles.noDataImage}
              />
              <Text style={modernStyles.noDataText}>SORRY NO DATA FOUND</Text>
            </View>
          </View>
          <View style={modernStyles.footer}>
            <TouchableOpacity
              style={modernStyles.bookButton}
              onPress={() =>
                navigation.navigate('BookAppointmentScreen', {
                  mechanic,
                  selectedAvailability,
                })
              }>
              <Text style={modernStyles.bookButtonText}>BOOK APPOINTMENT</Text>
            </TouchableOpacity>

            {mechanics && mechanics.length > 1 && (
              <View style={modernStyles.navigationButtons}>
                <TouchableOpacity
                  style={[
                    modernStyles.navButton,
                    (!mechanics || currentIndex === 0) &&
                      modernStyles.navButtonDisabled,
                  ]}
                  disabled={!mechanics || currentIndex === 0}
                  onPress={() => goToMechanic((currentIndex ?? 0) - 1)}>
                  <Text style={modernStyles.navButtonText}>← PREVIOUS</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    modernStyles.navButton,
                    (!mechanics || currentIndex === mechanics.length - 1) &&
                      modernStyles.navButtonDisabled,
                  ]}
                  disabled={!mechanics || currentIndex === mechanics.length - 1}
                  onPress={() => goToMechanic((currentIndex ?? 0) + 1)}>
                  <Text style={modernStyles.navButtonText}>NEXT →</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Fixed Footer */}
      </View>
      <LoaderOverlay visible={isNavigating} />
      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </>
  );
};

// Modern Styles - New stylesheet for easy reverting
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: Colors.BACKGROUND,
  },

  // Header Styles
  headerSafeArea: {
    backgroundColor: '#1f2a3a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2a3a',
    paddingVertical: 18,
    paddingHorizontal: 16,
    shadowRadius: 8,
  },
  backButton: {
    padding: 8,
    borderRadius: 12,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
    transform: [{rotate: '180deg'}],
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },
  headerSpacer: {
    width: 40,
  },

  // Scroll Container
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 24,
    backgroundColor: '#1a2332',
  },

  // Section Styles
  section: {
    marginBottom: 28,
  },
  sectionTitle: {
    color: '#ffff',
    fontWeight: '700',
    fontSize: 15,
    marginBottom: 16,
    letterSpacing: 1.2,
    textTransform: 'uppercase',
  },

  // Mechanic Card Styles
  mechanicCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 24,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.12,
    shadowRadius: 12,
  },
  mechanicHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 20,
  },
  mechanicImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: Colors.PRIMARY,
  },
  imageBorder: {
    position: 'absolute',
    width: 88,
    height: 88,
    borderRadius: 44,
    borderWidth: 2,
    borderColor: Colors.PRIMARY + '30',
    top: -4,
    left: -4,
  },
  mechanicInfo: {
    flex: 1,
  },
  nameAndFavoriteRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  mechanicName: {
    flex: 1,
    fontWeight: '700',
    fontSize: 22,
    color: Colors.TEXT_COLOR,
    letterSpacing: 0.3,
  },
  favoriteButton: {
    padding: 8,
    marginLeft: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FFB74D',
  },
  ratingText: {
    fontSize: 15,
    color: '#E65100',
    fontWeight: '700',
    marginRight: 4,
  },
  starIcon: {
    fontSize: 16,
  },

  // Info Grid
  infoGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#FAFAFA',
    paddingVertical: 20,
    paddingHorizontal: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  infoIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.PRIMARY + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  infoEmoji: {
    fontSize: 22,
  },
  infoLabel: {
    fontSize: 9,
    color: Colors.PRIMARY_DARK,
    fontWeight: '400',
    marginBottom: 6,
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontWeight: '700',
    fontSize: 12,
    color: Colors.TEXT_COLOR,
    textAlign: 'center',
  },
  availabilityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  availabilityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  availabilityText: {
    fontSize: 8,
    fontWeight: '400',
  },

  // Location Box
  locationBox: {
    backgroundColor: '#FFF5F5',
    padding: 18,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  locationIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  locationTitle: {
    fontSize: 13,
    color: Colors.PRIMARY_DARK,
    fontWeight: '700',
    textTransform: 'uppercase',
    letterSpacing: 0.8,
  },
  locationText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    lineHeight: 22,
    paddingLeft: 26,
  },

  // Stats Section
  statsSection: {
    marginBottom: 28,
  },
  statsGrid: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statBox: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  statBoxMiddle: {
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#F0F0F0',
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.PRIMARY + '10',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statEmoji: {
    fontSize: 24,
  },
  statValue: {
    fontWeight: '400',
    fontSize: 14,
    color: Colors.PRIMARY,
    marginBottom: 6,
  },
  statLabel: {
    fontSize: 11,
    color: Colors.PRIMARY_DARK,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 16,
  },

  // Services
  servicesContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  servicesWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  serviceChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY + '08',
    borderColor: Colors.PRIMARY + '30',
    borderWidth: 1.5,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  serviceIcon: {
    fontSize: 14,
    marginRight: 8,
  },
  serviceText: {
    fontSize: 13,
    color: Colors.PRIMARY,
    fontWeight: '600',
  },

  // Info Card
  infoCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoRowIcon: {
    fontSize: 18,
    marginRight: 12,
  },
  infoRowLabel: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    flex: 1,
  },
  infoRowValue: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    fontWeight: '600',
    flex: 1.2,
    textAlign: 'right',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 13,
    fontWeight: '700',
  },

  // Reviews
  reviewsContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  noDataIcon: {
    fontSize: 56,
    marginBottom: 16,
    opacity: 0.5,
  },
  noDataImage: {
    width: 80,
    height: 80,
    marginBottom: 16,
    opacity: 0.4,
  },
  noDataText: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  // Footer Styles
  footer: {
    backgroundColor: 'transparent',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  bookButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 18,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 12,
    shadowRadius: 8,
  },
  bookButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: '700',
    fontSize: 16,
    letterSpacing: 1.2,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  navButton: {
    flex: 1,
    backgroundColor: Colors.COMMON_GREY_SHADE_DARK,
    paddingVertical: 14,
    borderRadius: 14,
    alignItems: 'center',
    shadowRadius: 4,
  },
  navButtonDisabled: {
    opacity: 0.4,
    backgroundColor: '#E0E0E0',
    elevation: 0,
  },
  navButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: '700',
    fontSize: 14,
    letterSpacing: 0.8,
  },
});

export default MechanicProfilePage;
