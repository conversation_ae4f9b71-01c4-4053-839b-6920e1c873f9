import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Share,
  Clipboard,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomButton from '../../../components/common/CustomButton';
import CustomAlert from '../../../components/common/CustomAlert';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../../utils/constants/AppStrings';

// Firebase imports (adjust based on your Firebase setup)
import auth from '@react-native-firebase/auth';
import { getDatabase, ref, set, get } from '@react-native-firebase/database';

// Branch SDK import (install: npm install react-native-branch)
import branch from 'react-native-branch';

const ReferFriend = () => {
  const navigation = useNavigation();
  
  // State management
  const [inviteCode, setInviteCode] = useState<string>('loading...');
  const [earnedDollar, setEarnedDollar] = useState<string>('0');
  const [loading, setLoading] = useState<boolean>(true);
  const [customerId, setCustomerId] = useState<string>('');

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    // Get customer ID and load data
    const loadCustomerData = async () => {
      try {
        const user = auth().currentUser;
        if (user) {
          setCustomerId(user.uid);
          await Promise.all([
            loadInviteCode(user.uid),
            loadCredit()
          ]);
        }
      } catch (error) {
        console.error('Error loading customer data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCustomerData();
  }, []);

  /**
   * Load invite code from Firebase or create new deep link
   */
  const loadInviteCode = async (userId: string) => {
    try {
      const db = getDatabase();
      const inviteCodeRef = ref(db, `customer/${userId}/invite-code`);

      const snapshot = await get(inviteCodeRef);

      if (snapshot.exists()) {
        setInviteCode(snapshot.val());
      } else {
        console.log('No invite code found, creating deep link...');
        await createDeepLink(userId);
      }
    } catch (error) {
      console.error('Error loading invite code:', error);
      setInviteCode('Error loading invite code');
    }
  };

  /**
   * Load earned credits from Branch
   */
  const loadCredit = async () => {
    try {
      // For now, set a default value since Branch rewards might not be available
      setEarnedDollar('0');
      console.log('Credits feature disabled - using default value');
    } catch (error) {
      console.error('Error loading credits:', error);
      setEarnedDollar('0');
    }
  };

  /**
   * Create Branch deep link
   */
  const createDeepLink = async (userId: string) => {
    try {
      // Branch Universal Object properties
      const branchUniversalObject = await branch.createBranchUniversalObject(
        'mycanx',
        {
          canonicalUrl: 'https://example.com/content/123',
          title: 'myCANx',
          contentDescription: 'Peer-to-peer marketplace for on-demand mobile mechanics and auto technicians',
          contentImageUrl: 'https://firebasestorage.googleapis.com/v0/b/mycanx-cf697.appspot.com/o/mycanx-branch.io%2Fmycanx_logo400x400.png?alt=media&token=394b1f53-7818-4fb4-b04a-710b8863e45f',
          contentMetadata: {
            customMetadata: {
              custom: 'data',
              testing: '123',
              this_is: 'true',
            },
          },
        }
      );

      // Link properties
      const linkProperties = {
        feature: 'onboarding',
        channel: 'facebook',
        campaign: 'myCANx launch',
        stage: 'new user',
        tags: ['one', 'two', 'three'],
      };

      // Control parameters - simplified for compatibility
      const controlParams = {};

      // Generate short URL
      const { url } = await branchUniversalObject.generateShortUrl(
        linkProperties,
        controlParams
      );

      console.log('Generated URL:', url);
      setInviteCode(url);
      
      // Save invite code to Firebase
      await updateInviteCode(userId, url);
      
    } catch (error) {
      console.error('Error creating deep link:', error);
      setInviteCode('Error creating invite link');
    }
  };

  /**
   * Update invite code in Firebase
   */
  const updateInviteCode = async (userId: string, inviteUrl: string) => {
    try {
      const db = getDatabase();
      const inviteCodeRef = ref(db, `customer/${userId}/invite-code`);
      await set(inviteCodeRef, inviteUrl);
      console.log('Invite code saved to Firebase');
    } catch (error) {
      console.error('Error updating invite code:', error);
    }
  };

  /**
   * Handle share invite code
   */
  const handleInviteFriend = async () => {
    if (inviteCode === 'loading...' || inviteCode.includes('Error')) {
      showCustomAlert('Error', 'Please wait while we load your invite link');
      return;
    }

    try {
      const message = `myCANx is a service provider for your vehicle at your place. Join through following link: ${inviteCode}`;
      
      const result = await Share.share({
        message: message,
        title: 'Invitation from myCANx',
        url: inviteCode, // iOS only
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Invitation sent');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      console.error('Error sharing invite:', error);
      showCustomAlert('Error', 'Failed to share invite link');
    }
  };

  /**
   * Handle copy link to clipboard
   */
  const handleCopyLink = () => {
    if (inviteCode === 'loading...' || inviteCode.includes('Error')) {
      showCustomAlert('Error', 'Please wait while we load your invite link');
      return;
    }

    Clipboard.setString(inviteCode);
    showCustomAlert('Success', 'Referral link has been copied to clipboard');
  };

  /**
   * Track Branch event (optional - for analytics)
   */
  const triggerEvent = async () => {
    try {
      // Branch event tracking - simplified for compatibility
      console.log('Referral share event tracked for customer:', customerId);
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.mainContainer} edges={[ 'left', 'right']}>
        <TitleSection
          title={AppStrings.MCX_REFER_FRIEND_TITLE}
          bgColor={Colors.PRIMARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          style={styles.titleSection}
          onBack={() => (navigation as any).goBack()}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.loadingText}>Loading referral information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.mainContainer} edges={[ 'left', 'right']}>
      <TitleSection
        title={AppStrings.MCX_REFER_FRIEND_TITLE}
        bgColor={Colors.PRIMARY}
        textColor={Colors.COMMON_WHITE_SHADE}
        style={styles.titleSection}
        onBack={() => (navigation as any).goBack()}
      />
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
        useHorizontalPadding={true}
      >
        <View style={styles.whiteContainer}>
          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* Refer a Friend Image */}
            <View style={styles.imageContainer}>
              <Image
                source={AppCommonIcons.MCX_REFER_FRIEND_ICON}
                style={styles.referImage}
                resizeMode="contain"
              />
            </View>

            {/* Earned Section */}
            <View style={styles.earnedSection}>
              <Text style={styles.earnedLabel}>
                {AppStrings.MCX_REFER_FRIEND_EARNED_TEXT}
              </Text>
              <Text style={styles.earnedAmount}>
                ${earnedDollar}
              </Text>
            </View>

            {/* Description */}
            <Text style={styles.subtitle}>
              {AppStrings.MCX_REFER_FRIEND_SUBTITLE}
            </Text>

            <Text style={styles.description}>
              {AppStrings.MCX_REFER_FRIEND_DESCRIPTION}
            </Text>

            {/* Referral Link */}
            <TouchableOpacity 
              style={styles.linkContainer} 
              onPress={handleCopyLink}
              disabled={inviteCode === 'loading...'}
            >
              <Text 
                style={styles.referralLink}
                numberOfLines={1}
                ellipsizeMode="middle"
              >
                {inviteCode === 'loading...' 
                  ? 'Generating your referral link...'
                  : inviteCode}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Fixed Bottom Button */}
          <View style={styles.buttonContainer}>
            <CustomButton
              text={AppStrings.MCX_REFER_FRIEND_INVITE_BUTTON}
              onPress={handleInviteFriend}
              variant="primary"
              size="medium"
              fullWidth={false}
              isBoldText={true}
              style={styles.inviteButton}
              textStyle={styles.inviteButtonText}
              disabled={inviteCode === 'loading...'}
            />
          </View>
        </View>
      </ScreenLayout>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  loadingText: {
    marginTop: 16,
    fontSize: Sizes.MEDIUM,
    color: Colors.SECONDARY,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 20,
  },
  mainContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 30,
  },
  imageContainer: {
    alignItems: 'center',
    width: '100%',
  },
  referImage: {
    width: '90%',
    height: 250,
  },
  earnedSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  earnedLabel: {
    fontSize: Sizes.XXLARGE,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
  },
  earnedAmount: {
    fontSize: Sizes.XXXLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.PRIMARY,
    fontWeight: '700',
    paddingLeft: 4,
  },
  subtitle: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    textAlign: 'center',
    marginBottom: 12,
    fontWeight: '800',
    fontStyle: 'italic',
  },
  description: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  linkContainer: {
    backgroundColor: '#F0F0F0',
    paddingVertical: 14,
    paddingHorizontal: 16,
    marginBottom: 20,
    marginTop: 10,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  referralLink: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: '#007AFF',
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    paddingBottom: 30,
    paddingTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inviteButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 40,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignSelf: 'center',
    minWidth: 300,
    maxWidth: 450,
  },
  inviteButtonText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '700',
  },
});

export default ReferFriend;