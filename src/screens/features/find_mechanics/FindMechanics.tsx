import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Geolocation from 'react-native-geolocation-service';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CommonDropdown from '../../../components/common/CommonDropdown';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import CustomAlert from '../../../components/common/CustomAlert';
import MechanicCard from '../../../components/cardstyles/MechanicCard';
import { fmAvailabilityOptions } from '../../../utils/templates/TemplateConfig';
import { VehicleService } from '../../../utils/services/VehicleService';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';
import {
  calculateDistance,
  GC_NEAR_MILES,
} from '../../../utils/helpers/DistanceUtils';
import {normalizeMechanicRating} from '../../../utils/helpers/RatingUtils';
import {useAuth} from '../../../utils/configs/AuthContext';
import {ref, set, remove, onValue} from '@react-native-firebase/database';
import {getDatabase} from '@react-native-firebase/database';
import NetInfo from '@react-native-community/netinfo';

interface MechanicItem {
  id: string;
  [key: string]: any;
}

const FindMechanic = () => {
  const navigation = useNavigation();
  const {user} = useAuth();
  const [searchText, setSearchText] = useState<string>('');
  const [selectedServiceType, setSelectedServiceType] = useState<string | null>(
    null,
  );
  const [selectedAvailability, setSelectedAvailability] = useState<
    string | null
  >(null);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [mechanics, setMechanics] = useState<MechanicItem[]>([]);
  const [serviceMap, setServiceMap] = useState<Record<string, string>>({});
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const serviceTypeDropdownData = useMemo(() => {
    return Object.keys(serviceMap).map(key => ({
      label: serviceMap[key],
      value: key,
    }));
  }, [serviceMap]);

  const availabilityDropdownData = fmAvailabilityOptions;

  const fetchMechanicsData = useCallback(async () => {
    try {
      setLoading(true);
      const mechanicsSnapshot = await VehicleService.getLoggedMechanics().once(
        'value',
      );
      const mechanicsData = mechanicsSnapshot.val() || {};
      const rawMechanics = Object.keys(mechanicsData).map(key => ({
        id: key,
        ...mechanicsData[key],
      }));
      setMechanics(rawMechanics);
      const _serviceMap = await VehicleService.getServiceIdNameMap();
      setServiceMap(_serviceMap);
    } catch (error) {
      console.error('Error fetching mechanics data:', error);
      showCustomAlert('Error', 'Failed to load mechanics data');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch favorites from Firebase
  useEffect(() => {
    if (!user?.uid) {
      return;
    }

    const favRef = VehicleService.fetchUserInformation(user.uid).child('myfavorites');
    const unsubscribe = onValue(favRef, (snapshot) => {
      const favs = snapshot.val() || {};
      setFavorites(new Set(Object.keys(favs)));
    });

    return () => unsubscribe();
  }, [user]);

  useFocusEffect(
    React.useCallback(() => {
      // Network check - similar to Ionic's ionViewCanEnter
      const checkNetworkAndLoad = async () => {
        const state = await NetInfo.fetch();
        if (!state.isConnected) {
          console.log("Network check failed: ", state.isConnected);
          navigation.navigate('NetworkFailedPage' as never);
          return;
        }

        // Reset filters when screen comes into focus
        setSearchText('');
        setSelectedServiceType(null);
        setSelectedAvailability(null);
        // Don't reset favorites - they're loaded from Firebase

        fetchMechanicsData();
      };

      checkNetworkAndLoad();
    }, [fetchMechanicsData, navigation])
  );

  useEffect(() => {
    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          getCurrentLocation();
        }
      } else {
        getCurrentLocation();
      }
    };

    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        position => {
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        error => {
          console.error('Error getting location:', error);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    requestLocationPermission();
  }, []);

  const filteredMechanics = (mechanics || []).filter(mechanic => {
    if (mechanic['account-disabled'] === true) {
      return false;
    }
    const firstName = mechanic['first-name'] || '';
    const lastName = mechanic['last-name'] || '';
    const name = `${firstName} ${lastName}`.trim();
    const address = mechanic.address2 || '';
    const matchesSearch =
      name.toLowerCase().includes(searchText.toLowerCase()) ||
      address.toLowerCase().includes(searchText.toLowerCase());
    let matchesAvailability = true;
    if (selectedAvailability === 'Near Me') {
      if (
        currentLocation &&
        mechanic.location &&
        mechanic.location.latitude &&
        mechanic.location.longitude
      ) {
        const distance = calculateDistance(
          currentLocation.latitude,
          currentLocation.longitude,
          mechanic.location.latitude,
          mechanic.location.longitude,
        );
        matchesAvailability =
          typeof distance === 'number' && distance < GC_NEAR_MILES;
      } else {
        matchesAvailability = false;
      }
    } else if (selectedAvailability === 'Open') {
      matchesAvailability = !!mechanic.availability;
    } else {
      matchesAvailability =
        // !selectedAvailability || mechanic.availability === selectedAvailability;
        matchesAvailability = true;
    }
    const services = mechanic.services || {};
    const matchesServiceType =
      !selectedServiceType || services[selectedServiceType];
    return (
      matchesSearch && matchesAvailability && matchesServiceType && address
    );
  });

  /**
   * Toggle favorite mechanic - matches Ionic's toggleFavorite behavior
   * Persists to Firebase at customer/{customerId}/myfavorites/{mechanicId}
   */
  const toggleFavorite = async (mechanicId: string) => {
    try {
      const customerId = user?.uid;
      if (!customerId) {
        showCustomAlert('Error', 'Unable to update favorite. Please login again.');
        return;
      }

      const db = getDatabase();
      const favRef = ref(db, `customer/${customerId}/myfavorites/${mechanicId}`);

      if (favorites.has(mechanicId)) {
        // Remove from favorites
        await remove(favRef);
        showCustomAlert('Success', 'Removed from favorite.');
      } else {
        // Add to favorites
        await set(favRef, true);
        showCustomAlert('Success', 'Added to favorite.');
      }
      // State will update automatically via Firebase listener
    } catch (error) {
      console.error('Error toggling favorite:', error);
      showCustomAlert('Error', 'Failed to update favorite. Please try again.');
    }
  };

  const renderMechanicCard = ({ item }: { item: MechanicItem }) => {
    const firstName = item['first-name'] || '';
    const lastName = item['last-name'] || '';
    const name = `${firstName} ${lastName}`.trim() || 'Unknown Mechanic';
    const address =
      item.address2.length > 0 ? item.address2 : 'Location not available';
    // Default to 5 if rating is 0, undefined, or null (matching Ionic behavior)
    const userRating = normalizeMechanicRating(item['mechanic-rating']);
    const ratingOutOf = item['rating-out-of'] || 5;
    let availability = 'Open';
    if (item.availability === true) {
      availability = 'Open';
    } else if (item.availability === false) {
      availability = 'Appointment';
    } else if (typeof item.availability === 'string') {
      availability = item.availability;
    } else if (item.availability === undefined) {
      availability = 'Appointment';
    }
    const experience = item.experience || '';
    const gender = item.gender || '';
    const email = item.email || '';
    const mobile = item.mobile || '';
    const imageUrl = item.imageUrl || '';
    const dateOfBirth = item.dateofbirth || '';
    const country = item.country || '';

    const handleCardPress = () => {
      const stackNavigation = navigation.getParent();
      console.log("mechanic param:",filteredMechanics)
      console.log("item being passed:", item);
      console.log("item['mechanic-rating']:", item['mechanic-rating']);
      // Ensure all properties are preserved when passing
      // Default to 5 if rating is 0, undefined, or null (matching Ionic behavior)
      const mechanicToPass = {
        ...item,
        'mechanic-rating': normalizeMechanicRating(item['mechanic-rating']),
      };
      stackNavigation?.navigate('MechanicProfilePage', {
        mechanic: mechanicToPass,
        mechanics: filteredMechanics,
        currentIndex: filteredMechanics.findIndex(m => m.id === item.id),
        selectedAvailability,
      });
    };

    return (
      <MechanicCard
        id={parseInt(item.id, 10) || 0}
        name={name}
        address={address}
        userRating={userRating}
        ratingOutOf={ratingOutOf}
        availability={availability}
        isFavorite={favorites.has(item.id)}
        onFavoriteToggle={() => toggleFavorite(item.id)}
        showFavoriteIcon={true}
        cardStyle={modernStyles.findMechanicCard}
        experience={experience}
        gender={gender}
        email={email}
        mobile={mobile}
        imageUrl={imageUrl}
        dateOfBirth={dateOfBirth}
        country={country}
        onCardPress={handleCardPress}
      />
    );
  };

  return (
    <ScreenLayout
      useScrollView={false}
      useImageBackground={true}
      centerContent={false}
      useHorizontalPadding={true}
    >
      <View style={modernStyles.container}>
        <View style={modernStyles.tableContainer}>
          <View style={modernStyles.searchRow}>
            <CommonTextInput
              value={searchText}
              onChangeText={setSearchText}
              placeholder={AppStrings.MCX_SEARCH_NEARBY_TEXT}
              backgroundColor={'transparent'}
              placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
            />
          </View>
          <View style={modernStyles.filtersRow}>
            <View style={modernStyles.filterCell}>
              <Text style={modernStyles.filterLabel}>
                {AppStrings.MCX_SERVICE_TYPE_KEYWORD}
              </Text>
              <CommonDropdown
                data={serviceTypeDropdownData}
                value={selectedServiceType}
                onValueChange={setSelectedServiceType}
                placeholder={AppStrings.MCX_SERVICE_TYPE_TEXT}
                style={modernStyles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
            <View style={modernStyles.filterCellLast}>
              <Text style={modernStyles.filterLabel}>
                {AppStrings.MCX_MECHANIC_AVAILABILITY_TEXT}
              </Text>
              <CommonDropdown
                data={availabilityDropdownData}
                value={selectedAvailability}
                onValueChange={setSelectedAvailability}
                placeholder={AppStrings.MCX_SELECT_AVAILABILITY_TEXT}
                style={modernStyles.dropdownStyle}
                placeholderTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                selectedTextColor={Colors.COMMON_COMPONENT_TEXT_COLOR}
                fontWeight="800"
              />
            </View>
          </View>
        </View>
        <LoaderOverlay visible={loading} />
        {!loading && (
          <FlatList
            data={filteredMechanics}
            keyExtractor={(item: MechanicItem) => item.id}
            renderItem={renderMechanicCard}
            style={modernStyles.mechanicsList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={modernStyles.listContent}
            ListEmptyComponent={
              <View style={modernStyles.emptyContainer}>
                <View style={modernStyles.emptyStateCard}>
                  <Text style={modernStyles.emptyIcon}>🔍</Text>
                  <Text style={modernStyles.emptyText}>
                    {AppStrings.MCX_NO_MECHANICS_FOUND}
                  </Text>
                  <Text style={modernStyles.emptySubText}>
                    Try adjusting your search filters or location
                  </Text>
                </View>
              </View>
            }
          />
        )}

        <CustomAlert
          visible={alertVisible}
          title={alertTitle}
          message={alertMessage}
          onDismiss={hideAlert}
          buttons={alertButtons}
        />
      </View>
    </ScreenLayout>
  );
};

// Modern Styles - Enhanced styling for better visual appeal
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingBottom: 100, // Add padding to account for bottom bar height
  },

  // Search and Filter Section
  tableContainer: {
    marginTop: 20,
    marginHorizontal: 0,
    marginBottom: 16,
  },
  searchRow: {
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 4,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  filtersRow: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingBottom: 8,
    justifyContent: 'space-between',
    gap: 12,
  },
  filterCell: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  filterCellLast: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  filterLabel: {
    fontSize: 12,
    paddingVertical: 4,
    paddingBottom: 8,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.PRIMARY_DARK,
    textTransform: 'uppercase',
    fontWeight: '700',
    letterSpacing: 0.8,
  },
  dropdownStyle: {
    marginLeft: 0,
    marginRight: 0,
    marginBottom: 0,
    backgroundColor: 'transparent',
    borderRadius: 12,
  },

  // Mechanics List
  mechanicsList: {
    flex: 1,
    paddingTop: 8,
  },
  listContent: {
    paddingBottom: 24,
    paddingHorizontal: 4,
  },
  findMechanicCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 20,
    opacity: 0.6,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    fontWeight: '600',
    color: Colors.PRIMARY_DARK,
    lineHeight: 24,
    letterSpacing: 0.3,
  },
  emptySubText: {
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    color: Colors.TEXT_COLOR,
    marginTop: 8,
    opacity: 0.7,
    lineHeight: 20,
  },
});

// Keep original styles for backward compatibility
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tableContainer: {
    marginTop: 16,
    marginHorizontal: 0,
  },
  searchRow: {
    marginBottom: 16,
  },
  filtersRow: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingBottom: 20,
    justifyContent: 'space-between',
  },
  filterCell: {
    flex: 1,
    paddingRight: 10,
  },
  filterCellLast: {
    flex: 1,
  },
  filterLabel: {
    fontSize: Sizes.SMALL,
    paddingVertical: 5,
    fontFamily: Fonts.ROBO_REGULAR,
    color: '#FFF',
    textTransform: 'uppercase',
  },
  dropdownStyle: {
    marginLeft: 0,
    marginRight: 0,
    marginBottom: 0,
    backgroundColor: 'transparent',
  },
  mechanicsList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  findMechanicCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    fontWeight: '700',
  },
});

export default FindMechanic;
