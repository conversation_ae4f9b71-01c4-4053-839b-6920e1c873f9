import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useNavigation, useRoute} from '@react-navigation/native';
import {RouteNames} from '../../../../utils/constants/AppStrings';
import AppBackground from '../../../../components/ui/AppBackground';
import CustomAlert from '../../../../components/common/CustomAlert';
import StarRating from '../../../../components/common/StarRating';
import {TimelineService} from '../../../../utils/services/TimelineService';
import LoaderOverlay from '../../../../components/common/LoaderOverlay';
import {Colors} from '../../../../utils/constants/Theme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../../../../utils/globals';
import {BackHandler} from 'react-native';
const MessageDetails = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {
    'message-array': messageArray,
    position: position,
    initialTab: initialTab,
  } = route.params as any;
  const message = messageArray[position];

  const [timeline, setTimeline] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [mechanicDetail, setMechanicDetail] = useState<any>(null);
  const [imageLoadingStates, setImageLoadingStates] = useState<{ [imageId: string]: boolean }>({});

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };
  const handleImageLoad = useCallback((imageId: string) => {
    setImageLoadingStates(prev => ({
      ...prev,
      [imageId]: false,
    }));
  }, []);
  const handleImageError = useCallback((imageId: string) => {
    setImageLoadingStates(prev => ({
      ...prev,
      [imageId]: false,
    }));
  }, []);

  useEffect(() => {
    const loadTimelineData = async () => {
      if (message?.['work-request-id']) {
        setLoading(true);
        try {
          const timelineData = await TimelineService.loadTimeline(
            message['work-request-id'],
          );
          const reversedTimelineData = timelineData.reverse();
          setTimeline(reversedTimelineData);
          const imageStates: { [imageId: string]: boolean } = {};
          reversedTimelineData.forEach(item => {
            if (item.images && Array.isArray(item.images)) {
              item.images.forEach((img: any) => {
                if (img.id) {
                  imageStates[img.id] = true; // Start as loading
                }
              });
            }
          });
          setImageLoadingStates(imageStates);

          let mechanic = null;
          if (
            message.details?.mechanic &&
            Object.keys(message.details.mechanic).length > 0
          ) {
            mechanic = message.details.mechanic;
          } else if (
            reversedTimelineData.length > 0 &&
            reversedTimelineData[0].mechanic &&
            Object.keys(reversedTimelineData[0].mechanic).length > 0
          ) {
            mechanic = reversedTimelineData[0].mechanic;
          } else if (
            reversedTimelineData.length > 0 &&
            reversedTimelineData[0].mechanic
          ) {
            mechanic = reversedTimelineData[0].mechanic;
          } else if (message.details?.mechanic) {
            mechanic = message.details.mechanic;
          } else {
            console.log('No mechanic data found');
            mechanic = null;
          }
          setMechanicDetail(mechanic);
        } catch (error) {
          console.error('Error loading timeline:', error);
          setMechanicDetail(null);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };
    loadTimelineData();
  }, [message]);

  const handleBackNavigation = useCallback(() => {
    const currentState = navigation.getState();
    const currentRoute = currentState?.routes?.[currentState?.index];
    const route = navigation
      .getState()
      ?.routes?.find((r: any) => r.name === 'MessageDetails');
    const routeParams = route?.params as any;
    const fromConfirmAppointment = routeParams?.fromConfirmAppointment;
    if (fromConfirmAppointment) {
      (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
        screen: RouteNames.MCX_NAV_MESSAGES,
        params: {
          initialTab,
          refresh: true,
          fromConfirmAppointment: false,
        },
      });
    } else {
      (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
        screen: RouteNames.MCX_NAV_MESSAGES,
        params: {
          initialTab,
          refresh: true,
        },
      });
    }
    return true;
  }, [navigation, initialTab]);
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackNavigation,
    );
    return () => backHandler.remove();
  }, [handleBackNavigation]);
  const day = message.details?.day || '';
  const month = message.details?.month || '';
  const services =
    message.details?.services?.map((s: any) => s.service).join(', ') || '';
  const time = message.details?.time || '';

  const appointmentStatus =
    message['work-status'] === 'cancelled'
      ? 'CANCELLED'
      : timeline.length > 0
      ? timeline[0].tagline
      : message.status === 'open'
      ? 'Pending'
      : 'Mechanic Waiting For Your Confirmation';
  const serviceSubTitle =
    message.details?.services?.[0]?.subservice?.join(', ') ||
    'Pre-purchase Check';

  const hasMechanic = (msg: any) =>
    msg.details?.mechanic &&
    (Object.keys(msg.details.mechanic).length > 0 ||
      msg.details.mechanic === null);

  const onPrevious = () => {
    let newPos = position - 1;
    while (newPos >= 0 && !hasMechanic(messageArray[newPos])) {
      newPos--;
    }
    if (newPos >= 0) {
      (navigation as any).navigate('MessageDetails', {
        'message-array': messageArray,
        position: newPos,
      });
    }
  };

  const onNext = () => {
    let newPos = position + 1;
    while (newPos < messageArray.length && !hasMechanic(messageArray[newPos])) {
      newPos++;
    }
    if (newPos < messageArray.length) {
      (navigation as any).navigate('MessageDetails', {
        'message-array': messageArray,
        position: newPos,
      });
    }
  };

  const hasPrevious = () => {
    for (let i = position - 1; i >= 0; i--) {
      if (hasMechanic(messageArray[i])) return true;
    }
    return false;
  };

  const hasNext = () => {
    for (let i = position + 1; i < messageArray.length; i++) {
      if (hasMechanic(messageArray[i])) return true;
    }
    return false;
  };
  const onChat = useCallback(async () => {
    try {
      const cid = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!cid) {
        showCustomAlert('Error', 'Customer ID not found');
        return;
      }

      // Check if mechanic exists
      if (!mechanicDetail || Object.keys(mechanicDetail).length === 0) {
        showCustomAlert('No Mechanic', 'No mechanic assigned to this request yet.');
        return;
      }

      const mechanicId =
        mechanicDetail['mechanic-id'] ||
        mechanicDetail['id'] ||
        message['mechanic-id'];
      const mechanicImage =
        mechanicDetail?.imageUrl || mechanicDetail?.image || '';
      const mechanicName = `${mechanicDetail['first-name'] || ''} ${
        mechanicDetail['last-name'] || ''
      }`.trim();

      if (!mechanicId || !message?.['work-request-id']) {
        showCustomAlert('Error', 'Missing required information to start chat');
        return;
      }

      const params = {
        mechanicId,
        customerId: cid,
        mechanicImage,
        mechanicName,
        workRequestId: message['work-request-id'],
        workRequestDetails: {
          day: message.details?.day,
          month: message.details?.month,
          service: message.details?.services,
          time: message.details?.time,
        },
        isMessage: true,
      };

      (navigation as any).navigate(RouteNames.MCX_NAV_CHAT_ROOM, params);
    } catch (e) {
      console.log('Failed to open chat room:', e);
      showCustomAlert('Error', 'Failed to open chat');
    }
  }, [navigation, message, mechanicDetail]);

  const isSession = useCallback((msg: any) => {
    if (!msg['is-approved']) {
      return 'approve';
    } else if (msg['is-paid']) {
      return 'paid';
    } else if (msg.status === 'session') {
      return 'session';
    } else if (msg.status === 'appointment') {
      return 'appointment';
    }
    return false;
  }, []);

  const goToCompletedAppointment = useCallback(() => {
    try {
      const appointmentDetail = {
        ...message,
        details: {
          ...message.details,
          mechanic:
            typeof message.details.mechanic === 'object'
              ? message.details.mechanic.uid || message.details.mechanic.id
              : message.details.mechanic,
        },
      };

      const params = {'appointment-detail': appointmentDetail};

      if (appointmentDetail['work-status'] !== 'cancelled') {
        (navigation as any).navigate('CompletedSession', params);
      }
    } catch (error) {
      console.error('Error navigating to CompletedSession:', error);
    }
  }, [navigation, message]);

  const goToAppointmentConfirmation = useCallback(() => {
    const params = {
      'appointment-detail': message,
      initialTab,
      origin: 'messages' as const,
      fromMessageDetails: true,
    };
 if (params['appointment-detail']['work-status'] !== 'cancelled') {
    (navigation as any).push('ConfirmAppointment', params);
     }
  }, [navigation, message, initialTab]);

  const goToAppointmentSession = useCallback(() => {
    const params = {'session-details': message, mechanicDetail};
    if (params['session-details']['work-status'] !== 'cancelled') {
      (navigation as any).push('AppointmentSession', params);
    }
  }, [navigation, message, mechanicDetail]);

  const goToAppointmentDetail = useCallback(() => {
    const params = {
      'appointment-detail': message,
      initialTab,
      fromMessageDetails: true,
    };
    console.log("param:", params)
    if (params['appointment-detail']['work-status'] !== 'cancelled') {
      (navigation as any).push(RouteNames.MCX_NAV_AppointmentDetails, params);
    }
  }, [navigation, message, initialTab]);

  const goToPage = useCallback(
    (type: string | boolean) => {
      if (type === 'approve') {
        goToAppointmentConfirmation();
      } else if (type === 'paid') {
        goToCompletedAppointment();
      } else if (type === 'session') {
        goToAppointmentSession();
      } else if (type === 'appointment') {
        goToAppointmentDetail();
      } else {
        console.log('no details page');
      }
    },
    [
      goToAppointmentConfirmation,
      goToCompletedAppointment,
      goToAppointmentSession,
      goToAppointmentDetail,
    ],
  );

  const renderHeader = useCallback(
    () => (
      <>
        {/* Only show mechanic profile if mechanic data exists */}
        {mechanicDetail !== null && Object.keys(mechanicDetail).length > 0 && (
          <View style={modernStyles.profileCard}>
            {mechanicDetail.imageUrl ? (
              <Image
                source={{uri: mechanicDetail.imageUrl}}
                style={modernStyles.avatar}
              />
            ) : (
              <View
                style={[
                  modernStyles.avatar,
                  {
                    backgroundColor: '#eee',
                    justifyContent: 'center',
                    alignItems: 'center',
                  },
                ]}>
                <Icon name="person" size={28} color="#bbb" />
              </View>
            )}
            <View style={modernStyles.profileInfo}>
              <Text style={modernStyles.name}>
                {mechanicDetail['first-name']} {mechanicDetail['last-name']}
              </Text>
              <Text style={modernStyles.address}>
                {mechanicDetail.address1}, {mechanicDetail.address2}
              </Text>
              <View style={modernStyles.ratingRow}>
                <StarRating
                  rating={mechanicDetail['mechanic-rating'] || mechanicDetail.rating || 0}
                  maxRating={5}
                  size={18}
                  useImages={false}
                  showEmptyStars={true}
                  color="#8f0a19"
                />
                <Text style={modernStyles.availability}>
                  {mechanicDetail.availability ? 'Open' : 'Appointment'}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Service message card - always show */}
        <TouchableOpacity
          style={modernStyles.serviceMessage}
          onPress={() => goToPage(isSession(message))}
          activeOpacity={0.8}>
          <View style={modernStyles.dateBox}>
            <Text style={modernStyles.dateDay}>{day}</Text>
            <Text style={modernStyles.dateMonth}>{month}</Text>
          </View>
          <View style={modernStyles.serviceInfo}>
            {message.details?.services?.map((service: any, index: number) => (
              <View key={index} style={{marginBottom: 8}}>
                <Text style={[modernStyles.serviceText, {color: '#000'}]}>
                  {service.service}
                </Text>
                {service.subservice &&
                  service.subservice.map((sub: string, subIndex: number) => (
                    <Text
                      key={subIndex}
                      style={[
                        modernStyles.rateInfo,
                        {marginLeft: 16, fontSize: 12, color: '#a10000'},
                      ]}>
                      {sub}
                    </Text>
                  ))}
              </View>
            ))}
            {time && message['work-status'] !== 'cancelled' && (
              <Text style={modernStyles.serviceTime}>{time}</Text>
            )}
            <Text
              style={[
                modernStyles.status,
                message['work-status'] === 'cancelled' && {
                  backgroundColor: '#ec3d11ff',
                  color: '#000',
                },
              ]}>
              {appointmentStatus}
            </Text>
          </View>
        </TouchableOpacity>
      </>
    ),
    [
      mechanicDetail,
      day,
      month,
      services,
      serviceSubTitle,
      time,
      appointmentStatus,
      message,
      goToPage,
      isSession,
    ],
  );
  return (
    <View style={modernStyles.container}>
      <AppBackground />
      <View style={modernStyles.navTitle}>
        <TouchableOpacity
          style={modernStyles.backButton}
          onPress={handleBackNavigation}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={modernStyles.navTitleText}>MESSAGE DETAILS</Text>
      </View>
      <LoaderOverlay visible={loading} />
      {!loading && (
        <View style={modernStyles.timelineContainer}>
          <FlatList
            data={message.status === 'open' ? [] : timeline}
            keyExtractor={(_, index) => index.toString()}
            contentContainerStyle={modernStyles.content}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={renderHeader}
            renderItem={({item}) => (
              <View style={modernStyles.timelineItem}>
                <View style={modernStyles.timelineLeftContainer}>
                  <View style={modernStyles.timelineLineFull} />
                  <View style={modernStyles.timelineIconContainer}>
                    <Icon name="calendar" size={22} color="#ffff" />
                  </View>
                </View>
                <View style={modernStyles.timelineRightContainer}>
                  <View style={modernStyles.timelineDateTime}>
                    <Text style={modernStyles.timelineDate}>
                      {item.time.date}
                    </Text>
                    <Text style={modernStyles.timelineTime}>
                      {item.time.time}
                    </Text>
                  </View>
                  <View style={modernStyles.timelineCard}>
                    <Text style={modernStyles.timelineTitle}>
                      {item.tagline || item.title}
                    </Text>
                    {item.content && (
                      <Text style={modernStyles.timelineContent}>
                        {item.content}
                      </Text>
                    )}
                    <View style={modernStyles.timelineImages}>
                      {Array.isArray(item.images) && item.images.length > 0
                        ? item.images.map((img: any) => (
                            <View key={img.id} style={modernStyles.imageWrapper}>
                              {imageLoadingStates[img.id] && (
                                <View style={modernStyles.imageLoader}>
                                  <ActivityIndicator
                                    size="small"
                                    color={Colors.PRIMARY}
                                  />
                                </View>
                              )}
                              <Image
                                source={{uri: img.url}}
                                style={[
                                  modernStyles.timelineImage,
                                  imageLoadingStates[img.id] && {opacity: 0.5},
                                ]}
                                onLoad={() => handleImageLoad(img.id)}
                                onError={() => handleImageError(img.id)}
                              />
                            </View>
                          ))
                        : null}
                    </View>
                  </View>
                </View>
              </View>
            )}
          />
        </View>
      )}
      {mechanicDetail && Object.keys(mechanicDetail).length > 0 && (
        <TouchableOpacity
          style={modernStyles.chatFab}
          onPress={onChat}
          disabled={loading || !mechanicDetail}>
          <Icon name="chatbubbles-outline" size={30} color="#fff" />
        </TouchableOpacity>
      )}
      <View style={modernStyles.footer}>
        <TouchableOpacity
          style={[
            modernStyles.footerBtn,
            (loading || !hasPrevious()) && {opacity: 0.5},
          ]}
          onPress={onPrevious}
          disabled={loading || !hasPrevious()}>
          <Text style={modernStyles.footerBtnText}>Previous</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            modernStyles.footerBtn,
            (loading || !hasNext()) && {opacity: 0.5},
          ]}
          onPress={onNext}
          disabled={loading || !hasNext()}>
          <Text style={modernStyles.footerBtnText}>Next</Text>
        </TouchableOpacity>
      </View>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

export default MessageDetails;

// Modern Styles - Enhanced styling for better visual appeal
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  backButton: {
    position: 'absolute',
    left: 20,
    padding: 12,
    zIndex: 10,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  navTitle: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    alignItems: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  navTitleText: {
    color: '#fff',
    fontWeight: '800',
    fontSize: 18,
    letterSpacing: 1.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  timelineContainer: {
    flex: 1,
    position: 'relative',
  },
  content: {
    flexGrow: 1,
    padding: 10,
    paddingTop: 16,
    paddingBottom: 120,
  },
  profileCard: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    padding: 20,
    borderRadius: 20,
    marginBottom: 10,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.15,
    shadowRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: Colors.PRIMARY,
  },
  profileInfo: {
    marginLeft: 16,
    justifyContent: 'center',
    flex: 1,
  },
  name: {
    fontWeight: '700',
    fontSize: 18,
    color: Colors.TEXT_COLOR,
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  address: {
    color: '#666666',
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  ratingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 12,
    color: Colors.PRIMARY,
  },
  availability: {
    color: Colors.PRIMARY,
    fontWeight: '700',
    fontSize: 12,
    backgroundColor: Colors.PRIMARY + '15',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  serviceMessage: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    marginVertical: 16,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.12,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  dateBox: {
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    width: 80,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  dateDay: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '800',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  dateMonth: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.9,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  serviceInfo: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  serviceText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.TEXT_COLOR,
    marginBottom: 6,
    letterSpacing: 0.3,
  },
  rateInfo: {
    color: '#666666',
    marginTop: 4,
    fontSize: 14,
    lineHeight: 20,
  },
  serviceTime: {
    color: '#666666',
    marginTop: 4,
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'right',
  },
  status: {
    marginTop: 8,
    color: Colors.PRIMARY,
    fontWeight: '700',
    fontSize: 13,
    backgroundColor: Colors.PRIMARY + '15',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 24,
    paddingHorizontal: 4,
    position: 'relative',
  },
  timelineLeftContainer: {
    width: 70,
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 12,
  },
  timelineLineFull: {
    position: 'absolute',
    left: 34,
    top: 0,
    bottom: -24,
    width: 3,
    backgroundColor: '#11b4f4ff',
    zIndex: 1,
    borderRadius: 2,
  },
  timelineIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#11b4f4ff',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    elevation: 6,
    shadowColor: Colors.PRIMARY,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.4,
    shadowRadius: 8,
    position: 'relative',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.9)',
  },
  timelineRightContainer: {
    flex: 1,
    marginLeft: 20,
    paddingTop: 12,
  },
  timelineDateTime: {
    marginBottom: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  timelineDate: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 2,
    opacity: 0.9,
    letterSpacing: 0.5,
  },
  timelineTime: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.COMMON_WHITE_SHADE,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  timelineCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: 16,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  timelineTitle: {
    fontWeight: '700',
    color: Colors.PRIMARY,
    fontSize: 16,
    marginBottom: 10,
    letterSpacing: 0.3,
  },
  timelineContent: {
    color: '#555555',
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 12,
  },
  timelineImages: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 8,
  },
  imageWrapper: {
    position: 'relative',
    width: 60,
    height: 60,
  },
  imageLoader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 12,
    zIndex: 10,
  },
  timelineImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  chatFab: {
    position: 'absolute',
    bottom: 100,
    right: 24,
    backgroundColor: Colors.PRIMARY,
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 12,
    shadowColor: Colors.PRIMARY,
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.4,
    shadowRadius: 12,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.9)',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: 'rgba(18, 27, 42, 0.95)',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.2,
    shadowRadius: 12,
    gap: 16,
  },
  footerBtn: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 4,
    shadowColor: Colors.PRIMARY,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  footerBtnText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 16,
    letterSpacing: 0.8,
    textTransform: 'uppercase',
  },
});
