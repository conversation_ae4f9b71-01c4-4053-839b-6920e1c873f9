import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  TextInput,
  StyleSheet,
  ActivityIndicator,
    SafeAreaView
} from 'react-native';
import Modal from 'react-native-modal';
import {useRoute, useNavigation} from '@react-navigation/native';
import {AppCommonIcons} from '../../../../utils/constants/AppStrings';
import AppBackground from '../../../../components/ui/AppBackground';
import CustomAlert from '../../../../components/common/CustomAlert';
import StarRating from '../../../../components/common/StarRating';
import Icon from 'react-native-vector-icons/Ionicons';
import {getAuth} from '@react-native-firebase/auth';
import {get} from '@react-native-firebase/database';
import PaymentService from '../../../../utils/services/PaymentService';
import {AppointmentService} from '../../../../utils/services/AppointmentService';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';

const CheckoutScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const {
    mechanicDetail,
    sessionDetail,
    costDetail,
    onPayNow,
    onRedeemPoints,
    workRequestId,
  } = route.params as any;
  const creditedPoint = 0;
  const totalNum = parseFloat(costDetail?.total || '0');
  const taxesPercentage = costDetail?.taxes || 0;
  const serviceFee = totalNum / (1 + taxesPercentage / 100);
  const taxesNum = totalNum - serviceFee;
  const subTotal = serviceFee + taxesNum;
  const costDetailWithDefaults = {
    serviceFee: serviceFee,
    taxes: taxesPercentage,
    subTotal: subTotal,
    tip: costDetail?.tip || 0,
    couponFee: costDetail?.couponFee || 0,
    total: totalNum,
  };
  const services = sessionDetail?.details?.services || [];
  const [invoiceData, setInvoiceData] = useState<any>({
    services: [],
    taxes: 0,
    total: 0,
  });
  const [loadingInvoice, setLoadingInvoice] = useState(true);
  const [selectedTip, setSelectedTip] = useState<string | null>(null);
  const [customTip, setCustomTip] = useState<number>(0);
  const [savedCards, setSavedCards] = useState<any[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [customTipInput, setCustomTipInput] = useState<string>('');

  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const processInvoiceDetail = useCallback((invoice: any) => {
    if (!invoice || typeof invoice !== 'object') {
      console.warn('Invalid invoice data: invoice is not an object');
      return;
    }
    if (!invoice.services || !Array.isArray(invoice.services)) {
      console.warn('Invalid invoice data: missing or invalid services array');
      return;
    }
    let tempInvoice = [];
    for (let sv = 0; sv < invoice.services.length; sv++) {
      const service = invoice.services[sv];
      if (!service || typeof service !== 'object') continue;
      if (!service.subservice || !Array.isArray(service.subservice)) continue;
      if (!service.selected || !Array.isArray(service.selected)) continue;
      if (!service.mechanicBid || !Array.isArray(service.mechanicBid)) continue;
      let tempSubInvoice = [];
      for (let ssv = 0; ssv < service.subservice.length; ssv++) {
        if (
          service.selected[ssv] &&
          service.subservice[ssv] &&
          service.mechanicBid[ssv]
        ) {
          tempSubInvoice.push({
            name: service.subservice[ssv],
            price: service.mechanicBid[ssv],
          });
        }
      }
      if (tempSubInvoice.length > 0) {
        tempInvoice.push({
          service: service.service || '',
          subservice: tempSubInvoice,
        });
      }
    }
    const serviceDetails = {
      services: tempInvoice,
      taxes:
        invoice['service-tax'] && typeof invoice['service-tax'] === 'number'
          ? invoice['service-tax']
          : 0,
      total: '',
    };
    calculateTaxes(serviceDetails);
    setInvoiceData(serviceDetails);
  }, []);

  const calculateTaxes = (serviceDetails: any) => {
    serviceDetails.total = 0;
    let totServ = 0;
    for (let sv = 0; sv < serviceDetails.services.length; sv++) {
      for (
        let ssv = 0;
        ssv < serviceDetails.services[sv].subservice.length;
        ssv++
      ) {
        const price = parseFloat(
          serviceDetails.services[sv].subservice[ssv].price,
        );
        if (!isNaN(price)) {
          totServ += price;
        }
      }
    }
    serviceDetails.serviceFee = totServ.toFixed(2);
    const taxes = parseFloat(serviceDetails.taxes);
    if (!isNaN(taxes)) {
      serviceDetails.total = (totServ + (taxes * totServ) / 100).toFixed(2);
    } else {
      serviceDetails.total = totServ.toFixed(2);
    }
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      await fetchCards();
      await fetchInvoiceData();
    };

    const fetchCards = async () => {
      const auth = getAuth();
      const user = auth.currentUser;
      if (user) {
        try {
          const cardsData = await PaymentService.fetchCustomerCardDetails();
          const cardsArray = cardsData
            ? Object.keys(cardsData).map(key => ({
                id: key,
                ...cardsData[key],
              }))
            : [];
          setSavedCards(cardsArray);
          if (cardsArray.length > 0) {
            setSelectedCardId(cardsArray[0].id);
          }
        } catch (error) {
          console.error('Error fetching cards:', error);
          setSavedCards([]);
        }
      } else {
        console.warn('No authenticated user found');
        setSavedCards([]);
      }
    };

    const fetchInvoiceData = async () => {
      setLoadingInvoice(true);
      try {
        if (!workRequestId) {
          console.warn('No work-request-id found');
          return;
        }
        const invoiceQuery = AppointmentService.fetchInvoiceGenerated(workRequestId);
        const invoiceSnapshot = await get(invoiceQuery);

        if (invoiceSnapshot.exists()) {
          const invoiceDetail = invoiceSnapshot.val()[workRequestId];
          processInvoiceDetail(invoiceDetail);
        }
      } catch (error) {
        console.error('Error fetching invoice:', error);
      } finally {
        setLoadingInvoice(false);
      }
    };

    checkNetworkAndLoad();
  }, [navigation, workRequestId, processInvoiceDetail]);

  const tipAmount = (() => {
    if (selectedTip === 'custom') {
      return customTip;
    } else if (selectedTip) {
      return (
        (parseInt(selectedTip, 10) / 100) * costDetailWithDefaults.serviceFee
      );
    }
    return costDetailWithDefaults.tip || 0;
  })();


  const formatTipAmount = (amount: number) => {
    return parseFloat(amount.toFixed(2));
  };
  const handleTipSelect = (tip: string) => {
    setSelectedTip(tip);
    if (tip !== 'custom') {
      setCustomTip(0);
      setCustomTipInput('');
    }
  };

  const calculateTotal = () => {
    const baseAmount = parseFloat(invoiceData.total || costDetailWithDefaults.subTotal);
    return (
      baseAmount +
      tipAmount -
      costDetailWithDefaults.couponFee
    ).toFixed(2);
  };

  const onPayNowHandler = () => {
    setModalVisible(true);
  };

  const confirmPayment = async (selectedCard: any) => {
    setIsProcessing(true);

    if (!mechanicDetail['stripe-acc-key']) {
      setIsProcessing(false);
      showCustomAlert(
        'Payment Failed',
        'Your payment process failed on the payment gateway due to mechanic account information missing, Please contact your mechanic.',
        [
          {
            text: 'OK',
            onPress: () => {
              hideAlert();
              setModalVisible(false);
            },
          },
        ]
      );
      return;
    }

    const token = selectedCard.token;

    const paymentInfo = {
      mechanicStripeKey: mechanicDetail['stripe-acc-key'],
      tipToMechanic: costDetail?.tip || 0,
      serviceFee: Number.parseInt(costDetail['serviceFee']),
      serviceTax: costDetail['taxes'],
      couponFee: costDetail?.couponFee || 0,
      'work-request-id': workRequestId,
    };

    const amount = Math.round(parseFloat(calculateTotal()));
    const auth = getAuth();
    const user = auth.currentUser;
    if (user) {
      try {
        const result = await PaymentService.processPayment(
          token,
          paymentInfo,
          amount,
        );
        setIsProcessing(false);
        if (result && (result as any).charge) {
          if ((result as any).charge.status === 'succeeded') {
            showCustomAlert('Success', 'Payment processed successfully!', [
              {
                text: 'OK',
                onPress: () => {
                  hideAlert();
                  setModalVisible(false);
                  if (onPayNow) {
                    onPayNow();
                  }
                },
              },
            ]);
          } else if ((result as any).charge.status === 'charge-failed') {
            showCustomAlert(
              'Payment Failed',
              (result as any).charge.errorMessage ||
                'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    hideAlert();
                    setModalVisible(false);
                    navigation.goBack();
                  },
                },
              ]
            );
          } else {
            showCustomAlert(
              'Payment Failed',
              (result as any).charge.errorMessage ||
                'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    hideAlert();
                    setModalVisible(false);
                    navigation.goBack();
                  },
                },
              ]
            );
          }
        } else {
          showCustomAlert(
            'Payment Failed',
            'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
            [
              {
                text: 'OK',
                onPress: () => {
                  hideAlert();
                  setModalVisible(false);
                  navigation.goBack();
                },
              },
            ]
          );
        }
      } catch (error) {
        setIsProcessing(false);
        showCustomAlert(
          'Payment Failed',
          'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
          [
            {
              text: 'OK',
              onPress: () => {
                hideAlert();
                setModalVisible(false);
              },
            },
          ]
        );
      }
    }
  };



  return (
    < SafeAreaView
      style={[
        styles.container,
        {paddingTop: insets.top},
        {paddingBottom: insets.top},
      ]}>
      <AppBackground />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Image
          source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
      <View style={styles.navTitle}>
        <Text style={styles.navTitleText}>CHECKOUT</Text>
      </View>
      <ScrollView
        style={styles.scrollArea}
        contentContainerStyle={{paddingBottom: 30}}>
        <Text style={styles.sectionTitle}>MY MECHANIC</Text>
        <View style={styles.card}>
          <Image
            source={{uri: mechanicDetail.imageUrl}}
            style={styles.mechanicImage}
          />
          <View style={styles.mechanicInfo}>
            <Text style={styles.mechanicName}>
              {mechanicDetail['first-name']} {mechanicDetail['last-name']}
            </Text>
            <Text style={{ color: '#000' }} >
              {mechanicDetail.address1}, {mechanicDetail.address2}
            </Text>
            <Text style={styles.availability}>
              Availability: {mechanicDetail.availability ? 'Open' : 'Appointment'}
            </Text>
            <View style={styles.ratingContainer}>
              <StarRating
                rating={mechanicDetail['mechanic-rating'] || 0}
                maxRating={5}
                size={16}
                useImages={false}
                showEmptyStars={true}
                color="#8f0a19"
              />
            </View>
          </View>
        </View>
        <View style={styles.appointmentCard}>
          <View style={[styles.dateBlock, { alignSelf: 'stretch', height: 'auto', paddingVertical: 20 }]}>
            <Text style={styles.dateNumber}>
              {sessionDetail.details?.day}
            </Text>
            <Text style={styles.dateMonth}>
              {sessionDetail.details?.month}
            </Text>
          </View>
          <View style={styles.serviceInfo}>
            {services.map((service: any, index: number) => (
              <View key={index} style={{ marginBottom: 8 }}>
                <Text style={styles.serviceTitle}>{service.service}</Text>
                {service.subservice && service.subservice.map((sub: string, subIndex: number) => (
                  <Text key={subIndex} style={[styles.serviceDescription, { marginLeft: 16, fontSize: 12 }]}>{sub}</Text>
                ))}
              </View>
            ))}
            <Text style={styles.serviceTime}>
              {sessionDetail.details?.time}
            </Text>
          </View>
        </View>
        <Text style={styles.sectionTitle}>PAYMENT DETAILS</Text>
        <View style={styles.paymentBox}>
          {savedCards.length === 0 ? (
            <TouchableOpacity style={styles.paymentItem}>
              <Text>Add payment method</Text>
            </TouchableOpacity>
          ) : (
            savedCards?.map((card, index) => (
              <View key={card.id}>
                <View style={styles.paymentItem}>
                  <Text style={styles.cardText}>
                    **** **** **** {card.cards?.last4 || '****'}
                  </Text>
                  <Text style={styles.expText}>
                    Exp: {card.cards?.exp_month || 'MM'}/
                    {card.cards?.exp_year || 'YYYY'}
                  </Text>
                </View>
                {index < savedCards.length - 1 && (
                  <View style={styles.divider} />
                )}
              </View>
            ))
          )}
        </View>
        <Text style={styles.sectionTitle}>SESSION DETAILS</Text>
        <View style={styles.sessionBox}>
          {/* Services List with Prices */}
          <View style={styles.servicesList}>

            {loadingInvoice ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#900" />
                <Text style={styles.loadingText}>Loading invoice details...</Text>
              </View>
            ) : invoiceData.services && invoiceData.services.length > 0 ? (
              invoiceData.services.map((service: any, idx: number) => (
                <View key={idx}>
                  <View style={styles.invoiceRow}>
                    <Text style={styles.invoiceLabel}>
                      {service.service}
                    </Text>
                    <Text />
                  </View>
                  {service.subservice.map((sub: any, subIdx: number) => (
                    <View key={subIdx} style={styles.invoiceRow}>
                      <Text style={styles.invoiceLabel}> - {sub.name}</Text>
                      <Text style={{color: '#000'}}>${sub.price}</Text>
                    </View>
                  ))}
                </View>
              ))
            ) : (
              <Text style={styles.invoiceLabel}>
                No invoice data available
              </Text>
            )}
          </View>

          <View style={styles.divider} />

          {/* Session Information */}
          <View style={styles.sessionInfo}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Duration:</Text>
              <Text style={styles.infoValue}>{sessionDetail.duration || 'Not specified'}</Text>
            </View>
            {/* <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Visit Type:</Text>
              <Text style={styles.infoValue}>{sessionDetail['visit-type'] || (mechanicDetail.availability ? 'Open' : 'Appointment')}</Text>
            </View> */}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Location:</Text>
              <View style={styles.locationContainer}>
                <Text style={styles.locationText} numberOfLines={2}>
                  {(() => {
                    if (!sessionDetail.location || sessionDetail.location.length === 0) {
                      return 'Location not available';
                    }

                    // First line: address1, address2, city
                    const firstLine = sessionDetail.location.slice(0, 3).filter((line: string) => line && line.trim()).join(', ');

                    // Second line: state, pin, country (extract from the last item)
                    let secondLine = '';
                    if (sessionDetail.location.length > 2) {
                      const lastItem = sessionDetail.location[sessionDetail.location.length - 1];
                      if (lastItem && lastItem.includes(' - ')) {
                        // Extract state from the city,state part and country-pin from the last part
                        const cityStatePart = sessionDetail.location[2] || '';
                        const countryPinPart = lastItem;

                        // Get state from cityStatePart (after comma)
                        const state = cityStatePart.split(', ')[1] || '';

                        // Get country and pin from countryPinPart
                        const countryPin = countryPinPart.split(' - ');
                        const country = countryPin[0] || '';
                        const pin = countryPin[1] || '';

                        secondLine = [state, pin, country].filter(item => item && item.trim()).join(', ');
                      } else {
                        secondLine = lastItem;
                      }
                    }

                    return [firstLine, secondLine].filter(line => line && line.trim()).join('\n');
                  })()}
                </Text>
              </View>
            </View>
          </View>
        </View>
        <Text style={styles.sectionTitle}>LEAVE A TIP</Text>
        <View style={styles.tipRow}>
          {['2', '5', '10', '15', 'custom'].map(tip => (
            <TouchableOpacity
              key={tip}
              style={[
                styles.tipButton,
                selectedTip === tip && styles.tipSelected,
              ]}
              onPress={() => handleTipSelect(tip)}>
              <Text style={styles.tipText}>
                {tip === 'custom' ? 'Custom' : `${tip}%`}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {selectedTip === 'custom' && (
  <TextInput
    placeholder="Tip ($)"
    style={styles.customTipInput}
    keyboardType="decimal-pad"
    value={customTipInput}
    onChangeText={val => {
      if (val === '' || /^\d*\.?\d{0,2}$/.test(val)) {
        setCustomTipInput(val);

        const numVal = val === '' || val === '.' ? 0 : parseFloat(val) || 0;
        setCustomTip(numVal);
      }
    }}
    onBlur={() => {
      if (customTipInput && customTipInput !== '.') {
        const formatted = parseFloat(customTipInput).toFixed(2);
        setCustomTipInput(formatted);
        setCustomTip(parseFloat(formatted));
      }
    }}
    maxLength={10}
  />
)}
        {creditedPoint > 0 && (
          <>
            <Text style={styles.sectionTitle}>REFERRAL POINT</Text>
            <View style={styles.referralBox}>
              <Text>
                Earned Points:{' '}
                <Text style={styles.referralHighlight}>${creditedPoint}</Text>
              </Text>
              <TouchableOpacity
                style={styles.redeemButton}
                onPress={onRedeemPoints}>
                <Text style={styles.redeemText}>Redeem Now</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
        <Text style={styles.sectionTitle}>COST DETAILS</Text>
        <View style={styles.costBox}>
          <View style={styles.costRow}>
            <Text style={{ color: '#000' }} >Service Fee</Text>
            <Text style={{ color: '#000' }}>${invoiceData.serviceFee || costDetailWithDefaults.serviceFee.toFixed(2)}</Text>
          </View>
          <View style={styles.costRow}>
            <Text style={{ color: '#000' }} >Taxes ({invoiceData.taxes || costDetailWithDefaults.taxes}%)</Text>
            <Text style={{ color: '#000' }}>${((parseFloat(invoiceData.serviceFee || costDetailWithDefaults.serviceFee) * (invoiceData.taxes || costDetailWithDefaults.taxes)) / 100).toFixed(2)}</Text>
          </View>
          <View style={styles.costRow}>
            <Text style={styles.subTotal}>Sub Total</Text>
            <Text style={styles.subTotal}>
              ${invoiceData.total || costDetailWithDefaults.subTotal.toFixed(2)}
            </Text>
          </View>
          <View style={styles.costRow}>
            <Text style={{ color: '#000' }}>Tip</Text>
            <Text style={{ color: '#000' }}>${formatTipAmount(tipAmount).toFixed(2)}</Text>
          </View>

          {costDetailWithDefaults.couponFee > 0 && (
            <View style={styles.costRow}>
              <Text>Coupon discount</Text>
              <Text>${costDetailWithDefaults.couponFee.toFixed(2)}</Text>
            </View>
          )}
          <View style={styles.costRow}>
            <Text style={styles.totalText}>Total</Text>
            <Text style={styles.totalText}>${calculateTotal()}</Text>
          </View>
        </View>
      </ScrollView>
      <TouchableOpacity style={styles.footerButton} onPress={onPayNowHandler}>
        <Text style={styles.footerButtonText}>PAY NOW</Text>
      </TouchableOpacity>
      <Modal
        isVisible={modalVisible}
        onBackdropPress={() => !isProcessing && setModalVisible(false)}
        useNativeDriver={true}
        useNativeDriverForBackdrop={true}>
        <View style={styles.modalContainer}>
          {isProcessing ? (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color="#900" />
              <Text style={styles.processingText}>
                Processing payment... Do not refresh this page.
              </Text>
            </View>
          ) : (
            <>
              <Text style={styles.modalHeader}>CHOOSE CARD</Text>
              {savedCards.length === 0 ? (
                <Text  style={{ color: '#000' }} >No saved cards available</Text>
              ) : (
                savedCards.map(card => (
                  <TouchableOpacity
                    key={card.id}
                    style={styles.cardOption}
                    onPress={() => setSelectedCardId(card.id)}>
                    <View style={styles.radioCircle}>
                      {selectedCardId === card.id && (
                        <View style={styles.selectedRb} />
                      )}
                    </View>
                    <View style={styles.cardDetails}>
                      <Text style={styles.expiryText}>
                        Exp : {card.cards?.exp_month}/{card.cards?.exp_year}
                      </Text>
                      <Text style={styles.cardNumber}>
                        **** **** **** {card.cards?.last4}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))
              )}
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={styles.backModalButton}
                  onPress={() => {
                    setTimeout(() => setModalVisible(false), 100);
                  }}>
                  <Text style={styles.backButtonText}>BACK</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.payButton}
                  onPress={() => {
                    const selectedCard = savedCards.find(
                      card => card.id === selectedCardId,
                    );
                    if (selectedCard) {
                      confirmPayment(selectedCard);
                    }
                  }}
                  disabled={!selectedCardId}>
                  <Text style={styles.payButtonText}>PAY ${calculateTotal()}</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </Modal>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </SafeAreaView>
  );
};

export default CheckoutScreen;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c1a24',
  },
  header: {
    height: 56,
    backgroundColor: '#1f2a38',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  backButton: {
    position: 'absolute',
    left: 15,
    padding: 8,
    zIndex: 10,
  },
  logo: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 16,
    marginTop: 5,
  },
  scrollArea: {
    padding: 15,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 16,
    marginVertical: 10,
  },

  card: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 8,
  },
  mechanicImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  mechanicInfo: {
    marginLeft: 10,
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: 16,
      color: '#000',
  },
  availability: {
    color: 'red',
  },
  ratingContainer: {
    marginTop: 4,
  },
  appointmentCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 8,
    marginVertical: 10,
    alignItems: 'flex-start',
  },
  navTitle: {
    backgroundColor: '#a00',
    paddingVertical: 10,
    alignItems: 'center',
  },
  navTitleText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  dateBlock: {
    backgroundColor: '#b22222',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
  },
  dateNumber: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  dateMonth: {
    color: '#fff',
    fontSize: 14,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 16,
  },
  serviceDescription: {
    color: '#b22222',
    fontSize: 14,
    marginTop: 2,
  },
  serviceTime: {
    color: '#555',
    fontSize: 12,
    marginTop: 4,
  },
  paymentBox: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 10,
  },
  paymentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    justifyContent: 'space-between',
  },
  rightArrow: {
    width: 20,
    height: 20,
    tintColor: '#999',
  },
  cardIcon: {
    width: 50,
    height: 30,
    resizeMode: 'contain',
  },
  cardText: {
    color: '#000',
    marginLeft: 10,
    fontSize: 14,
  },
  expText: {
    
    fontSize: 12,
    color: '#000',
  },
  divider: {
    height: 1,
    backgroundColor: '#ddd',
    marginVertical: 10,
  },
  sessionBox: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  servicesList: {
    marginBottom: 15,
  },
  servicesHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
  },
  serviceItem: {
    marginBottom: 10,
  },
  serviceName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#000',
    marginBottom: 5,
  },
  subserviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 15,
    marginBottom: 3,
  },
  subserviceName: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  subservicePrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#b22222',
  },
  sessionInfo: {
    marginTop: 10,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000',
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  locationContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  locationText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'right',
    marginBottom: 2,
  },
  invoiceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  invoiceLabel: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#000',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  tipRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  tipButton: {
    padding: 10,
    backgroundColor: '#333',
    borderRadius: 6,
    flex: 1,
    marginHorizontal: 3,
    alignItems: 'center',
  },
  tipSelected: {
    backgroundColor: '#900',
  },
  tipText: {
    color: '#fff',
  },
  customTipInput: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
     color: '#000',
  },
  referralBox: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  referralHighlight: {
    color: 'red',
    fontWeight: 'bold',
  },
  redeemButton: {
    marginTop: 10,
    backgroundColor: '#900',
    padding: 10,
    borderRadius: 6,
  },
  redeemText: {
    color: '#fff',
    textAlign: 'center',
  },
  costBox: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 8,
  },
  costRow: {
    color: '#000',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  subTotal: {
    fontWeight: 'bold',
    color: '#900',
  },
  totalText: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#000',
  },
  footerButton: {
    backgroundColor: '#900',
    padding: 18,
    alignItems: 'center',
  },
  footerButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    width: '90%',
    alignSelf: 'center',
  },
  modalHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#e90d0dff',
  },
  cardOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#900',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  selectedRb: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#900',
  },
  cardDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expiryText: {
    color: '#900',
    marginRight: 10,
  },
  cardLogo: {
    width: 40,
    height: 25,
    resizeMode: 'contain',
    marginRight: 10,
  },
  cardNumber: {
    fontWeight: 'bold',
    color: '#000',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  backModalButton: {
    backgroundColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  backButtonText: {
    color: '#000',
    fontWeight: 'bold',
  },
  payButton: {
    backgroundColor: '#900',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    alignItems: 'center',
  },
  payButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  processingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  processingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
});
