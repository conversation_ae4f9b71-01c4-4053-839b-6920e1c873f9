import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import {RouteNames} from '../../../utils/constants/AppStrings';
import {useNavigation} from '@react-navigation/native';
import {CardField, useStripe} from '@stripe/stripe-react-native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomButton from '../../../components/common/CustomButton';
import CustomAlert from '../../../components/common/CustomAlert';
import {Colors, Fonts, Sizes} from '../../../utils/constants/Theme';
import PaymentService from '../../../utils/services/PaymentService';
import auth from '@react-native-firebase/auth';
import database from '@react-native-firebase/database';
import AppBar from '../../../components/common/AppBar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';
const EditPaymentPage = () => {
  const navigation = useNavigation();
  const {createToken} = useStripe();
  const emailListenerRef = useRef<any>(null);
  const cardListenerRef = useRef<any>(null);
  const cardRefPath = useRef<any>(null);

  const [customerEmail, setCustomerEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [cardComplete, setCardComplete] = useState(false);
  const [cardDetails, setCardDetails] = useState<any>(null);
  const [formError, setFormError] = useState('');
  const [cardError, setCardError] = useState('');
  const [cardKey, setCardKey] = useState(Date.now());
  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };
  const checkUserAuthentication = useCallback((): boolean => {
    const user = auth().currentUser;
    if (!user) {
      showCustomAlert('Authentication Required', 'Please login to continue', [
        {text: 'OK', onPress: () => navigation.goBack()},
      ]);
      return false;
    }
    return true;
  }, [navigation]);

  const fetchCustomerEmail = async () => {
    try {
      const customerId = auth().currentUser?.uid;
      if (!customerId) {
        console.error('No customer ID found');
        return;
      }

      const emailRef = database()
        .ref('customer')
        .child(customerId)
        .child('email');

      emailListenerRef.current = emailRef.on('value', (emailSnap: any) => {
        if (emailSnap && emailSnap.val !== undefined) {
          const email = emailSnap.val();
          console.log('Customer email fetched:', email);
          setCustomerEmail(email || '');
        }
      });
    } catch (error) {
      console.error('Error fetching customer email:', error);
    }
  };

  const handleCardChange = async (cardDetail: any) => {
    setCardDetails(cardDetail);
    await setCardComplete(cardDetail.complete);

    // Real-time validation - show errors immediately
    let errorMessage = '';

    if (cardDetail.validNumber === 'Invalid') {
      errorMessage = 'Your card number is invalid.';
    } else if (cardDetail.validNumber === 'Incomplete') {
      errorMessage = 'Your card number is incomplete.';
    } else if (cardDetail.validExpiryDate === 'Invalid') {
      errorMessage = "Your card's expiration date is invalid.";
    } else if (cardDetail.validExpiryDate === 'Incomplete') {
      errorMessage = "Your card's expiration date is incomplete.";
    } else if (cardDetail.validCVC === 'Invalid') {
      errorMessage = "Your card's security code is invalid.";
    } else if (cardDetail.validCVC === 'Incomplete') {
      errorMessage = "Your card's security code is incomplete.";
    } else if (cardDetail.postalCode && cardDetail.postalCode.length > 10) {
      errorMessage = 'Your postal code is too long.';
    } else if (!cardDetail.complete && cardDetail.postalCode === '') {
      errorMessage = 'Your postal code is incomplete.';
    }

    setCardError(errorMessage);

    if (formError) {
      setFormError('');
    }
  };

  const handleCardBlur = () => {
    // Additional validation on blur
    if (cardDetails && !cardDetails.complete) {
      let errorMessage = '';

      if (cardDetails.validNumber === 'Invalid') {
        errorMessage = 'Your card number is invalid.';
      } else if (cardDetails.validNumber === 'Incomplete') {
        errorMessage = 'Your card number is incomplete.';
      } else if (cardDetails.validExpiryDate === 'Invalid') {
        errorMessage = "Your card's expiration date is invalid.";
      } else if (cardDetails.validExpiryDate === 'Incomplete') {
        errorMessage = "Your card's expiration date is incomplete.";
      } else if (cardDetails.validCVC === 'Invalid') {
        errorMessage = "Your card's security code is invalid.";
      } else if (cardDetails.validCVC === 'Incomplete') {
        errorMessage = "Your card's security code is incomplete.";
      } else if (
        !cardDetails.postalCode ||
        cardDetails.postalCode.trim() === ''
      ) {
        errorMessage = 'Your postal code is incomplete.';
      }

      setCardError(errorMessage);
    }
  };

  const handleSubmit = async () => {
    // Validate postal code length before submission
    if (cardDetails?.postalCode && cardDetails.postalCode.length > 10) {
      setCardError('Your postal code is too long (max 10 characters).');
      return;
    }

    if (!cardComplete) {
      setCardError('Please complete all card details.');
      showCustomAlert('Error', 'Please complete your card details');
      return;
    }

    setFormError('');
    setCardError('');
    setLoading(true);
    setLoadingMessage('Connecting to payment gateway...');

    try {
      setLoadingMessage('Processing card details...');
      console.log('card details');
      console.log(cardDetails);

      // Create token using Stripe React Native
      const {token, error} = await createToken({type: 'Card'});

      if (error) {
        throw new Error(error.message);
      }

      if (!token) {
        throw new Error('Failed to create payment token');
      }

      console.log('Stripe token created:', token);

      // Save to Firebase
      await stripeTokenHandler(token);
    } catch (error: any) {
      console.error('Payment processing error:', error);
      setLoading(false);
      setLoadingMessage('');
      setFormError(error.message || 'Failed to process payment information');
    }
  };

  const stripeTokenHandler = async (token: any) => {
    try {
      const customerId = auth().currentUser?.uid;
      if (!customerId) {
        showCustomAlert('Error', 'User not authenticated');
        setLoading(false);
        setLoadingMessage('');
        return;
      }

      token.email = customerEmail;

      console.log('Payment API Payload:', token);

      const cardRef = await PaymentService.saveCardDetails(token);
      cardRefPath.current = cardRef;

      cardListenerRef.current = cardRef.on('value', (snapshot: any) => {
        if (snapshot && snapshot.val !== undefined) {
          const cardData = snapshot.val();

          if (cardData) {
            console.log('Payment API Response:', cardData);

            if (cardData.status === 'saved') {
              cleanupCardListener();

              setLoading(false);
              setLoadingMessage('');

              showCustomAlert('SUCCESS', 'Payment method added successfully.', [
                {text: 'OK', onPress: () => navigation.goBack()},
              ]);
            } else if (cardData.status === 'card-failed') {
              cleanupCardListener();

              setLoading(false);
              setLoadingMessage('');

              // Match Ionic behavior: show error and go back
              showCustomAlert(
                'FAILED',
                cardData.errorMessage || 'Processing failed.',
                [{text: 'OK', onPress: () => navigation.goBack()}],
              );
            }
          }
        }
      });
    } catch (error) {
      console.error('Error saving card:', error);
      setLoading(false);
      setLoadingMessage('');
      showCustomAlert('Error', 'Failed to save card details');
    }
  };

  const cleanupCardListener = () => {
    if (cardListenerRef.current && cardRefPath.current) {
      cardListenerRef.current();
      cardListenerRef.current = null;
      cardRefPath.current = null;
    }
  };

  const cleanupListeners = useCallback(() => {
    if (emailListenerRef.current) {
      emailListenerRef.current();
      emailListenerRef.current = null;
    }

    cleanupCardListener();
  }, []);

  useEffect(() => {
    // Reset all states when component mounts to clear any previous input data
    setCustomerEmail('');
    setLoading(false);
    setLoadingMessage('');
    setCardComplete(false);
    setCardDetails(null);
    setFormError('');
    setCardError('');
    //setCardKey(Date.now()); // Force CardField remount to clear inputs
  }, []);

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      if (!checkUserAuthentication()) {
        return;
      }

      fetchCustomerEmail();
    };

    checkNetworkAndLoad();

    return () => {
      cleanupListeners();
    };
  }, [checkUserAuthentication, cleanupListeners, navigation]);
  const handleMailPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_MESSAGES as never);
  };
  return (
    <SafeAreaView style={[styles.mainContainer,{paddingTop: insets.top}]}>
    <KeyboardAvoidingView
      style={styles.mainContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      <AppBar  showBackButton={false}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true} onMailPress={handleMailPress} />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.mainContainer}>
          <TitleSection
            title="ADD CARD DETAILS"
            bgColor={Colors.PRIMARY}
            onBack={() => navigation.goBack()}
            textColor="#fff"
            style={styles.titleSection}
          />
          <ScreenLayout
            useScrollView={true}
            useImageBackground={true}
            centerContent={false}
            useHorizontalPadding={true}
            scrollContainerStyle={{paddingBottom: 80}}>
            <View style={styles.formContainer}>
              <Text style={styles.cardLabel}>Credit or debit card</Text>

              <View style={styles.cardFieldContainer}>
                <CardField
                  key={cardKey}
                  postalCodeEnabled={true}
                  placeholders={{
                    number: 'Card number',
                    cvc: 'CVC',
                    postalCode: 'ZIP Code',
                  }}
                  cardStyle={{
                    backgroundColor: '#FFFFFF',
                    textColor: '#32325d',
                    borderColor: '#FFFFFF',
                    borderWidth: 0,
                    borderRadius: 4,
                    fontSize: 16,
                    placeholderColor: '#aab7c4',
                  }}
                  style={styles.cardField}
                  onCardChange={handleCardChange}
                  onFocus={() => {
                    // Clear error on focus
                    if (cardError && cardError.includes('postal code')) {
                      setCardError('');
                    }
                  }}
                  dangerouslyGetFullCardDetails={false}
                />
              </View>

              {/* Card validation errors (matches Ionic behavior) */}
              <View style={styles.errorContainer}>
                {cardError ? (
                  <Text style={styles.cardErrorText}>{cardError}</Text>
                ) : null}

                {/* Form submission errors */}
                {formError ? (
                  <Text style={styles.errorText}>{formError}</Text>
                ) : null}
              </View>

              <CustomButton
                text="Save card"
                onPress={handleSubmit}
                variant="primary"
                size="large"
                fullWidth={true}
                backgroundColor={Colors.PRIMARY}
                textColor="#fff"
                isBoldText={false}
                disabled={loading || !cardComplete}
              />

              {loading && (
                <View style={styles.loadingOverlay}>
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={Colors.PRIMARY} />
                    <Text style={styles.loadingText}>{loadingMessage}</Text>
                  </View>
                </View>
              )}
            </View>
          </ScreenLayout>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>

    <CustomAlert
      visible={alertVisible}
      title={alertTitle}
      message={alertMessage}
      onDismiss={hideAlert}
      buttons={alertButtons}
    />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.APPBAR_BG_COLOR,
  },
  titleSection: {
    marginBottom: 0,
  },
  formContainer: {
    marginTop: 20,
    padding: 20,
    borderRadius: 0,
  },
  cardLabel: {
    fontSize: Sizes.MEDIUM,
    color: '#fff',
    marginBottom: 12,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  cardFieldContainer: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 4,
    overflow: 'hidden',
    height: 50,
  },
  cardField: {
    width: '100%',
    height: 50,
  },
  errorContainer: {
    minHeight: 24,
    marginBottom: 10,
  },
  cardErrorText: {
    color: '#f49075',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(44, 62, 80, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 0,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: Sizes.MEDIUM,
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
  },
  errorText: {
    color: '#fa755a',
    fontSize: 14,
    marginTop: -15,
    marginBottom: 15,
    fontFamily: Fonts.ROBO_REGULAR,
  },
});

export default EditPaymentPage;
