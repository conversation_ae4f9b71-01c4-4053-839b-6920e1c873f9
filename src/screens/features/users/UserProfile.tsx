import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ActivityIndicator,
  TouchableOpacity
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Colors, Fonts, Sizes, ICONS} from '../../../utils/constants/Theme';
import {AppCommonIcons, AppStrings, RouteNames} from '../../../utils/constants/AppStrings';
import TitleSection from '../../../components/common/TitleSection';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import PaymentCardComponent from '../../../components/cardstyles/PaymentCardComponent';
import CustomCardListStyle from '../../../components/cardstyles/CustomCardListStyle';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CustomAlert from '../../../components/common/CustomAlert';
import database from '@react-native-firebase/database';
import auth from '@react-native-firebase/auth';
import analytics from '@react-native-firebase/analytics';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {LoginManager} from 'react-native-fbsdk-next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
  GC_SIGNIN_PROVIDER_NORMAL,
  GC_SIGNIN_PROVIDER_GOOGLE,
  GC_SIGNIN_PROVIDER_FACEBOOK,
  GC_SIGNIN_PROVIDER_APPLE,
  GC_PROVIDER_APPLE,
} from '../../../utils/globals';
import {
  matchCardIcon,
  getCardBrandName,
} from '../../../utils/cardUtils/cardUtils';
import NetInfo from '@react-native-community/netinfo';

const UserProfile = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [userInformation, setUserInformation] = useState<any>(null);
  const [userVehicleList, setUserVehicleList] = useState<any[]>([]);
  const [customerCards, setCustomerCards] = useState<any>({});
  const [isOnline, setIsOnline] = useState(true);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      trackScreenView();
      fetchUserDetails();
      loadCardDetails();
    };

    checkNetworkAndLoad();
  }, [navigation]);

  const trackScreenView = async () => {
    try {
      await analytics().logScreenView({
        screen_name: 'MyProfile Page',
        screen_class: 'MyProfilePage',
      });
    } catch (error) {
      console.error('Analytics error:', error);
    }
  };

  const fetchUserDetails = async () => {
    setLoading(true);
    try {
      const userId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!userId) {
        navigation.navigate(RouteNames.MCX_NAV_LOGIN_SCREEN as never);
        return;
      }

      const userRef = database().ref(`customer/${userId}`);
      userRef.on('value', snapshot => {
        const userInfo = snapshot.val();
        if (userInfo) {
          const formattedUserInfo = {
            name: `${userInfo['first-name']} ${userInfo['last-name']}`,
            email: userInfo['email'],
            image: userInfo['image'] || null,
            mobile: userInfo['mobile'],
            vehicles: userInfo['myvehicles'] || {},
            providerType: userInfo['provider-type'],
          };

          setUserInformation(formattedUserInfo);
          fetchVehicleDetails(formattedUserInfo.vehicles);
        }
        setLoading(false);
      });
    } catch (error) {
      console.error('Error fetching user details:', error);
      setLoading(false);
    }
  };

  const fetchVehicleDetails = (vehicles: any) => {
    const vehicleArray: any[] = [];
    for (const vehicleId in vehicles) {
      if (vehicles.hasOwnProperty(vehicleId)) {
        const vehicleDetail = vehicles[vehicleId];
        vehicleArray.push({
          name: `${vehicleDetail['make']} ${vehicleDetail['model'] || ''}`,
          'vehicle-id': vehicleId,
          'vehicle-detail': vehicleDetail,
        });
      }
    }
    setUserVehicleList(vehicleArray);
  };

  const loadCardDetails = async () => {
    try {
      const userId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!userId) return;

      const cardRef = database()
        .ref(`saved-cards/${userId}`)
        .orderByChild('status')
        .equalTo('saved');

      cardRef.on('value', snapshot => {
        const cards = snapshot.val();
        setCustomerCards(cards || {});
      });
    } catch (error) {
      console.error('Error loading card details:', error);
    }
  };

  const doYouWantToDeleteCard = (cardKey: string) => {
    showCustomAlert(
      'Delete Card',
      'Are you sure you want to delete card? Note: There is no Undo.',
      [
        {text: 'Cancel', onPress: hideAlert},
        {
          text: 'DELETE',
          onPress: () => {
            hideAlert();
            checkCardAvailability(cardKey);
          },
        },
      ],
    );
  };

  const checkCardAvailability = (cardKey: string) => {
    const cardCount = Object.keys(customerCards).length;
    if (cardCount > 1) {
      deleteCard(cardKey);
    } else {
      showCustomAlert(
        'Add new card',
        'Minimum more than one card required to delete existing card.',
      );
    }
  };

  const deleteCard = async (cardKey: string) => {
    try {
      const userId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      await database().ref(`saved-cards/${userId}/${cardKey}`).remove();
      showCustomAlert('Success', 'Card deleted successfully');
    } catch (error) {
      console.error('Error deleting card:', error);
      showCustomAlert('Error', 'Failed to delete card');
    }
  };

  const fetchPendingAppointments = async (): Promise<boolean> => {
    try {
      const userId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      let hasPending = false;

      const appointmentsSnapshot = await database()
        .ref(`customer/${userId}/appointments`)
        .orderByChild('status')
        .once('value');

      const appointments = appointmentsSnapshot.val();
      if (appointments) {
        for (const key in appointments) {
          if (['new', 'session'].includes(appointments[key].status)) {
            hasPending = true;
            break;
          }
        }
      }

      const requestsSnapshot = await database()
        .ref(`customer/${userId}/work-requests`)
        .orderByChild('status')
        .equalTo('pending')
        .once('value');

      const requests = requestsSnapshot.val();
      if (requests && Object.keys(requests).length > 0) {
        hasPending = true;
      }

      return hasPending;
    } catch (error) {
      console.error('Error checking pending appointments:', error);
      return false;
    }
  };

  const deleteMyAccount = async () => {
    showCustomAlert(
      'DELETE ACCOUNT',
      'Are you sure you want to delete your Account?',
      [
        {text: 'Cancel', onPress: hideAlert},
        {
          text: 'YES',
          onPress: async () => {
            hideAlert();
            const hasPending = await fetchPendingAppointments();
            if (hasPending) {
              showCustomAlert(
                'Warning!',
                'Please cancel or complete your pending requests',
              );
            } else {
              deleteUserData();
            }
          },
        },
      ],
    );
  };

  const deleteUserData = async () => {
    setLoading(true);    
    try {
      const providerType = userInformation?.providerType ||
             await AsyncStorage.getItem(GC_SIGNUP_PROVIDER_TYPE_PREF);

      if (providerType === GC_SIGNIN_PROVIDER_NORMAL) {
        await performAccountDeletion();
        
      } else if (providerType === GC_SIGNIN_PROVIDER_GOOGLE) {
        await revokeGoogleToken();
        await performAccountDeletion();      
      } else if (providerType === GC_SIGNIN_PROVIDER_FACEBOOK) {
        await revokeFacebookToken();
        await performAccountDeletion();        
      } else if (providerType === GC_SIGNIN_PROVIDER_APPLE || providerType === GC_PROVIDER_APPLE) {
        try {
          await revokeAppleToken();
          await performAccountDeletion();
        } catch (appleError: any) {
          await performAccountDeletion();
        }
      } else {
        await performAccountDeletion();
      }

    } catch (error: any) {
      setLoading(false);
      showCustomAlert('Error', error.message || 'Failed to delete account');
    }
  };

  const performAccountDeletion = async () => {
    try {
      await auth().currentUser?.delete();
      await AsyncStorage.setItem('AccountCreateProcess', 'false');
      await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, '');
      setLoading(false);
      
      showCustomAlert(
        'Success',
        'Your request for account delete has been submitted.',
        [
          {
            text: 'OK',
            onPress: async () => {
              hideAlert();
              await auth().signOut();
              navigation.navigate(RouteNames.MCX_NAV_LOGIN_SCREEN as never);
            },
          },
        ],
      );     
    } catch (error: any) {
      setLoading(false);
      showCustomAlert('Error', error.message || 'Failed to delete account');
    }
  };
  const revokeGoogleToken = async () => {
    try {
      await GoogleSignin.revokeAccess();
      await GoogleSignin.signOut();
    } catch (error) {
      console.error('Error revoking Google token:', error);
    }
  };

  const revokeFacebookToken = async () => {
    try {
      await LoginManager.logOut();
    } catch (error) {
      console.error('Error revoking Facebook token:', error);
    }
  };

  const revokeAppleToken = async () => {
    try {
      // For React Native, Apple Sign-In token revocation might be handled automatically
      // during Firebase auth().signOut() - but we keep this for consistency
    } catch (error) {
      throw error; // Re-throw to handle in calling function
    }
  };

  const handleEditProfile = () => {
    (navigation as any).navigate(RouteNames.MCX_NAV_EDIT_PROFILE, {
      isFromSignup: false,
    });
  };

  const handleAddPaymentMethod = () => {
    navigation.navigate(RouteNames.MCX_NAV_EDIT_PAYMENT as never);
  };

  const handleAddVehicles = () => {
    (navigation as any).navigate(RouteNames.MCX_NAV_VEHICLE_EDIT, {
      'vehicle-array': userVehicleList,
    });
  };

  const handleVehiclePress = (vehicleInfo: any) => {
    (navigation as any).navigate(RouteNames.MCX_NAV_VEHICLE_EDIT, {
      'vehicle-info': vehicleInfo,
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
      </View>
    );
  }

  // Fixed bottom content
  const fixedBottomContent = (
    <View style={styles.deleteAccountButton}>
      <Text style={styles.deleteAccountText}>
        {AppStrings.MCX_DELETE_ACCOUNT_TEXT}
      </Text>
      <TouchableOpacity
        style={styles.deleteButtonContainer}
        onPress={deleteMyAccount}>
        <Text style={styles.deleteButtonText}>
          {AppStrings.MCX_DELETE_ACCOUNT_KEYWORD}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={true}
      fixedBottomContent={fixedBottomContent}
      >
      {/* Header */}
      <TitleSection
        title={AppStrings.MCX_USER_PROFILE_Title}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
         onBack={() => (navigation as any).goBack()}
      />

      {/* MY DETAILS Section */}
      <CommonCardStyle
        header={AppStrings.MCX_USER_DETAIL_Text}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_WHITE_SHADE}
        isCardContainerDecorated={true}
        isTitleBordered={true}
        cardBackgroundColor="white">
        {/* User Profile Header */}
        <View style={styles.profileHeader}>
          <Image
            source={
              typeof userInformation?.image === 'string' &&
              userInformation.image.length > 0
                ? {uri: userInformation.image}
                : ICONS.USER_PROFILE_PIC || AppCommonIcons.MCX_USER_PROFILE_PIC
            }
            style={styles.profileAvatar}
          />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{userInformation?.name}</Text>
            <Text style={styles.profileEmail}>{userInformation?.email}</Text>
          </View>
        </View>
        <HorizontalDivider />

        {/* Phone Number Row */}
        <CustomCardListStyle
          title={userInformation?.mobile}
          rightText={AppStrings.MCX_EDIT_PROFILE_DETAILS_TEXT}
          rightElementType="image"
          rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
          rightIconStyle={styles.cardIconStyle}
          onPress={handleEditProfile}
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
          subtitleColor={Colors.COMMON_GREY_SHADE_LIGHT}
          showHorizontalLine={true}
          horizontalLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          rightTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />

        {/* Add Payment Method Row */}
        <CustomCardListStyle
          title={AppStrings.MCX_ADD_PAYMENT_METHOD_TEXT}
          rightElementType="image"
          rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
          rightIconStyle={styles.cardIconStyle}
          onPress={handleAddPaymentMethod}
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />

        {/* Payment Cards */}
        {Object.keys(customerCards).length > 0 && (
          <>
            <HorizontalDivider />
            {Object.keys(customerCards).map((cardKey, index) => {
              const card = customerCards[cardKey];
              const resolvedBrand =
                card?.brand ||
                card?.cards?.brand ||
                card?.token?.card?.brand ||
                '';
              const last4 =
                card?.last4 ||
                card?.cards?.last4 ||
                card?.token?.card?.last4 ||
                '';

              return (
                <React.Fragment key={cardKey}>
                  <PaymentCardComponent
                    card={{
                      type: getCardBrandName(resolvedBrand),
                      maskedNumber: `**** **** **** ${last4}`,
                      brandShort: resolvedBrand,
                    }}
                    onDelete={() => doYouWantToDeleteCard(cardKey)}
                  />
                  {index < Object.keys(customerCards).length - 1 && (
                    <HorizontalDivider />
                  )}
                </React.Fragment>
              );
            })}
          </>
        )}
      </CommonCardStyle>

      {/* MY VEHICLES Section */}
      <CommonCardStyle
        header={AppStrings.MCX_MY_VEHICLE_TEXT}
        headerColor={Colors.SECONDARY}
        textColor={Colors.COMMON_GREY_SHADE_LIGHT}
        isCardContainerDecorated={true}
        isTitleBordered={true}
        cardBackgroundColor="white">
        {/* Vehicle List */}
        {userVehicleList && userVehicleList.length > 0 && (
          <>
            {userVehicleList.map((vehicle, index) => (
              <React.Fragment key={vehicle['vehicle-id']}>
                <CustomCardListStyle
                  title={vehicle.name}
                  rightText="Edit"
                  rightElementType="image"
                  rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
                  rightIconStyle={styles.cardIconStyle}
                  onPress={() => handleVehiclePress(vehicle)}
                  textColor={Colors.COMMON_GREY_SHADE_LIGHT}
                  rightTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
                />
                {index < userVehicleList.length - 1 && <HorizontalDivider />}
              </React.Fragment>
            ))}
            <HorizontalDivider />
          </>
        )}

        <CustomCardListStyle
          title={AppStrings.MCX_USER_PROFILE_ADD_VEHICLES}
          rightElementType="image"
          rightIcon={AppCommonIcons.MCX_ARROW_RIGHT}
          rightIconStyle={styles.cardIconStyle}
          onPress={handleAddVehicles}
          textColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />
      </CommonCardStyle>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  // Profile Header Styles
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 8,
  },
  profileAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: Sizes.XLARGE,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: 'bold',
    color: Colors.SECONDARY,
  },
  profileEmail: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.PRIMARY,
  },
  cardIconStyle: {
    width: 20,
    height: 20,
    tintColor: Colors.PRIMARY,
  },
  deleteAccountButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  deleteAccountText: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
  },
  deleteButtonContainer: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  deleteButtonText: {
    fontSize: Sizes.SMALL,
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default UserProfile;
