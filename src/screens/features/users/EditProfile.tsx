import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  TextInput,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
} from 'react-native';
import { request, check, PERMISSIONS, RESULTS } from 'react-native-permissions';
import DateTimePicker from '@react-native-community/datetimepicker';
import Modal from 'react-native-modal';
import {launchImageLibrary} from 'react-native-image-picker';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import Geolocation from 'react-native-geolocation-service';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CommonDropdown from '../../../components/common/CommonDropdown';
import CustomButton from '../../../components/common/CustomButton';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import UseAddressPopupPage from '../../../components/common/UseAddressPopupPage';
import CustomAlert from '../../../components/common/CustomAlert';
import {
  Colors,
  Fonts,
  Sizes,
  CommonUIParams,
} from '../../../utils/constants/Theme';
import {wp, hp} from '../../../utils/ResponsiveParams';
import {AppCommonIcons, AppStrings, RouteNames} from '../../../utils/constants/AppStrings';
import {ERROR_MESSAGE_FOR_SIGNUP} from '../../../utils/constants/ErrorMessages';
import {useNavigation} from '@react-navigation/native';
import {GooglePlacesAutocompleteDefaultProps} from '../../../utils/configs/GooglePlacesAutocompleteProps';
import Config from 'react-native-config';
import database from '@react-native-firebase/database';
import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import axios from 'axios';
import AppBar from '../../../components/common/AppBar';
import {VehicleService} from '../../../utils/services/VehicleService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from '../../../utils/configs/AuthContext';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const EditProfile = () => {
  const navigation = useNavigation();
  const route = useNavigation().getState()?.routes?.find(r => r.name === 'EditProfile');
  const isFromSignup = (route?.params as any)?.isFromSignup || false;
  const autocompleteRef = useRef<any>(null);
  const screenLayoutRef = useRef<ScrollView>(null);
  const {user} = useAuth();

  const handleMailPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_MESSAGES as never);
  };
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [gender, setGender] = useState('');
  const [maritalStatus, setMaritalStatus] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [hasLoadedProfile, setHasLoadedProfile] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerValue, setDatePickerValue] = useState(new Date(1982, 0, 1));
  const [tempDate, setTempDate] = useState(new Date());
  const [address1, setAddress1] = useState('');
  const [address2, setAddress2] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [country, setCountry] = useState('');
  const [coordinates, setCoordinates] = useState<number[]>([]);
  const [locationInput, setLocationInput] = useState('');
  const [customerProfilePic, setCustomerProfilePic] = useState('');
  const [uploadImage, setUploadImage] = useState<any>(null);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  // const [selectedLocationType, setSelectedLocationType] = useState<
  //   'currentlocation' | 'savedlocation' | 'searchlocation'
  // >('currentlocation');
  const [selectedLocationType, setSelectedLocationType] = useState<
    'currentlocation' | 'savedlocation' | 'searchlocation'
  >('searchlocation');
  const [initialLocationData, setInitialLocationData] = useState<any>(null);
  const [customerRequestAdrress, setCustomerRequestAdrress] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [showAddressPopup, setShowAddressPopup] = useState(false);
  const [pendingPlace, setPendingPlace] = useState<any>(null);
  const [googlePlacesFocused, setGooglePlacesFocused] = useState(false);
  const [searchY, setSearchY] = useState(0);
  const searchContainerRef = useRef<View>(null);
  // const [savedLocations, setSavedLocations] = useState<any[]>([]);
  // const [locationSaved, setLocationSaved] = useState(false);
  // const [selectedSavedLocation, setSelectedSavedLocation] = useState('');
  const [pendingLocation, setPendingLocation] = useState<any>(null);
  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

    const processLocation = useCallback(async (latitude: number, longitude: number) => {
      try {
        const response = await axios.get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`,
        );
  
        if (response.data.results.length > 0) {
          const result = response.data.results[0];
          const parsedAddress = parseAddressComponents(
            result.address_components || [],
            result.formatted_address || '',
          );
  
          const locationData = {
            address_array: parsedAddress,
            formatted_address: result.formatted_address,
            location: {latitude, longitude},
          };
  
          setInitialLocationData(locationData);
          setCustomerRequestAdrress(locationData);
          setCoordinates([latitude, longitude]);
        }
      } catch (error) {
        console.error('Error in processLocation:', error);
      }
    }, []);
  
    const processExistingCoordinates = useCallback(async (
      latitude: number,
      longitude: number,
      profileInfo: any,
    ) => {
      try {
        const response = await axios.get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`,
        );
  
        if (response.data.results.length > 0) {
          const result = response.data.results[0];
          const parsedAddress = parseAddressComponents(
            result.address_components || [],
            result.formatted_address || '',
          );
  
          const locationData = {
            address_array: parsedAddress,
            formatted_address: result.formatted_address,
            location: {latitude, longitude},
          };
  
          setInitialLocationData(locationData);
          setCustomerRequestAdrress(locationData);
          setCoordinates([latitude, longitude]);
        }
      } catch (error) {
        console.error('Error in processExistingCoordinates:', error);
      }
    }, []);

  useEffect(() => {
    if (googlePlacesFocused && searchY > 0) {
      setTimeout(() => {
        screenLayoutRef.current?.scrollTo({ x: 0, y: searchY, animated: true });
        setGooglePlacesFocused(false);
      }, 100);
    }
  }, [googlePlacesFocused, searchY]);

  const loadProfileData = useCallback(async () => {
    try {
      const customerId = auth().currentUser?.uid;
      if (!customerId) {
        return;
      }

      const profileRef = database().ref('customer').child(customerId);

      profileRef.on(
        'value',
        snapshot => {
          const profileInfo = snapshot.val();
          if (profileInfo) {
            const imageUrl = profileInfo['image'];
            setCustomerProfilePic(imageUrl || '');
            const locationData = profileInfo['location'];
            if (locationData && typeof locationData === 'object') {
              const lat = locationData['latitude'];
              const lng = locationData['longitude'];
              if (typeof lat === 'number' && typeof lng === 'number') {
                const coords = [lat, lng];
                setCoordinates(coords);
                processExistingCoordinates(lat, lng, profileInfo);
              } else {
                setCoordinates([]);
              }
            } else {
              setCoordinates([]);
            }
            const fields = {
              'first-name': profileInfo['first-name'],
              'last-name': profileInfo['last-name'],
              email: profileInfo['email'],
              mobile: profileInfo['mobile'],
              gender: profileInfo['gender'],
              maritalstatus: profileInfo['maritalstatus'],
              dateofbirth: profileInfo['dateofbirth'],
              address1: profileInfo['address1'],
              address2: profileInfo['address2'],
              city: profileInfo['city'],
              state: profileInfo['state'],
              zipcode: profileInfo['zipcode'],
              country: profileInfo['country'],
            };

            setFirstName(fields['first-name'] || '');
            setLastName(fields['last-name'] || '');
            setEmail(fields['email'] || '');
            setMobile(fields['mobile'] || '');
            setGender(fields['gender'] || '');
            setMaritalStatus(fields['maritalstatus'] || '');
            setDateOfBirth(fields['dateofbirth'] || '');
            // Parse dateOfBirth and set datePickerValue
            if (fields['dateofbirth']) {
              const dateParts = fields['dateofbirth'].split('/');
              if (dateParts.length === 3) {
                const month = parseInt(dateParts[0], 10) - 1; // Month is 0-based
                const day = parseInt(dateParts[1], 10);
                const year = parseInt(dateParts[2], 10);
                setDatePickerValue(new Date(year, month, day));
              }
            }
            setAddress1(fields['address1'] || '');
            setAddress2(fields['address2'] || '');
            setCity(fields['city'] || '');
            setState(fields['state'] || '');
            setZipCode(fields['zipcode'] || '');
            setCountry(fields['country'] || '');
            setHasLoadedProfile(true);
          }
        },
        error => {
          console.error('EditProfile: Firebase on value error:', error);
        },
      );
    } catch (error) {
      console.error('EditProfile: Error loading profile:', error);
    }
  }, [processExistingCoordinates]);

  const checkNetworkAndLoadProfile = useCallback(async () => {
    const isOnline = true;
    if (!isOnline) {
      navigation.navigate('NetworkFailedPage' as never);
      return;
    }
    loadProfileData();
  }, [loadProfileData, navigation]);

  useEffect(() => {
    checkNetworkAndLoadProfile();
  }, [checkNetworkAndLoadProfile]);

  // useEffect(() => {
  //   const loadCurrentLocation = async () => {
  //     try {
  //       await requestCurrentLocation();
  //     } catch (error) {
  //       console.error('Error loading current location:', error);
  //     }
  //   };
  //   loadCurrentLocation();
  // }, []);


  // const fetchSavedLocations = useCallback(async () => {
  //   try {
  //     if (user?.uid) {
  //       const locations = await VehicleService.fetchSavedLocations(user.uid);
  //       setSavedLocations(locations);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching saved locations:', error);
  //     setSavedLocations([]);
  //   }
  // }, [user?.uid]);

  // useEffect(() => {
  //   if (user?.uid) {
  //     fetchSavedLocations();
  //   }
  // }, [user?.uid, fetchSavedLocations]);


  // Dropdown options
  const genderOptions = [
    {label: 'Male', value: 'Male'},
    {label: 'Female', value: 'Female'},
    {label: 'Other', value: 'Other'},
  ];

  const maritalStatusOptions = [
    {label: 'Single', value: 'Single'},
    {label: 'Married', value: 'Married'},
    {label: 'Divorced', value: 'Divorced'},
    {label: 'Widowed', value: 'Widowed'},
  ];

  const formatDateDDMMYYYY = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}/${month}/${year}`;
  };

  const parseDateDDMMYYYY = (dateString: string) => {
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1;
      const year = parseInt(parts[2], 10);
      return new Date(year, month, day);
    }
    return new Date();
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setDatePickerValue(selectedDate);
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const year = selectedDate.getFullYear().toString();
      const formattedDate = `${month}/${day}/${year}`;
      setDateOfBirth(formattedDate);

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    } else if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
  };

  const handleChangePhoto = () => {
    const options = {
      mediaType: 'photo' as const,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel) {
      } else if (response.errorMessage) {
        console.error('ImagePicker Error:', response.errorMessage);
        showCustomAlert('Error', 'Failed to pick image');
      } else if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        const source = asset.uri;
        setCustomerProfilePic(source || '');
        setUploadImage(response);
        updateProfileImage(response);
      }
    });
  };

  const updateProfileImage = (result: any) => {
    setUploadImage(result);
    uploadingImage(result);
  };

  const uploadingImage = async (imageResult: any) => {
    try {
      const customerId = auth().currentUser?.uid;
      if (
        !customerId ||
        !imageResult ||
        !imageResult.assets ||
        !imageResult.assets[0]
      ) {
        return;
      }

      const asset = imageResult.assets[0];
      const storagePath = `customerProfilePic/${customerId}`;
      const ref = storage().ref(storagePath);

      const uploadMetadata = {
        contentType: asset.type || 'image/jpeg',
      };
      const uploadResult = await ref.putFile(asset.uri, uploadMetadata);
      if (uploadResult.state === 'success') {
        const url = await ref.getDownloadURL();
        await uploadingProfileImagePath(url);
      }
    } catch (error) {
      console.error('EditProfile: Error uploading image:', error);
      showCustomAlert('Error', 'Failed to upload image');
    }
  };

  const uploadingProfileImagePath = async (url: string) => {
    try {
      const customerId = auth().currentUser?.uid;
      if (!customerId) {
        return;
      }

      const imageRef = database()
        .ref('customer')
        .child(customerId)
        .child('image');

      await imageRef.set(url);
      setCustomerProfilePic(url);
    } catch (error) {
      console.error('EditProfile: Error updating profile image path:', error);
    }
  };

  const formatMobileNumber = (text: string) => {
    const x = text.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/);
    if (x) {
      const formatted = !x[2]
        ? x[1]
        : '(' + x[1] + ') ' + x[2] + (x[3] ? '-' + x[3] : '');
      setMobile(formatted);
    }
  };

  const parseAddressComponents = (
    addressComponents: any[],
    formattedAddress: string,
  ) => {
    const addressData = {
      address1: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      zipcode: '',
    };

    if (addressComponents && addressComponents.length > 0) {
      addressComponents.forEach((component: any) => {
        const types = component.types;
        if (
          types.includes('locality') ||
          types.includes('administrative_area_level_2')
        ) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressData.state = component.long_name;
        } else if (types.includes('country')) {
          addressData.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressData.zipcode = component.long_name;
        }
      });
    }
    if (!addressData.address1 && formattedAddress) {
      const parts = formattedAddress.split(',');
      addressData.address1 = parts[0]?.trim() || '';
      if (parts.length > 1 && !addressData.city) {
        addressData.city = parts[1]?.trim() || '';
      }
      if (parts.length > 2 && !addressData.state) {
        addressData.state = parts[2]?.trim() || '';
      }
    }

    return addressData;
  };

  const handleLocationChange = useCallback(
    (locationData: any) => {
      if (!hasLoadedProfile) {
        console.log('Ignoring location change - profile not loaded yet');
        return;
      }

      console.log('Applying location change:', locationData);
      setSelectedLocation(locationData);
      if (locationData) {
        setCity(locationData.address_array.city);
        setState(locationData.address_array.state);
        setCountry(locationData.address_array.country);
        setZipCode(locationData.address_array.zipcode);
        setCoordinates([
          locationData.location.latitude,
          locationData.location.longitude,
        ]);
      }
    },
    [hasLoadedProfile],
  );



  const requestCurrentLocation = useCallback(async () => {
    try {
      const permission = Platform.OS === 'ios' ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
      let status = await check(permission);
      if (status !== RESULTS.GRANTED) {
        status = await request(permission);
      }
      if (status === RESULTS.GRANTED) {
        Geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords;
            if (user?.uid) {
              try {
                await VehicleService.updateCustomerLocation(user.uid, { latitude, longitude });
              } catch (error) {
                console.error('Error updating location in DB:', error);
              }
            }
            await processLocation(latitude, longitude);
          },
          (error) => {
            console.error('Geolocation error:', error);
            showCustomAlert('Error', 'Unable to get current location');
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      } else {
        showCustomAlert('Permission Denied', 'Location permission is required');
      }
    } catch (error) {
      console.error('Location permission error:', error);
    }
  }, [user?.uid, processLocation]);
  // const handleLocationTypeChange = useCallback(
  //   (type: 'currentlocation' | 'savedlocation' | 'searchlocation') => {
  //     setSelectedLocationType(type);
  //     setLocationSaved(false);
  //     setCustomerRequestAdrress(null);
  //     if (autocompleteRef.current) {
  //       autocompleteRef.current.setAddressText('');
  //     }
      
  //     if (type === 'currentlocation') {
  //       requestCurrentLocation();
  //     }
  //   },
  //   [requestCurrentLocation],
  // );
// useEffect(() => {
//   if (selectedSavedLocation && selectedLocationType === 'savedlocation') {
//     const location = savedLocations.find(l => l.id === selectedSavedLocation);
//     if (location) {
//       const addressParts = location.address
//         .split(',')
//         .map((part: string) => part.trim());
      
//       setCustomerRequestAdrress({
//         address_array: {
//           address1: addressParts[0] || '',
//           address2: '',
//           city: addressParts[1] || '',
//           state: addressParts[2] || '',
//           country: addressParts[3] || 'India',
//           zipcode: '',
//         },
//         formatted_address: location.address,
//         location: location.coordinates,
//       });
      
//       setCoordinates([location.coordinates.latitude, location.coordinates.longitude]);
//       _setSelectedLocation(location.coordinates);
      
//       // Auto-fill address fields
//       setAddress1(addressParts[0] || '');
//       setCity(addressParts[1] || '');
//       setState(addressParts[2] || '');
//       setCountry(addressParts[3] || 'India');
//     }
//   }
// }, [selectedSavedLocation, savedLocations, selectedLocationType]);
  const requestSavedLocation = () => {
    // No alert needed - dropdown will show placeholder when empty
  };

  const _setSelectedLocation = (location: any) => {
    setSelectedLocation(location);
  };
  const processPlaces = (place: any) => {
    let place_address = place.address_components;
    let tempAddress: any = {};

    if (place_address && place_address.length > 0) {
      place_address.forEach((component: any) => {
        const types = component.types;

        if (types.includes('premise')) {
          tempAddress['premise'] = component.long_name;
        }
        if (types.includes('route')) {
          tempAddress['route'] = component.long_name;
        }
        if (types.includes('sublocality_level_1')) {
          tempAddress['sublocality'] = component.long_name;
        }
        if (types.includes('locality')) {
          tempAddress['locality'] = component.long_name;
        }
        if (types.includes('administrative_area_level_3')) {
          tempAddress['city'] = component.long_name;
        }
        if (types.includes('administrative_area_level_1')) {
          tempAddress['state'] = component.long_name;
        }
        if (types.includes('country')) {
          tempAddress['country'] = component.long_name;
        }
        if (types.includes('postal_code')) {
          tempAddress['postal-code'] = component.long_name;
        }
      });
    }

    prepareAddress(tempAddress);
  };

  const prepareAddress = (address: any) => {
    setZipCode('');
    setCountry('');
    setState('');
    setCity('');
    setCoordinates([]);
    setZipCode(address['postal-code'] || '');
    setCountry(address['country'] || '');
    setState(address['state'] || '');
    setCity(address['city'] || '');
    setCoordinates(coordinates); 
  };
useEffect(() => {
  if (firstName) {
    setErrors(prev => ({ ...prev, firstName: '' }));
  }
}, [firstName]);

useEffect(() => {
  if (lastName) {
    setErrors(prev => ({ ...prev, lastName: '' }));
  }
}, [lastName]);

useEffect(() => {
  if (mobile) {
    setErrors(prev => ({ ...prev, mobile: '' }));
  }
}, [mobile]);

useEffect(() => {
  if (gender) {
    setErrors(prev => ({ ...prev, gender: '' }));
  }
}, [gender]);

useEffect(() => {
  if (maritalStatus) {
    setErrors(prev => ({ ...prev, maritalStatus: '' }));
  }
}, [maritalStatus]);

useEffect(() => {
  if (dateOfBirth) {
    setErrors(prev => ({ ...prev, dateOfBirth: '' }));
  }
}, [dateOfBirth]);

useEffect(() => {
  if (address1) {
    setErrors(prev => ({ ...prev, address1: '' }));
  }
}, [address1]);

useEffect(() => {
  if (address2) {
    setErrors(prev => ({ ...prev, address2: '' }));
  }
}, [address2]);

useEffect(() => {
  if (city) {
    setErrors(prev => ({ ...prev, city: '' }));
  }
}, [city]);

useEffect(() => {
  if (state) {
    setErrors(prev => ({ ...prev, state: '' }));
  }
}, [state]);

useEffect(() => {
  if (zipCode) {
    setErrors(prev => ({ ...prev, zipCode: '' }));
  }
}, [zipCode]);

useEffect(() => {
  if (country) {
    setErrors(prev => ({ ...prev, country: '' }));
  }
}, [country]);

// Clear location-related errors when coordinates are set
useEffect(() => {
  if (coordinates?.length === 2) {
    setErrors(prev => ({ ...prev, coordinates: '' }));
  }
}, [coordinates]);
  // const doYouWantToUseAddress = (place: any) => {
  //   setPendingPlace(place);
  //   setShowAddressPopup(true);
  // };
  const doYouWantToUseAddress = (place: any) => {
  setPendingLocation(place);
  setShowAddressPopup(true);
};
// const handleAddressConfirm = () => {
//   if (pendingPlace) {
//     setLocationSaved(false);
//     const parsedAddress = parseAddressComponents(
//       pendingPlace.address_components || [],
//       pendingPlace.formatted_address || '',
//     );
    
//     const lat = pendingPlace.geometry.location.lat;
//     const lng = pendingPlace.geometry.location.lng;
    
//     // Store the selected location data
//     setCustomerRequestAdrress({
//       address_array: parsedAddress,
//       formatted_address: pendingPlace.formatted_address,
//       location: {
//         latitude: lat,
//         longitude: lng,
//       },
//     });

//     // Update coordinates
//     setCoordinates([lat, lng]);
//     setSelectedLocation({
//       latitude: lat,
//       longitude: lng,
//     });

//     // Always update address fields with new location data
//     setAddress1(parsedAddress.address1 || '');
//     setAddress2(parsedAddress.address2 || '');
//     setCity(parsedAddress.city || '');
//     setState(parsedAddress.state || '');
//     setZipCode(parsedAddress.zipcode || '');
//     setCountry(parsedAddress.country || 'India');
//   }
  
//   setShowAddressPopup(false);
//   setPendingPlace(null);

//   // Clear the search input
//   if (autocompleteRef.current) {
//     autocompleteRef.current.setAddressText('');
//   }
// };
 const handleAddressConfirm = () => {
  if (pendingLocation) {
   // setLocationSaved(false);
    
    // Clear all address fields first
    setAddress1('');
    setAddress2('');
    setCity('');
    setState('');
    setCountry('');
    setZipCode('');

    // Extract address components
    const addressComponents = pendingLocation.address_components || [];
    let city = '';
    let state = '';
    let country = '';
    let zipcode = '';
    let address1 = '';

    addressComponents.forEach((component: any) => {
      const types = component.types;
      if (types.includes('street_number') || types.includes('route')) {
        if (types.includes('street_number')) {
          address1 = component.long_name + ' ';
        } else if (types.includes('route')) {
          address1 += component.long_name;
        }
      } else if (
        types.includes('locality') ||
        types.includes('administrative_area_level_2')
      ) {
        city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        state = component.long_name;
      } else if (types.includes('country')) {
        country = component.long_name;
      } else if (types.includes('postal_code')) {
        zipcode = component.long_name;
      }
    });

    // If no street address found, use first part of formatted address
    if (!address1 && pendingLocation.formatted_address) {
      const parts = pendingLocation.formatted_address.split(',');
      address1 = parts[0]?.trim() || '';
    }

    // Extract coordinates
    const lat = pendingLocation.geometry.location.lat;
    const lng = pendingLocation.geometry.location.lng;

    // Prepare location data
    const parsedAddress = {
      address1: address1,
      address2: '',
      city: city,
      state: state,
      country: country,
      zipcode: zipcode,
    };

    const locationData = {
      address_array: parsedAddress,
      formatted_address: pendingLocation.formatted_address,
      location: {
        latitude: lat,
        longitude: lng,
      },
    };

    // Update all location-related state
    setCustomerRequestAdrress(locationData);
    setCoordinates([lat, lng]);
    setSelectedLocation({
      latitude: lat,
      longitude: lng,
    });

    // Update address fields
    if (address1) setAddress1(address1);
    if (city) setCity(city);
    if (state) setState(state);
    if (country) setCountry(country);
    if (zipcode) setZipCode(zipcode);
    setAddress2(''); // Explicitly clear address2

    console.log('Auto-filled address:', {
      address1,
      address2: '',
      city,
      state,
      country,
      zipcode,
      coordinates: [lat, lng],
    });
  }

  setShowAddressPopup(false);
  setPendingLocation(null);

  // Clear the search input
  if (autocompleteRef.current) {
    autocompleteRef.current.setAddressText('');
  }
};
const handleAddressCancel = () => {
    setShowAddressPopup(false);
    setPendingPlace(null);
    // Don't clear any fields - just close the popup
  };

  /**
   * Focus to next input
   * @param event key enter value
   * @param nextRef next focus ref
   */
  const focusNext = (event: any, nextRef: React.RefObject<TextInput>) => {
    if (nextRef && nextRef.current) {
      nextRef.current.focus();
    }
  };

  const handleSaveProfile = async () => {
    // Validate
    const newErrors: any = {};

    // Use ERROR_MESSAGE_FOR_SIGNUP structure
    if (!firstName)
      newErrors.firstName = ERROR_MESSAGE_FOR_SIGNUP.firstname[0].message;
    if (!lastName)
      newErrors.lastName = ERROR_MESSAGE_FOR_SIGNUP.lastname[0].message;
    if (!email) newErrors.email = ERROR_MESSAGE_FOR_SIGNUP.email[0].message;
    if (!mobile) newErrors.mobile = ERROR_MESSAGE_FOR_SIGNUP.mobile[0].message;
    if (!gender) newErrors.gender = ERROR_MESSAGE_FOR_SIGNUP.gender[0].message;
    if (!maritalStatus)
      newErrors.maritalStatus =
        ERROR_MESSAGE_FOR_SIGNUP.maritalstatus[0].message;
    if (!dateOfBirth)
      newErrors.dateOfBirth = ERROR_MESSAGE_FOR_SIGNUP.dateofbirth[0].message;
    if (!coordinates || coordinates.length === 0)
      newErrors.coordinates = ERROR_MESSAGE_FOR_SIGNUP.coordinates[0].message;
    if (!address1)
      newErrors.address1 = ERROR_MESSAGE_FOR_SIGNUP.address1[0].message;
    // if (!address2)
    //   newErrors.address2 = ERROR_MESSAGE_FOR_SIGNUP.address2[0].message;
    if (!city) newErrors.city = ERROR_MESSAGE_FOR_SIGNUP.city[0].message;
    if (!state) newErrors.state = ERROR_MESSAGE_FOR_SIGNUP.state[0].message;
    if (!zipCode)
      newErrors.zipCode = ERROR_MESSAGE_FOR_SIGNUP.zipcode[0].message;
    if (!country)
      newErrors.country = ERROR_MESSAGE_FOR_SIGNUP.country[0].message;

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      showCustomAlert('Validation Error', 'Please fill all required fields');
      return;
    }

    setLoading(true);
    try {
      const customerId = auth().currentUser?.uid;
      if (!customerId) {
        return;
      }

      const profile = {
        email: email,
        'first-name': firstName,
        'last-name': lastName,
        mobile: mobile,
        location: {
          latitude: coordinates[0] || 0,
          longitude: coordinates[1] || 0,
        },
        country: country,
        city: city,
        address1: address1,
        address2: address2,
        gender: gender,
        dateofbirth: dateOfBirth,
        maritalstatus: maritalStatus,
        state: state,
        zipcode: zipCode,
      };
      const updateRef = database().ref('customer').child(customerId);
      const result = await updateRef.update(profile);
      showCustomAlert('Success', 'Profile updated successfully', [
        {text: 'OK', onPress: () => {
          hideAlert();
          if (isFromSignup) {
            navigation.navigate(RouteNames.MCX_NAV_DashBoard as never);
          } else {
            navigation.goBack();
          }
        }},
      ]);
    } catch (error: any) {
      console.error('EditProfile: Error saving profile:', error);
      showCustomAlert('Error', error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const renderSearchAutocomplete = (onSelect: (locationData: any) => void) => (
   <GooglePlacesAutocomplete
  ref={autocompleteRef}
  {...GooglePlacesAutocompleteDefaultProps}
  placeholder="Search for a location"
  textInputProps={{
    placeholderTextColor: '#6B7280',
  }}
  minLength={2}
  fetchDetails={true}
  onPress={(data, details = null) => {
    if (details) {
      setPendingPlace(details);
      setShowAddressPopup(true);
      // Clear the input immediately after selection
      setTimeout(() => {
        if (autocompleteRef.current) {
          autocompleteRef.current.setAddressText('');
        }
      }, 100);
    }
  }}
  query={{
    key: Config.GOOGLE_PLACES_API_KEY,
    language: 'en',
    components: 'country:in',
  }}
  debounce={300}
  styles={{
    container: {flex: 0, zIndex: 1000},
    textInput: styles.locationInput,
    textInputContainer: styles.locationInputContainer,
    listView: styles.locationListView,
    row: styles.locationRow,
    description: styles.locationDescription,
  }}
  enablePoweredByContainer={false}
  keyboardShouldPersistTaps="handled"
/>
  );
   const handleBackPress = async () => {
     if (isFromSignup) {
            navigation.navigate(RouteNames.MCX_NAV_DashBoard as never);
          } else {
            navigation.goBack();
          }
  };
  return (
    <SafeAreaView style={[styles.mainContainer, {paddingTop: insets.top}]}>
      <AppBar
        showBackButton={true}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onBackPress={handleBackPress}
      />
      <TitleSection
        title={AppStrings.MCX_EDIT_PROFILE_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <KeyboardAvoidingView
        style={styles.flex}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <ScreenLayout
          ref={screenLayoutRef}
          useScrollView={true}
          useImageBackground={true}
          centerContent={false}
          useHorizontalPadding={true}
          scrollContainerStyle={StyleSheet.flatten([
            styles.scrollSpace,
            {paddingBottom: Platform.OS === 'ios' ? insets.bottom * 3 : Math.max(insets.bottom, 24) + 160},
          ])}
          keyboardShouldPersistTaps="handled"
          fixedBottomContent={
            <CustomButton
              text={AppStrings.MCX_SAVE_MY_PROFILE_BUTTON}
              onPress={handleSaveProfile}
              variant="primary"
              size="large"
              fullWidth={true}
              backgroundColor={Colors.SECONDARY}
              textColor="#fff"
              isBoldText={true}
              isBottomButton={true}
              bottomLineWidth={1}
              bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
              disabled={loading}
            />
          }
          fixedBottomStyle={{paddingBottom: Math.max(insets.bottom, 16)}}
        >
        <View style={styles.formContainer}>
          <View style={styles.profilePhotoSection}>
            <Image
              source={
                customerProfilePic
                  ? {uri: customerProfilePic}
                  : AppCommonIcons.MCX_USER_PROFILE_PIC
              }
              style={styles.profilePhoto}
            />
            <TouchableOpacity
              style={styles.changePhotoButton}
              onPress={handleChangePhoto}>
              <Text style={styles.changePhotoText}>
                {AppStrings.MCX_CHANGE_PHOTO_TEXT}
              </Text>
              <Image
                source={AppCommonIcons.MCX_ARROW_RIGHT}
                style={styles.changePhotoIcon}
              />
            </TouchableOpacity>
          </View>
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_FIRST_NAME_LABEL}
          </Text>
          <CommonTextInput
            value={firstName}
            onChangeText={setFirstName}
            placeholder={AppStrings.MCX_FIRST_NAME_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.firstName && (
            <Text style={styles.errorText}>{errors.firstName}</Text>
          )}

          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_LAST_NAME_LABEL}
          </Text>
          <CommonTextInput
            value={lastName}
            onChangeText={setLastName}
            placeholder={AppStrings.MCX_LAST_NAME_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.lastName && (
            <Text style={styles.errorText}>{errors.lastName}</Text>
          )}

          <Text style={styles.fieldLabel}>{AppStrings.MCX_EMAIL_LABEL}</Text>
          <CommonTextInput
            value={email}
            onChangeText={setEmail}
            placeholder={AppStrings.MCX_EMAIL_PLACEHOLDER}
            keyboardType="email-address"
            style={[styles.textInput, styles.emailInput]}
            placeholderTextColor="#cac2c2ff"
            fontSize={Sizes.LARGE}
            disabled={true}
          />
          {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}

          <Text style={styles.fieldLabel}>{AppStrings.MCX_MOBILE_LABEL}</Text>
          <CommonTextInput
            value={mobile}
            onChangeText={text => {
              setMobile(text);
              formatMobileNumber(text);
            }}
            placeholder={AppStrings.MCX_MOBILE_PLACEHOLDER}
            keyboardType="phone-pad"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.mobile && (
            <Text style={styles.errorText}>{errors.mobile}</Text>
          )}
          <HorizontalDivider isFullWidth={true} />
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_GENDER_LABEL}
          </Text>
          <CommonDropdown
            data={genderOptions}
            value={gender}
            onValueChange={setGender}
            placeholder={AppStrings.MCX_GENDER_PLACEHOLDER}
            style={styles.dropdown}
            fontWeight="800"
          />
          {errors.gender && (
            <Text style={styles.errorText}>{errors.gender}</Text>
          )}
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_MARITAL_STATUS_LABEL}
          </Text>
          <CommonDropdown
            data={maritalStatusOptions}
            value={maritalStatus}
            onValueChange={setMaritalStatus}
            placeholder={AppStrings.MCX_MARITAL_STATUS_PLACEHOLDER}
            style={styles.dropdown}
            fontWeight="800"
          />
          {errors.maritalStatus && (
            <Text style={styles.errorText}>{errors.maritalStatus}</Text>
          )}
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_DATE_OF_BIRTH_LABEL}
          </Text>
          <TouchableOpacity
            style={styles.dateInputContainer}
            onPress={() => {
              setTempDate(dateOfBirth ? parseDateDDMMYYYY(dateOfBirth) : new Date());
              setShowDatePicker(true);
            }}
            activeOpacity={0.7}
          >
            <TextInput
              style={[styles.input, styles.disabledInput]}
              value={dateOfBirth || ''}
              editable={false}
              placeholder="Select Date"
              placeholderTextColor="#888"
              pointerEvents="none"
            />
          </TouchableOpacity>
          {errors.dateOfBirth && (
            <Text style={styles.errorText}>{errors.dateOfBirth}</Text>
          )}
          {/* <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_MY_LOCATION_TEXT}
            </Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={styles.radioOption}
                onPress={() => {
                  handleLocationTypeChange('currentlocation');
                }}>
                <Text style={styles.optionText}>
                  {AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}
                </Text>
                <View
                  style={[
                    styles.radioCircle,
                    selectedLocationType === 'currentlocation' && styles.radioCircleSelected,
                  ]}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.radioOption}
                onPress={() => {
                  handleLocationTypeChange('savedlocation');
                }}>
                <Text style={styles.optionText}>
                  {AppStrings.MCX_SAVED_LOCATION_TEXT}
                </Text>
                <View
                  style={[
                    styles.radioCircle,
                    selectedLocationType === 'savedlocation' && styles.radioCircleSelected,
                  ]}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.radioOption}
                onPress={() => {
                  handleLocationTypeChange('searchlocation');
                }}>
                <Text style={styles.optionText}>
                  {AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}
                </Text>
                <View
                  style={[
                    styles.radioCircle,
                    selectedLocationType === 'searchlocation' && styles.radioCircleSelected,
                  ]}
                />
              </TouchableOpacity>
            </View>

            {selectedLocationType === 'searchlocation' && (
              <View style={styles.locationInputContainer}>
                <GooglePlacesAutocomplete
                  {...GooglePlacesAutocompleteDefaultProps}
                  ref={autocompleteRef}
                  placeholder="Search for a location"
                  minLength={2}
                  fetchDetails={true}
                  onPress={(data, details = null) => {
                    if (details) {
                      setPendingPlace(details);
                      setShowAddressPopup(true);
                    }
                  }}
                  query={{
                    key: Config.GOOGLE_PLACES_API_KEY,
                    language: 'en',
                    components: 'country:in',
                  }}
                  debounce={300}
                  styles={{
                    container: {flex: 0, zIndex: 1000},
                    textInput: styles.locationInput,
                    textInputContainer: styles.locationInputContainer,
                    listView: styles.locationListView,
                    row: styles.locationRow,
                    description: styles.locationDescription,
                  }}
                  enablePoweredByContainer={false}
                />
              </View>
            )}

            {selectedLocationType === 'currentlocation' && (
              <View style={styles.locationDisplay}>
                <Text style={styles.locationText}>
                  {customerRequestAdrress
                    ? String(customerRequestAdrress.formatted_address)
                    : 'Loading location...'}
                </Text>
              </View>
            )}

{selectedLocationType === 'searchlocation' && customerRequestAdrress && (
  <View style={styles.locationDisplay}>
    <Text style={styles.locationText}>
      {String(customerRequestAdrress.formatted_address)}
    </Text>
    <TouchableOpacity
  style={[styles.saveLocationButton, locationSaved && styles.disabledButton]}
  disabled={locationSaved}
  onPress={async () => {
    try {
      if (user?.uid && customerRequestAdrress) {
        // Store current form values
        const currentFormState = {
          address1,
          address2,
          city,
          state,
          zipCode,
          country,
          coordinates,
        };

        const locationData = {
          name: String(
            customerRequestAdrress.formatted_address.split(',')[0] ||
            'Searched Location',
          ),
          address: String(customerRequestAdrress.formatted_address),
          coordinates: customerRequestAdrress.location,
        };

        await VehicleService.saveLocation(user.uid, locationData);
        const locations = await VehicleService.fetchSavedLocations(user.uid);
        
        // Update saved locations without affecting form state
        setSavedLocations(locations);
        setLocationSaved(true);

        // Restore form state
        setAddress1(currentFormState.address1);
        setAddress2(currentFormState.address2);
        setCity(currentFormState.city);
        setState(currentFormState.state);
        setZipCode(currentFormState.zipCode);
        setCountry(currentFormState.country);
        setCoordinates(currentFormState.coordinates);

        showCustomAlert('Success', 'Location saved successfully!');
      }
    } catch (error) {
      console.error('Error saving location:', error);
      showCustomAlert(
        'Error',
        'Failed to save location. Please try again.',
      );
    }
  }}>
  <Text style={[styles.saveLocationText, locationSaved && styles.disabledText]}>
    {locationSaved ? 'Location Saved' : AppStrings.MCX_SAVE_LOCATION_TEXT}
  </Text>
</TouchableOpacity>
  </View>
)}
     

            {selectedLocationType === 'savedlocation' && (
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>
                  {AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}
                </Text>
                <CommonDropdown
                  data={savedLocations.map(l => ({
                    label: String(l.name),
                    value: l.id,
                  }))}
                  value={selectedSavedLocation}
                  onValueChange={setSelectedSavedLocation}
                  placeholder="Select saved location"
                />
              </View>
            )}
          </View> */}
          <View style={styles.section}>
  <Text style={styles.sectionTitle}>
    {AppStrings.MCX_MY_LOCATION_TEXT}
  </Text>
  
  {/* Commented out radio buttons for current and saved locations */}
  {/* <View style={styles.radioGroup}>
    <TouchableOpacity
      style={styles.radioOption}
      onPress={() => {
        handleLocationTypeChange('currentlocation');
      }}>
      <Text style={styles.optionText}>
        {AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}
      </Text>
      <View
        style={[
          styles.radioCircle,
          selectedLocationType === 'currentlocation' && styles.radioCircleSelected,
        ]}
      />
    </TouchableOpacity>
    <TouchableOpacity
      style={styles.radioOption}
      onPress={() => {
        handleLocationTypeChange('savedlocation');
      }}>
      <Text style={styles.optionText}>
        {AppStrings.MCX_SAVED_LOCATION_TEXT}
      </Text>
      <View
        style={[
          styles.radioCircle,
          selectedLocationType === 'savedlocation' && styles.radioCircleSelected,
        ]}
      />
    </TouchableOpacity>
    <TouchableOpacity
      style={styles.radioOption}
      onPress={() => {
        handleLocationTypeChange('searchlocation');
      }}>
      <Text style={styles.optionText}>
        {AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}
      </Text>
      <View
        style={[
          styles.radioCircle,
          selectedLocationType === 'searchlocation' && styles.radioCircleSelected,
        ]}
      />
    </TouchableOpacity>
  </View> */}

  {/* Only show search location input */}
  <View style={styles.locationInputContainer}>
    <GooglePlacesAutocomplete
      {...GooglePlacesAutocompleteDefaultProps}
      ref={autocompleteRef}
      placeholder="Search for a location"
      textInputProps={{
        placeholderTextColor: '#6B7280',
      }}
      minLength={2}
      fetchDetails={true}
      onPress={(data, details = null) => {
        if (details) {
          setPendingLocation(details);
          setShowAddressPopup(true);
        }
      }}
      query={{
        key: Config.GOOGLE_PLACES_API_KEY,
        language: 'en',
        components: 'country:in',
      }}
      debounce={300}
      styles={{
        container: {flex: 0, zIndex: 1000},
        textInput: styles.locationInput,
        textInputContainer: styles.locationInputContainer,
        listView: styles.locationListView,
        row: styles.locationRow,
        description: styles.locationDescription,
      }}
      enablePoweredByContainer={false}
    />
  </View>

  {/* Show selected location if available */}
  {customerRequestAdrress && (
    <View style={styles.locationDisplay}>
      <Text style={styles.locationText}>
        {String(customerRequestAdrress.formatted_address)}
      </Text>
    </View>
  )}

  {/* Commented out current location display */}
  {/* {selectedLocationType === 'currentlocation' && (
    <View style={styles.locationDisplay}>
      <Text style={styles.locationText}>
        {customerRequestAdrress
          ? String(customerRequestAdrress.formatted_address)
          : 'Loading location...'}
      </Text>
    </View>
  )} */}

  {/* Commented out save location button */}
  {/* {selectedLocationType === 'searchlocation' && customerRequestAdrress && (
    <View style={styles.locationDisplay}>
      <Text style={styles.locationText}>
        {String(customerRequestAdrress.formatted_address)}
      </Text>
      <TouchableOpacity
        style={[styles.saveLocationButton, locationSaved && styles.disabledButton]}
        disabled={locationSaved}
        onPress={async () => {
          try {
            if (user?.uid && customerRequestAdrress) {
              const currentFormState = {
                address1,
                address2,
                city,
                state,
                zipCode,
                country,
                coordinates,
              };

              const locationData = {
                name: String(
                  customerRequestAdrress.formatted_address.split(',')[0] ||
                  'Searched Location',
                ),
                address: String(customerRequestAdrress.formatted_address),
                coordinates: customerRequestAdrress.location,
              };

              await VehicleService.saveLocation(user.uid, locationData);
              const locations = await VehicleService.fetchSavedLocations(user.uid);
              
              setSavedLocations(locations);
              setLocationSaved(true);

              setAddress1(currentFormState.address1);
              setAddress2(currentFormState.address2);
              setCity(currentFormState.city);
              setState(currentFormState.state);
              setZipCode(currentFormState.zipCode);
              setCountry(currentFormState.country);
              setCoordinates(currentFormState.coordinates);

              Alert.alert('Success', 'Location saved successfully!');
            }
          } catch (error) {
            console.error('Error saving location:', error);
            Alert.alert(
              'Error',
              'Failed to save location. Please try again.',
            );
          }
        }}>
        <Text style={[styles.saveLocationText, locationSaved && styles.disabledText]}>
          {locationSaved ? 'Location Saved' : AppStrings.MCX_SAVE_LOCATION_TEXT}
        </Text>
      </TouchableOpacity>
    </View>
  )} */}

  {/* Commented out saved location dropdown */}
  {/* {selectedLocationType === 'savedlocation' && (
    <View style={styles.dropdownContainer}>
      <Text style={styles.dropdownLabel}>
        {AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}
      </Text>
      <CommonDropdown
        data={savedLocations.map(l => ({
          label: String(l.name),
          value: l.id,
        }))}
        value={selectedSavedLocation}
        onValueChange={setSelectedSavedLocation}
        placeholder="Select saved location"
      />
    </View>
  )} */}
</View>
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_ADDRESS_1_LABEL}
          </Text>
          <CommonTextInput
            value={address1}
            onChangeText={setAddress1}
            placeholder={AppStrings.MCX_ADDRESS_1_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.address1 && (
            <Text style={styles.errorText}>{errors.address1}</Text>
          )}
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_ADDRESS_2_LABEL}
          </Text>
          <CommonTextInput
            value={address2}
            onChangeText={setAddress2}
            placeholder={AppStrings.MCX_ADDRESS_2_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          <Text style={styles.fieldLabel}>{AppStrings.MCX_CITY_LABEL}</Text>
          <CommonTextInput
            value={city}
            onChangeText={setCity}
            placeholder={AppStrings.MCX_CITY_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.city && <Text style={styles.errorText}>{errors.city}</Text>}
          <HorizontalDivider isFullWidth={true} />
          <Text style={styles.fieldLabel}>{AppStrings.MCX_STATE_LABEL}</Text>
          <CommonTextInput
            value={state}
            onChangeText={setState}
            placeholder={AppStrings.MCX_STATE_PLACEHOLDER}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
          />
          {errors.state && (
            <Text style={styles.errorText}>{errors.state}</Text>
          )}
          <Text style={styles.fieldLabel}>
            {AppStrings.MCX_ZIP_CODE_LABEL}
          </Text>
          <CommonTextInput
            value={zipCode}
            onChangeText={setZipCode}
            placeholder={AppStrings.MCX_ZIP_CODE_PLACEHOLDER}
            keyboardType="numeric"
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
            onFocus={() => {
              setTimeout(() => {
                screenLayoutRef.current?.scrollToEnd({animated: true});
              }, 100);
            }}
          />
          {errors.zipCode && (
            <Text style={styles.errorText}>{errors.zipCode}</Text>
          )}
          <Text style={styles.fieldLabel}>{AppStrings.MCX_COUNTRY_LABEL}</Text>
          <CommonTextInput
            value={country}
            onChangeText={setCountry}
            placeholder={AppStrings.MCX_COUNTRY_PLACEHOLDER}
            style={[styles.textInput,styles.emailInput]}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            fontSize={Sizes.LARGE}
            onFocus={() => {
              setTimeout(() => {
                screenLayoutRef.current?.scrollToEnd({animated: true});
              }, 100);
            }}
          />
          {errors.country && (
            <Text style={styles.errorText}>{errors.country}</Text>
          )}
        </View>
        {Platform.OS === 'ios' && showDatePicker && (
          <Modal
            isVisible={showDatePicker}
            style={styles.dateModal}
            onBackdropPress={() => setShowDatePicker(false)}
          >
            <View style={styles.dateModalContent}>
              <DateTimePicker
                value={tempDate}
                mode="date"
                display="spinner"
                onChange={(event, selectedDate) => {
                  if (selectedDate) {
                    setTempDate(selectedDate);
                  }
                }}
                maximumDate={new Date()}
              />
      <View style={styles.dateModalButtons}>
        <TouchableOpacity
          style={styles.dateCancelButton}
          onPress={() => setShowDatePicker(false)}
        >
          <Text style={styles.dateButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.dateDoneButton}
          onPress={() => {
            const formatted = formatDateDDMMYYYY(tempDate);
            setDateOfBirth(formatted);
            setShowDatePicker(false);
          }}
        >
          <Text style={styles.dateButtonText}>Done</Text>
        </TouchableOpacity>
      </View>
            </View>
          </Modal>
        )}
        {Platform.OS === 'android' && showDatePicker && (
          <DateTimePicker
            value={dateOfBirth ? parseDateDDMMYYYY(dateOfBirth) : new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                const formatted = formatDateDDMMYYYY(selectedDate);
                setDateOfBirth(formatted);
              }
            }}
            maximumDate={new Date()}
          />
        )}
      </ScreenLayout>
      </KeyboardAvoidingView>
      {/* <UseAddressPopupPage
        visible={showAddressPopup}
        heading="Use Address"
        body={pendingPlace?.formatted_address || ''}
        doAction={{
          callback: handleAddressConfirm,
        }}
        onCancel={handleAddressCancel}
        confirmText="Use"
      /> */}
      <UseAddressPopupPage
  visible={showAddressPopup}
  heading="Use Address"
  body={pendingLocation?.formatted_address || ''}
  doAction={{
    callback: handleAddressConfirm,
  }}
  onCancel={handleAddressCancel}
  confirmText="Use"
/>

<CustomAlert
  visible={alertVisible}
  title={alertTitle}
  message={alertMessage}
  onDismiss={hideAlert}
  buttons={alertButtons}
/>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  scrollSpace: {
    width: '100%',
  },
  mainContainer: {
   
    flex: 1,
    backgroundColor: Colors.APPBAR_BG_COLOR,
  },
  titleSection: {
    marginBottom: 0,
  },
  profilePhotoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  profilePhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  changePhotoButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
   modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20, // Add padding to prevent edge touching
  },
  modalContainer: {
    width: '90%', // Use percentage instead of fixed width
    maxWidth: 400,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeading: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#C70039',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalBody: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
    flexWrap: 'wrap', // Allow text to wrap
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10, // Add gap between buttons
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
    minWidth: 80, // Ensure minimum button width
  },
  cancelButton: {
    backgroundColor: '#C70039',
  },
  useButton: {
    backgroundColor: '#C70039',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  dateModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  dateCancelButton: {
    flex: 1,
    backgroundColor: '#C70039',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginRight: 10,
    alignItems: 'center',
  },
  dateDoneButton: {
    flex: 1,
    backgroundColor: '#C70039',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
    alignItems: 'center',
  },
  dateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  changePhotoText: {
    fontSize: Sizes.XSMALL,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    marginRight: 8,
    fontWeight: '600',
  },
  changePhotoIcon: {
    width: 32,
    height: 32,
    tintColor: Colors.PRIMARY,
  },
  formContainer: {
   backgroundColor: 'white',
   marginTop: 20,
   marginBottom: 20,
   paddingVertical: 0,
   borderRadius: 2,
   elevation: 2,
   shadowColor: '#000',
   shadowOffset: {width: 0, height: 2},
   shadowOpacity: 0.1,
   shadowRadius: 4,
 },
  fieldLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginTop: 16,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 1,
    fontWeight: '600',
  },
  textInput: {
    marginTop: 0,
    marginBottom: 25,
  },
  emailInput:{
    color:'#221d1dff',
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 16,
  },
  halfWidth: {
    flex: 0.48,
  },
  rowFieldLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginTop: 16,
    marginLeft: 0,
    fontWeight: '600',
  },
  dropdown: {
    marginLeft: 0,
    marginRight: 0,
    marginTop: 0,
    marginBottom: 25,
    paddingHorizontal: CommonUIParams.CUSTOM_PADDING_16,
  },
  rowTextInput: {
    marginLeft: 0,
    marginRight: 0,
    marginTop: 1,
    marginBottom: 0,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    padding: 8,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginRight: CommonUIParams.CUSTOM_PADDING_16,
    backgroundColor: '#fff',
    justifyContent: 'center',
    minHeight: 40,
    marginBottom: 25,
  },
  dateInputText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    fontWeight: '800',
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    padding: 8,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginRight: CommonUIParams.CUSTOM_PADDING_16,
    backgroundColor: '#fff',
    minHeight: 40,
    marginBottom: 25,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    fontWeight: '800',
  },
  disabledInput: {
    // additional styles if needed
  },
  // modalContainer: {
  //   flex: 1,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   backgroundColor: 'rgba(0, 0, 0, 0.2)',
  // },
  // modalContent: {
  //   backgroundColor: 'white',
  //   borderRadius: 10,
  //   padding: 20,
  //   alignItems: 'center',
  //   shadowColor: '#000',
  //   shadowOffset: {
  //     width: 0,
  //     height: 2,
  //   },
  //   shadowOpacity: 0.25,
  //   shadowRadius: 4,
  //   elevation: 5,
  // },
  errorText: {
    color: 'red',
    fontSize: Sizes.SMALL,
    marginTop: -20,
    marginLeft: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 5,
  },
  rowErrorText: {
    marginTop: 5,
    marginLeft: 0,
    marginBottom: 10,
  },
  autocompleteContainer: {
    position: 'absolute',
    top: 200,
    left: 16,
    right: 16,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },
  autocompleteWrapper: {
    flex: 0,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },
  locationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    zIndex: 1000,
  },
  autocompleteList: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderTopWidth: 0,
    maxHeight: 200,
  },
  autocompleteRow: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  inlineAutocompleteWrapper: {
    flex: 0,
    backgroundColor: 'transparent',
  },
  inlineLocationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  inlineAutocompleteList: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderTopWidth: 0,
    maxHeight: 200,
  },
  inlineAutocompleteRow: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  inlineSearchContainer: {
    marginHorizontal: CommonUIParams.CUSTOM_PADDING_16,
    marginBottom: 16,
  },
  section: {
    marginBottom: hp(3),
    marginLeft: wp(2.5),
    marginRight: wp(2.5),
  },
  sectionTitle: {
    fontSize: wp(4),
    fontFamily: Fonts.ROBO_BOLD,
    color: '#000',
    marginTop: hp(2),
    marginLeft: wp(2.5),
    marginBottom: hp(0.125),
    fontWeight: '600',
  },
  radioGroup: {
    marginBottom: hp(1.5),
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: wp(2),
    padding: wp(3),
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
    justifyContent: 'space-between',
  },
  radioCircle: {
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: Colors.COMMON_BlACK_SHADE,
    marginRight: wp(2),
  },
  radioCircleSelected: {
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    borderWidth: wp(1.5),
    borderColor: Colors.PRIMARY,
    marginRight: wp(2),
  },
  optionText: {
    fontSize: wp(3.5),
    color: Colors.COMMON_BlACK_SHADE,
  },
  locationInputContainer: {
    marginBottom: hp(1),
    zIndex: 1000,
  },
  locationListView: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  locationRow: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    padding: wp(3.25),
    height: hp(5.5),
    flexDirection: 'row',
  },
  locationDescription: {
    color: Colors.COMMON_BlACK_SHADE,
  },
  locationDisplay: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: wp(1),
    paddingVertical: wp(2.5),
    paddingHorizontal: wp(3),
    marginTop: hp(1),
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
  locationText: {
    fontSize: Sizes.MEDIUM,
    color: Colors.SECONDARY,
    fontWeight: '600',
  },
  saveLocationButton: {
    marginTop: hp(1),
    padding: wp(2),
    borderRadius: wp(1),
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: wp(40),
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  saveLocationText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: wp(4),
  },
  disabledText: {
    color: '#666666',
  },
  dropdownContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: wp(1),
    paddingVertical: hp(1.5),
    paddingHorizontal: wp(3),
    marginBottom: hp(1),
  },
  dropdownLabel: {
    fontWeight: 'bold',
    color: '#8B0000', // Dark red color to match the design
    marginBottom: hp(1),
    fontSize: wp(3.5),
    flex: 1,
  },
  dateModal: {
    justifyContent: 'center',
    margin: 0,
  },
  dateModalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 20,
    marginHorizontal: 20,
  },
  dateInputContainer: {
    marginBottom: 25,
  },
});

export default EditProfile;
