import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {onValue, update} from '@react-native-firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {RootStackParamList} from '../../../utils/configs/types';
import {GC_CUSTOMER_ID, NOTIFICATION_TYPE} from '../../../utils/globals';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import {Colors, Sizes} from '../../../utils/constants/Theme';
import {AppCommonIcons, RouteNames} from '../../../utils/constants/AppStrings';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import {AnalyticService} from '../../../utils/services/AnalyticService';
import AppBackground from '../../../components/ui/AppBackground';

import {ChatService} from '../../../utils/services/ChatService';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import CustomAlert from '../../../components/common/CustomAlert';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type ChatMessage = {
  type?: string;
  senderName?: string;
  message?: string;
  sendDate?: string;
  senderId?: string;
  mechanicDelete?: boolean;
  customerDelete?: boolean;
  _id?: string;
};

type ReadStatusMap = Record<string, boolean>;
type ChatRoomRoute = RouteProp<RootStackParamList, 'ChatRoomScreen'>;

const ChatRoomScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute<ChatRoomRoute>();
  const params = route.params;

  const [customerId, setCustomerId] = useState<string | null>(null);
  const [mechDetails, setMechDetails] = useState<any>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageIds, setMessageIds] = useState<string[]>([]);
  const [readMap, setReadMap] = useState<ReadStatusMap>({});
  const [input, setInput] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [customerDataLoaded, setCustomerDataLoaded] = useState<boolean>(false);
  const [mechanicDataLoaded, setMechanicDataLoaded] = useState<boolean>(false);
  const [messagesDataLoaded, setMessagesDataLoaded] = useState<boolean>(false);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const insets = useSafeAreaInsets();

  const chatUnsubRef = useRef<null | (() => void)>(null);
  const readUnsubRef = useRef<null | (() => void)>(null);
  const mechUnsubRef = useRef<null | (() => void)>(null);
  const flatListRef = useRef<FlatList>(null);
  const isInitialMount = useRef<boolean>(true);

  const [customerName, setCustomerName] = useState<string | null>(null);
  const [customerImage, setCustomerImage] = useState<string | null>(null);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const roomKey = useMemo(() => {
    return `${params.customerId}_${params.mechanicId}_${params.workRequestId}`;
  }, [params.customerId, params.mechanicId, params.workRequestId]);

  // Helper function to show custom alert
  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  // Check if all data is loaded and update loading state
  const checkAllDataLoaded = useCallback(() => {
    if (customerDataLoaded && mechanicDataLoaded && messagesDataLoaded) {
      setLoading(false);
    }
  }, [customerDataLoaded, mechanicDataLoaded, messagesDataLoaded]);

  // Reset loading states when roomKey changes
  useEffect(() => {
    setLoading(true);
    setCustomerDataLoaded(false);
    setMechanicDataLoaded(false);
    setMessagesDataLoaded(false);
  }, [roomKey]);

  useEffect(() => {
    AnalyticService.AddAnalyticsScreenView('Chatroom Page', '/#/chatroom');
  }, []);

  useEffect(() => {
    // Clear previous chat data when roomKey changes
    setMessages([]);
    setReadMap({});
    setMessageIds([]);

    (async () => {
      const cid = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      setCustomerId(cid);
      await preLoadChatData();
      attachMessageListener();
      attachReadMap();
    })();

    return () => {
      // Cleanup all listeners using unsubscribe functions
      if (chatUnsubRef.current) {
        chatUnsubRef.current();
        chatUnsubRef.current = null;
      }
      if (readUnsubRef.current) {
        readUnsubRef.current();
        readUnsubRef.current = null;
      }
      if (mechUnsubRef.current) {
        mechUnsubRef.current();
        mechUnsubRef.current = null;
      }
    };
  }, [roomKey]);

  // Check loading state whenever data loaded states change
  useEffect(() => {
    checkAllDataLoaded();
  }, [customerDataLoaded, mechanicDataLoaded, messagesDataLoaded, checkAllDataLoaded]);

  // Separate effect for auto-scrolling when messages change
  useEffect(() => {
    if (!isInitialMount.current && messages.length > 0) {
      // Delay scroll to ensure FlatList has rendered
      const timer = setTimeout(() => {
        flatListRef.current?.scrollToEnd({animated: true});
      }, 300);
      return () => clearTimeout(timer);
    }
    if (isInitialMount.current && messages.length > 0) {
      isInitialMount.current = false;
    }
  }, [messages]);

  const attachMessageListener = useCallback(() => {
    const chatRef = ChatService.chatRef(roomKey);
     
    // onValue returns unsubscribe function
    const unsubscribe = onValue(
      chatRef,
      snap => {
        const newMsgs: ChatMessage[] = [];
        const ids: string[] = [];
         
        snap.forEach(cs => {
          const item: ChatMessage = cs.val();
          if (item && item.customerDelete !== true) {
            const k = cs.key as string;
            newMsgs.push({...item, _id: k});
            ids.push(k);
          }
          return undefined;
        });
         
        setMessages(newMsgs);
        setMessageIds(ids);
        setMessagesDataLoaded(true);

        // Mark mechanic messages as read
        if (params.mechanicId) {
          const updates: Record<string, any> = {};
          ids.forEach((id, idx) => {
            const m = newMsgs[idx];
            if (m?.senderId === params.mechanicId) {
              updates[`${id}`] = true;
            }
          });
          if (Object.keys(updates).length > 0) {
            update(ChatService.chatReadRef(roomKey), updates).catch(() => {});
          }
        }
      },
      error => {
        console.error('Error listening to chat messages:', error);
        setMessagesDataLoaded(true); // Set to true even on error to stop loading
      }
    );
     
    chatUnsubRef.current = unsubscribe;
  }, [roomKey, params.mechanicId]);

  const attachReadMap = useCallback(() => {
    const readRef = ChatService.chatReadRef(roomKey);
     
    // onValue returns unsubscribe function
    const unsubscribe = onValue(
      readRef,
      snap => {
        setReadMap(snap.val() || {});
      },
      error => {
        console.error('Error listening to read map:', error);
      }
    );
     
    readUnsubRef.current = unsubscribe;
  }, [roomKey]);

  const sendMessage = async () => {
    if (!input.trim()) return;
    const messageToSend = input.trim();
    setInput(''); // Clear immediately

    try {
      const nowStr = new Date().toString();
      const payload: ChatMessage = {
        type: 'message',
        senderName: customerName || '',
        message: messageToSend,
        sendDate: nowStr,
        senderId: params.customerId,
        mechanicDelete: false,
        customerDelete: false,
      };

      console.log('Sending chat message payload:', JSON.stringify(payload, null, 2));
      console.log('Chat token:', await AsyncStorage.getItem('GC_FCM_TOKEN'));

      const chatLog = prepareChatLog(messageToSend);
      await ChatService.sendMessage(
        roomKey,
        payload,
        chatLog as any,
        params.customerId,
      );
       
      ChatService.getMechanicDetail(params.mechanicId)
        .child('my-chats')
        .child(roomKey)
        .set(true);

      // Auto-scroll will happen via useEffect when messages update
      sendNotification(messageToSend, roomKey);
    } catch (e) {
      showCustomAlert('Error', 'Failed to send message', [{ text: 'OK', onPress: hideAlert }]);
      setInput(messageToSend);
    }
  };

  const sendNotification = async (chatContent: string, chatkey: string) => {
    if (mechDetails?.['settings']?.['notification']?.['fcm-token']) {
      const token = mechDetails['settings']['notification']['fcm-token'];
      const title = 'New message';
      const body = `${customerName} has sent you a message: ${chatContent}`;

      try {
        await AppointmentService.sendPUSHToUser(
          token,
          title,
          body,
          'chat', // Pass "chat" as type param
          chatkey, // Pass roomkey as params
        );
        console.log('FCM notification sent successfully');
      } catch (error) {
        console.error('FCM send error:', error);
      }
    }
  };

  const prepareChatLog = (lastMessage: string) => {
    const svc = params.workRequestDetails?.service || [];
    return {
      'mechanic-id': params.mechanicId,
      'mechanic-name': params.mechanicName || '',
      'mechanic-image': params.mechanicImage || '',
      'customer-id': params.customerId,
      'customer-name': customerName,
      'customer-image': customerImage,
      'last-message': lastMessage,
      'last-sender': params.customerId,
      service: svc,
      date: params.workRequestDetails?.day,
      month: params.workRequestDetails?.month,
      time: params.workRequestDetails?.time || '6:00 am - 7:00 am',
      'updated-time': Math.floor(Date.now() / 1000),
      type: 'Service',
      'work-request-id': params.workRequestId,
      seen: false,
    };
  };

  const preLoadChatData = async () => {
    if (!params?.customerId) return;

    const userRef = ChatService.fetchUserInformation(params.customerId);
    if (userRef) {
      userRef.once('value', custResp => {
        const val = custResp.val() || {};
        const first = val['first-name'] || '';
        const last = val['last-name'] || '';
        const image = val['image'] || 'assets/imgs/profile.png';
        setCustomerName(`${first} ${last}`.trim());
        setCustomerImage(image);
        setCustomerDataLoaded(true);
      });
    }

    if (params?.mechanicId) {
      const mechanicRef = ChatService.getMechanicDetail(params.mechanicId);
        
      // onValue returns unsubscribe function
      const unsubscribe = onValue(
        mechanicRef,
        mechData => {
          const newMechDetails = mechData.val();
          setMechDetails(newMechDetails);
          setMechanicDataLoaded(true);
        },
        error => {
          console.error('Error listening to mechanic details:', error);
          setMechanicDataLoaded(true); // Set to true even on error to stop loading
        }
      );
        
      mechUnsubRef.current = unsubscribe;
    }
  };

  const deleteAll = async () => {
    try {
      await ChatService.deleteAllCustomerMessages(
        roomKey,
        params.customerId,
        messageIds,
      );
      // Navigate to chat list page instead of going back
      (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
        screen: RouteNames.MCX_NAV_CHAT_LIST,
      });
    } catch (e) {
      showCustomAlert('Error', 'Failed to delete', [{ text: 'OK', onPress: hideAlert }]);
    }
  };

  const confirmDeleteAll = () => {
    setAlertTitle('DELETE');
    setAlertMessage('Are you sure you want to delete all chats?');
    setAlertButtons([
      { text: 'Cancel', onPress: hideAlert },
      { text: 'YES',onPress: () => { hideAlert(); deleteAll(); } },
    ]);
    setAlertVisible(true);
  };

  const toggleExpand = () => setIsExpanded(p => !p);

  const renderItem = ({item, index}: {item: ChatMessage; index: number}) => {
    const isMe = item.senderId === params.customerId;
    const dt = item.sendDate ? new Date(item.sendDate) : null;
    const displayTime = dt
      ? dt.toLocaleString([], {
          year: '2-digit',
          month: 'numeric',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
        })
      : '';
    
    return (
      <View style={[styles.chatRow, isMe ? styles.rightAlign : styles.leftAlign]}>
        <View style={[styles.bubble, isMe ? styles.rightBubble : styles.leftBubble]}>
          <View style={styles.nameTimeRow}>
            <Text style={[isMe ? styles.meNameText : styles.otherNameText, {marginRight: 8}]}>
              {isMe ? 'Me' : item.senderName || ''}
            </Text>
            <Text style={isMe ? styles.meTimeText : styles.otherTimeText}>
              {displayTime}
            </Text>
          </View>
          <Text style={[styles.msgText, isMe ? styles.meMsgText : styles.otherMsgText]}>
            {item.message}
          </Text>
          {isMe && (
            <Text style={styles.readMark}>
              {readMap[messageIds[index]] ? '✓✓' : '✓'}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <AppBackground />
      <View style={styles.topHeader}>
        <TouchableOpacity
          onPress={() =>
            params.isMessage
              ? (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
                  screen: RouteNames.MCX_NAV_MESSAGES,
                })
              : (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
                  screen: RouteNames.MCX_NAV_CHAT_LIST,
                })
          }
          style={styles.headerBackBtn}>
          <Image
            source={AppCommonIcons.MCX_ARROW_RIGHT}
            style={styles.headerBackIcon}
          />
        </TouchableOpacity>
        <View style={styles.headerTitleWrap}>
          <Image
            source={
              params.mechanicImage
                ? {uri: params.mechanicImage}
                : AppCommonIcons.MCX_USER_PROFILE_PIC
            }
            style={styles.headerAvatarImage}
          />
          <Text style={styles.headerTitle} numberOfLines={1}>
            {params.mechanicName || ''}
          </Text>
        </View>
        <TouchableOpacity onPress={confirmDeleteAll} style={styles.headerTrashBtn}>
          <Image
            source={AppCommonIcons.MCX_DELETE_ICON}
            style={styles.headerTrashIcon}
          />
        </TouchableOpacity>
      </View>
       
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}>
        {params.workRequestDetails?.service?.length ? (
          <View style={styles.headerBar}>
            {isExpanded && (
              <View style={styles.expandContentRow}>
                <View style={styles.dateCard}>
                  <Text style={styles.dateNumber}>
                    {params.workRequestDetails?.day || ''}
                  </Text>
                  <Text style={styles.dateMonth}>
                    {params.workRequestDetails?.month || ''}
                  </Text>
                </View>
                <View style={styles.serviceCard}>
                  <View style={styles.servicesContent}>
                    <Text style={styles.serviceTitle}>Services</Text>
                    {Array.isArray(params.workRequestDetails.service) &&
                      params.workRequestDetails.service.map((s: any, idx: number) => (
                        <View key={idx} style={styles.serviceContainer}>
                          <Text style={styles.serviceItem}>
                            • {s?.service || s}
                          </Text>
                          {s?.subservice && Array.isArray(s.subservice) && s.subservice.length > 0 && (
                            <View style={styles.subserviceContainer}>
                              {s.subservice.map((sub: string, subIdx: number) => (
                                <Text key={subIdx} style={styles.subserviceItem}>
                                  - {sub}
                                </Text>
                              ))}
                            </View>
                          )}
                        </View>
                      ))}
                     {params.workRequestDetails?.time && (
                    <Text style={styles.serviceTime}>
                      {params.workRequestDetails?.time}
                    </Text>
                  )}
                  </View>
                  
                </View>
              </View>
            )}
            <TouchableOpacity
              onPress={toggleExpand}
              style={styles.expandFab}
              activeOpacity={0.8}>
              <Text style={styles.expandFabIcon}>{isExpanded ? '▲' : '▼'}</Text>
            </TouchableOpacity>
          </View>
        ) : null}

        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? (insets.bottom) * 4 : 0}>
          <FlatList
            ref={flatListRef}
            data={messages}
            keyExtractor={(item, i) => item?._id || `msg-${i}`}
            renderItem={renderItem}
            contentContainerStyle={styles.listContent}
            onContentSizeChange={() => {
              // Initial scroll on mount
              if (isInitialMount.current && messages.length > 0) {
                setTimeout(() => {
                  flatListRef.current?.scrollToEnd({animated: false});
                }, 100);
              }
            }}
          />
          <View style={styles.inputRow}>
            <TextInput
              style={[styles.input, {color: '#000'}]}
              placeholder="Type a message"
              placeholderTextColor="#999"
              value={input}
              onChangeText={setInput}
              onSubmitEditing={sendMessage}
              returnKeyType="send"
            />
            <TouchableOpacity onPress={sendMessage} style={styles.sendBtn}>
              <Text style={styles.sendIcon}>➤</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
        <LoaderOverlay visible={loading} />
      </ScreenLayout>
      
      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

// Styles remain the same as your original
const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  headerBar: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  expandContentRow: {
    flex: 1,
    flexDirection: 'row',
    gap: 12,
    alignItems: 'stretch',
  },
  dateCard: {
    width: 60,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
  dateNumber: {
    fontSize: Sizes.XXXLARGE,
    fontWeight: 'bold',
    color: '#fff',
  },
  dateMonth: {
    fontSize: Sizes.SMALL,
    color: '#fff',
    opacity: 0.9,
  },
  serviceCard: {flex: 1, backgroundColor: '#fff', borderRadius: 4, padding: 8, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start'},
  servicesContent: {flex: 1},
  serviceTitle: {fontWeight: '700', marginBottom: 4,color: '#6B7280'},
  serviceContainer: {marginBottom: 4},
  serviceItem: {color: Colors.COMMON_BlACK_SHADE, fontWeight: '600'},
  subserviceContainer: {marginLeft: 12, marginTop: 2},
  subserviceItem: {color: '#b22222', fontSize: 12, marginTop: 1},
  serviceTime: {color: '#6B7280', fontWeight: '600', fontSize: 12, marginRight: 8},
  listContent: {padding: 12},
  chatRow: {marginBottom: 8, flexDirection: 'row'},
  leftAlign: {justifyContent: 'flex-start'},
  rightAlign: {justifyContent: 'flex-end'},
  bubble: {maxWidth: '80%', borderRadius: 8, padding: 10},
  leftBubble: {
    backgroundColor: '#FFFFFF',
    borderWidth: 0.5,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
  rightBubble: {backgroundColor: '#C8FACC'},
  msgText: {marginTop: 6},
  meMsgText: {color: '#000000'},
  otherMsgText: {color: '#000000'},
  readMark: {alignSelf: 'flex-end', color: '#111827', marginTop: 6},
  nameTimeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  meNameText: {fontWeight: '700', color: '#0C5B0C'},
  otherNameText: {fontWeight: '700', color: '#1f4aa8'},
  meTimeText: {fontSize: Sizes.SMALL, color: '#4B5563'},
  otherTimeText: {fontSize: Sizes.SMALL, color: '#6B7280'},
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  sendBtn: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
  },
  sendIcon: {color: '#fff', fontSize: 18, fontWeight: '700'},
  mainContainer: {flex: 1},
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#2F4A58',
  },
  headerBackBtn: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerBackIcon: {
    width: 20,
    height: 20,
    tintColor: '#fff',
    transform: [{scaleX: -1}],
  },
  headerTitleWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  headerTitle: {
    color: '#fff',
    fontWeight: '700',
    marginLeft: 8,
    maxWidth: '70%',
  },
  headerTrashBtn: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTrashIcon: {width: 22, height: 22, tintColor: '#fff'},
  expandFab: {
    position: 'absolute',
    alignSelf: 'center',
    top: -20,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#C8102E',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 4,
  },
    headerAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#fff',
  },
  expandFabIcon: {color: '#fff', fontSize: 16, fontWeight: '700'},
});

export default ChatRoomScreen;
