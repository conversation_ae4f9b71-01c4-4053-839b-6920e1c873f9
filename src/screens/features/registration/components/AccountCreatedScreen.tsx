import React from 'react';
import {View, Text, StyleSheet, ScrollView, ActivityIndicator} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {VehicleService} from '../../../../utils/services/VehicleService';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import CustomButton from '../../../../components/common/CustomButton';
import CustomAlert from '../../../../components/common/CustomAlert';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
  GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
} from '../../../../utils/globals';
import {useAuth} from '../../../../utils/configs/AuthContext';
import {getAuth} from '@react-native-firebase/auth';
import {getApp} from '@react-native-firebase/app';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface AccountCreatedScreenProps {
  onContinue: () => void;
  vinData?: {
    vinNumber: string;
    vehicleInfo: {
      vin: string;
      make: string;
      model: string;
      manufactureYear: string;
      fuel: string;
      warning?: string;
      valid?: boolean;
    };
  };
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    password: string;
  };
  isSignupWithSocial: boolean;
}

const AccountCreatedScreen: React.FC<AccountCreatedScreenProps> = ({
  onContinue,
  vinData,
  userData,
  isSignupWithSocial,
}) => {
  const {updateUserRegistration, updateRegistrationStatus, signUp, setUser} =
    useAuth();

  const insets = useSafeAreaInsets();
 
  const [isLoading, setIsLoading] = React.useState<boolean>(false);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = React.useState<boolean>(false);
  const [alertTitle, setAlertTitle] = React.useState<string>('');
  const [alertMessage, setAlertMessage] = React.useState<string>('');
  const [alertButtons, setAlertButtons] = React.useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const handleContinue = async () => {
    if (isLoading) {
      return;
    }

    try {
      setIsLoading(true);
      let uid;

      if (!isSignupWithSocial) {
        // Email signup - create new user
        const user = await signUp(userData.email, userData.password);
        if (!user.uid) {
          throw new Error(
            'User ID not found. Please start registration from step 1.',
          );
        }
        uid = user.uid;
        await AsyncStorage.setItem('userEmail', userData.email);
        await AsyncStorage.setItem(GC_CUSTOMER_ID, uid);
      } else {
        // Social signup - get existing uid
        uid = await AsyncStorage.getItem(GC_CUSTOMER_ID);
        if (!uid) {
          throw new Error('User ID not found for social login');
        }
      }

      // Get provider information from storage
      const providerType =
        (await AsyncStorage.getItem(GC_SIGNUP_PROVIDER_TYPE_PREF)) || 'email';
      const providerTypeId =
        (await AsyncStorage.getItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF)) || uid;

      // Update user registration with all required fields
      await updateUserRegistration(
        uid,
        userData.firstName,
        userData.lastName,
        userData.email,
        userData.mobile,
        providerType,
        providerTypeId, 
        'COMPLETED', 
      );

      // Also explicitly update registration status
      await updateRegistrationStatus(uid, 'COMPLETED');

      // Add vehicle info if VIN was provided and valid
      if (vinData?.vehicleInfo?.valid && uid) {
        const {vehicleInfo} = vinData;
        await VehicleService.addVehicleInfo(uid, {
          vin: vehicleInfo.vin,
          make: vehicleInfo.make,
          model: vehicleInfo.model,
          manufactureYear: vehicleInfo.manufactureYear,
          fuel: vehicleInfo.fuel,
          warning: vehicleInfo.warning,
        });
      }

      // Set user after registration is completed
      const auth = getAuth(getApp());
      const currentUser = auth.currentUser;
      if (currentUser) {
        setUser(currentUser);
      }

      // Call onContinue which will handle navigation to dashboard
      onContinue();
    } catch (error: any) {
      console.error('SIGNUP ERROR:', error);
      setIsLoading(false);
      showCustomAlert(
        'Error',
        error.message || 'Failed to complete registration. Please try again.',
      );
    }
  };

  return (
    <View style={accountCreatedModernStyles.container}>
      <View style={accountCreatedModernStyles.accountCreatedSection}>
        <RegistrationTitleSection
          title="ACCOUNT CREATED"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={18}
          paddingHorizontal={0}
        />
        <ScrollView
          style={accountCreatedModernStyles.scrollContainer}
          contentContainerStyle={accountCreatedModernStyles.contentContainer}
          showsVerticalScrollIndicator={false}
          bounces={false}>
          <Text style={accountCreatedModernStyles.welcomeTitle}>
            Welcome to myCANx! 🎉
          </Text>
          <Text style={accountCreatedModernStyles.welcomeSubtitle}>
            Your account has been successfully created. Here's what you can do
            with myCANx:
          </Text>

          <View style={accountCreatedModernStyles.featureCard}>
            <Text style={accountCreatedModernStyles.featureTitle}>
              🚗 On-Demand Mobile Mechanics
            </Text>
            <Text style={accountCreatedModernStyles.paragraph}>
              Connect with skilled auto technicians anywhere, anytime. Get
              professional help during your travels or daily commute with just a
              tap.
            </Text>
          </View>

          <View style={accountCreatedModernStyles.featureCard}>
            <Text style={accountCreatedModernStyles.featureTitle}>
              🔧 Roadside Assistance
            </Text>
            <Text style={accountCreatedModernStyles.paragraph}>
              Need help with flat tires, fuel delivery, or jump starts? Our
              network of mechanics is ready to assist you wherever you are.
            </Text>
          </View>

          <View style={accountCreatedModernStyles.featureCard}>
            <Text style={accountCreatedModernStyles.featureTitle}>
              🔍 Diagnostics & Inspections
            </Text>
            <Text style={accountCreatedModernStyles.paragraph}>
              Get pre-purchase inspections, engine diagnostics, fluid checks,
              and comprehensive vehicle inspections from trusted professionals.
            </Text>
          </View>

          <View style={accountCreatedModernStyles.featureCard}>
            <Text style={accountCreatedModernStyles.featureTitle}>
              ⚙️ Professional Repairs
            </Text>
            <Text style={accountCreatedModernStyles.paragraph}>
              Book service appointments for everything from brake repairs to
              belt replacements. Our experienced technicians handle your vehicle
              with care.
            </Text>
          </View>

          <Text style={accountCreatedModernStyles.closingText}>
            All of this convenience is just a button away. Let's get started!
          </Text>
        </ScrollView>
      </View>
      <View
        style={[
          accountCreatedModernStyles.bottomContainer,
          {paddingBottom: insets.bottom, backgroundColor: '#FFFFFF'},
        ]}>
        <View style={accountCreatedModernStyles.buttonWrapper}>
          <CustomButton
            text={isLoading ? '' : AppStrings.MCX_CONTINUE_BUTTON}
            onPress={handleContinue}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            isBottomButton={true}
            bottomLineWidth={1}
            bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
            disabled={isLoading}
          />
          {isLoading && (
            <View style={accountCreatedModernStyles.buttonLoaderOverlay}>
              <ActivityIndicator size="small" color="#FFFFFF" />
              <Text style={accountCreatedModernStyles.buttonLoaderText}>
                Creating your account...
              </Text>
            </View>
          )}
        </View>
      </View>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

const accountCreatedModernStyles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  accountCreatedSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    alignSelf: 'stretch',
    flex: 1,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  scrollContainer: {
    flex: 1,
    marginBottom: 50,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    paddingBottom: 100,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    fontFamily: Fonts.ROBO_BOLD,
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 24,
    marginBottom: 24,
  },
  featureCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#A10000',
  },
  featureTitle: {
    fontSize: 17,
    fontWeight: '700',
    color: '#374151',
    fontFamily: Fonts.ROBO_BOLD,
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  paragraph: {
    fontSize: 15,
    color: '#4B5563',
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 22,
  },
  closingText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: Fonts.ROBO_BOLD,
    lineHeight: 24,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '600',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  buttonWrapper: {
    position: 'relative',
    width: '100%',
  },
  buttonLoaderOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 10,
    zIndex: 10,
  },
  buttonLoaderText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '600',
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  accountCreatedSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    flex: 1,
    overflow: 'hidden',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 80,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  paragraph: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 24,
    marginBottom: 15,
  },
});

export default AccountCreatedScreen;