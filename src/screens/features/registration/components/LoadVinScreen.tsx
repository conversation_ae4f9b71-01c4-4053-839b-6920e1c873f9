import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import VinDetailsComponent from './VinDetailsComponent';
import CustomAlert from '../../../../components/common/CustomAlert';
import {
  AppStrings,
  ExceptionStrings,
} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts} from '../../../../utils/constants/Theme';
import {VIN_URL} from '../../../../constants/constants';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

interface LoadVinScreenProps {
  vinData: {vinNumber: string; vehicleInfo: VehicleInfo};
  setVinData: (data: {vinNumber: string; vehicleInfo: VehicleInfo}) => void;
  onContinue: () => void;
  isSkipEnabled: () => boolean;
  onNavigateBack: () => void;
}

const LoadVinScreen: React.FC<LoadVinScreenProps> = ({
  vinData,
  setVinData,
  onContinue,
  isSkipEnabled,
  onNavigateBack,
}) => {
  const [vinNumber, setVinNumber] = useState(vinData.vinNumber || '');
  const [loading, setLoading] = useState(false);

  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const searchVIN = async (vin: string) => {
    const FORMAT = '?format=json';
    try {
      const response = await fetch(VIN_URL + vin + FORMAT);
      const data = await response.json();
      return data;
    } catch (error) {
      showCustomAlert('Error', 'Failed to fetch VIN data');
      return null;
    }
  };

  const processVIN = (vinApiData: any, vin: string) => {
    let vinInfo = {
      vin: vin,
      make: '',
      model: '',
      manufactureYear: '',
      fuel: '',
      warning: '',
      error: '',
      valid: false,
    };

    for (let vinIndex = 0; vinIndex < vinApiData.Count; vinIndex++) {
      switch (vinApiData.Results[vinIndex].VariableId) {
        case 26:
          vinInfo.make = vinApiData.Results[vinIndex].Value;
          break;
        case 28:
          vinInfo.model = vinApiData.Results[vinIndex].Value;
          break;
        case 29:
          vinInfo.manufactureYear = vinApiData.Results[vinIndex].Value;
          break;
        case 24:
          vinInfo.fuel = vinApiData.Results[vinIndex].Value;
          break;
        case 143:
          vinInfo.warning = vinApiData.Results[vinIndex].Value;
          break;
        case 156:
          vinInfo.error = vinApiData.Results[vinIndex].Value
            ? 'The Model Year decoded for this VIN may be incorrect'
            : '';
          break;
        default:
          break;
      }
    }

    if (!vinInfo.error) {
      if (!vinInfo.make) {
        vinInfo.error = 'The Model Year decoded for this VIN may be incorrect';
        vinInfo.valid = false;
        return vinInfo;
      }
      vinInfo.valid = true;
      return vinInfo;
    } else {
      vinInfo.valid = false;
      return vinInfo;
    }
  };

  // FIX: Handle VIN input change with proper validation
  const handleVinChange = (text: string) => {
    // Only allow alphanumeric characters (matching VehicleEditPage validation)
    const cleanText = text.replace(/[^a-zA-Z0-9]/g, '');
    // Convert to uppercase
    const upperText = cleanText.toUpperCase();
    setVinNumber(upperText);
  };

  const loadVIN = async () => {
    // VIN validation (matching VehicleEditPage)
    const vinPattern = /^[a-zA-Z0-9]+$/;
    
    if (!vinNumber.trim()) {
      showCustomAlert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        'VIN is required',
      );
      return;
    }

    if (!vinPattern.test(vinNumber)) {
      showCustomAlert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        'VIN must contain only alphanumeric characters',
      );
      return;
    }

    if (vinNumber.length !== 17) {
      showCustomAlert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    
    setLoading(true);
    const vinApiData = await searchVIN(vinNumber);
    if (vinApiData) {
      const processedVin = processVIN(vinApiData, vinNumber);
      setVinData({
        vinNumber,
        vehicleInfo: processedVin,
      });
      if (!processedVin.valid) {
        showCustomAlert('VIN Error', processedVin.error || 'Invalid VIN data');
      } else {
        showCustomAlert('Success', 'VIN loaded successfully');
      }
    }
    setLoading(false);
  };

  const handleContinueWithVehicle = async () => {
    if (!vinData.vehicleInfo.valid) {
      showCustomAlert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }
    onContinue();
  };

  const handleSkipVehicle = async () => {
    if (!isSkipEnabled()) {
      showCustomAlert(
        'Incomplete Information',
        'Please complete the required information in the previous steps before skipping.',
        [
          {
            text: 'Go Back',
            onPress: onNavigateBack,
          },
          {
            text: 'Cancel',
            onPress: hideAlert,
          },
        ]
      );
      return;
    }
    onContinue();
  };

  const BUTTON_HEIGHT = 120 + (insets.bottom > 0 ? insets.bottom : 16);

  return (
    <View style={vinModernStyles.outerContainer}>
      <ScrollView
        style={vinModernStyles.scrollView}
        contentContainerStyle={[
          vinModernStyles.scrollViewContent,
          {paddingBottom: BUTTON_HEIGHT + 40}
        ]}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        bounces={false}>
        <View style={vinModernStyles.vehicleSection}>
          <RegistrationTitleSection
            title={AppStrings.MCX_MY_VEHICLE_TITLE}
            backgroundColor="#FFFFFF"
            borderBottomWidth={1}
            borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
            paddingVertical={18}
            paddingHorizontal={0}
          />
          <View style={vinModernStyles.inputMainContainer}>
            <View style={vinModernStyles.inputContainer}>
              <Text style={vinModernStyles.inputLabel}>
                {AppStrings.MCX_VIN_LABEL}
              </Text>
              <CommonTextInput
                value={vinNumber}
                onChangeText={handleVinChange}
                placeholder="Enter 17-digit VIN number"
                style={vinModernStyles.vinInput}
                placeholderTextColor="#9CA3AF"
                maxLength={17}
                autoCapitalize="characters"
                autoCorrect={false}
                autoComplete="off"
                keyboardType="default"
              />
            </View>
            <CustomButton
              text={loading ? 'Loading...' : AppStrings.MCX_LOAD_VIN_BUTTON}
              onPress={loadVIN}
              variant="primary"
              size="small"
              backgroundColor={Colors.PRIMARY}
              textColor="#fff"
              style={vinModernStyles.loadVinButton}
              isBoldText={true}
              disabled={loading}
            />
          </View>
          {vinData.vehicleInfo.valid && (
            <View style={vinModernStyles.vehicleInfoContainer}>
              <VinDetailsComponent
                vin={vinData.vehicleInfo.vin}
                make={vinData.vehicleInfo.make}
                model={vinData.vehicleInfo.model}
                manufactureYear={vinData.vehicleInfo.manufactureYear}
                fuel={vinData.vehicleInfo.fuel}
              />
            </View>
          )}
        </View>
      </ScrollView>
      
      <View
        style={[
          vinModernStyles.fixedBottomContainer,
          {
            paddingBottom: insets.bottom > 0 ? insets.bottom : 16,
          }
        ]}>
        <CustomButton
          text="Skip Now"
          onPress={handleSkipVehicle}
          variant="outline"
          size="large"
          fullWidth={true}
          backgroundColor="#FFFFFF"
          textColor={Colors.SECONDARY}
          isBoldText={true}
          style={vinModernStyles.skipButton}
          disabled={loading}
        />
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinueWithVehicle}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={loading || !vinData.vehicleInfo.valid}
        />
      </View>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

const vinModernStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: 0,
  },
  vehicleSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputMainContainer: {
    paddingHorizontal: 0,
  },
  inputContainer: {
    marginBottom: 8,
    paddingHorizontal: 10,
    paddingTop: 8,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#374151',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 10,
    letterSpacing: 0.3,
  },
  vinInput: {
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    fontSize: 15,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#F9FAFB',
    paddingVertical: 14,
    paddingHorizontal: 16,
    // REMOVED: textTransform: 'uppercase' - this was causing the autocomplete issue
    // We handle uppercase in the handleVinChange function instead
  },
  loadVinButton: {
    borderRadius: 10,
    paddingVertical: 12,
    minHeight: 'auto',
    marginTop: 8,
    marginBottom: 12,
    marginHorizontal: 20,
  },
  vehicleInfoContainer: {
    marginTop: 0,
    marginBottom: 12,
    paddingHorizontal: 10,
  },
  skipButton: {
    borderWidth: 2,
    borderColor: Colors.SECONDARY,
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
  },
  fixedBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
});

export default LoadVinScreen;