import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import CustomAlert from '../../../../components/common/CustomAlert';
import {Colors, Fonts} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useAuth} from '../../../../utils/configs/AuthContext';

interface RegistrationPersonalDetailsProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  setPersonalData: (data: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  }) => void;
  onContinue: () => void;
  isSignupWithSocial?: boolean;
}

const RegistrationPersonalDetails: React.FC<
  RegistrationPersonalDetailsProps
> = ({
  personalData,
  setPersonalData,
  onContinue,
  isSignupWithSocial = false,
}) => {
  const [errors, setErrors] = React.useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
  }>({});
  const [touched, setTouched] = React.useState<{
    firstName: boolean;
    lastName: boolean;
    email: boolean;
    mobile: boolean;
  }>({
    firstName: false,
    lastName: false,
    email: false,
    mobile: false,
  });
  const [isEmailAlreadyRegistered, setIsEmailAlreadyRegistered] =
    React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [rawMobile, setRawMobile] = React.useState('');
  const insets = useSafeAreaInsets();
  const prevMobileRef = React.useRef('');
  const emailCheckTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = React.useState<boolean>(false);
  const [alertTitle, setAlertTitle] = React.useState<string>('');
  const [alertMessage, setAlertMessage] = React.useState<string>('');
  const [alertButtons, setAlertButtons] = React.useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  // Get isAvailableEmail from AuthContext - you need to add this to your AuthContext
  const authContext = useAuth();

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    if (cleanMobile.length !== 10) {
      return 'Enter a valid 10-digit mobile number.';
    }
    return '';
  };

  const formatPhoneNumber = (digits: string) => {
    if (digits.length === 0) return '';
    if (digits.length <= 3) {
      return `(${digits}`;
    }
    if (digits.length <= 6) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    }
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(
      6,
      10,
    )}`;
  };

  // ✅ NEW: Check email availability (Ionic behavior)
  const checkEmailAvailability = async (email: string) => {
    if (isSignupWithSocial) {
      return; // Skip for social signup
    }

    if (!email.trim() || !/\S+@\S+\.\S+/.test(email)) {
      setIsEmailAlreadyRegistered(false);
      return;
    }

    try {
      const emailExists = await authContext.isAvailableEmail(email);
      setIsEmailAlreadyRegistered(emailExists);

      if (emailExists) {
        setErrors(prev => ({
          ...prev,
          email: 'This email is already registered.',
        }));
      } else {
        setErrors(prev => ({
          ...prev,
          email: '',
        }));
      }
    } catch (error) {
      console.error('Email availability check error:', error);
      setIsEmailAlreadyRegistered(false);
    }
  };

  const validateField = (field: string, value: string) => {
    let error = '';
    if (!isSignupWithSocial) {
      if (field === 'firstName' && !value.trim()) {
        error = 'Enter first name.';
      } else if (field === 'lastName' && !value.trim()) {
        error = 'Enter last name.';
      } else if (field === 'email') {
        if (!value.trim()) {
          error = 'Enter a email.';
        } else if (!/\S+@\S+\.\S+/.test(value)) {
          error = 'Enter a valid email.';
        }
      }
    } else {
      if (
        field === 'firstName' &&
        personalData.firstName.trim() === '' &&
        !value.trim()
      ) {
        error = 'Enter first name.';
      } else if (
        field === 'lastName' &&
        personalData.lastName.trim() === '' &&
        !value.trim()
      ) {
        error = 'Enter last name.';
      }
    }

    if (field === 'mobile') {
      if (!value.trim()) {
        error = 'Enter a mobile number.';
      } else {
        error = validateMobile(value);
      }
    }
    setErrors(prev => ({...prev, [field]: error}));
  };

  const handleBlur = (field: string, value: string) => {
    setTouched(prev => ({...prev, [field]: true}));
    validateField(field, value);

    // ✅ NEW: Check email availability on blur
    if (field === 'email') {
      // Debounce email check
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current);
      }
      emailCheckTimeoutRef.current = setTimeout(() => {
        checkEmailAvailability(value);
      }, 500); // Wait 500ms after user stops typing
    }
  };

  const validate = () => {
    const newErrors: {
      firstName?: string;
      lastName?: string;
      email?: string;
      mobile?: string;
    } = {};

    if (!isSignupWithSocial) {
      if (!personalData.firstName.trim()) {
        newErrors.firstName = 'Enter first name.';
      }

      if (!personalData.lastName.trim()) {
        newErrors.lastName = 'Enter last name.';
      }

      if (!personalData.email.trim()) {
        newErrors.email = 'Enter a email.';
      } else if (!/\S+@\S+\.\S+/.test(personalData.email)) {
        newErrors.email = 'Enter a valid email.';
      } else if (isEmailAlreadyRegistered) {
        newErrors.email = 'This email is already registered.';
      }
    } else {
      if (
        personalData.firstName.trim() === '' &&
        !personalData.firstName.trim()
      ) {
        newErrors.firstName = 'Enter first name.';
      }

      if (
        personalData.lastName.trim() === '' &&
        !personalData.lastName.trim()
      ) {
        newErrors.lastName = 'Enter last name.';
      }
    }

    if (!personalData.mobile.trim()) {
      newErrors.mobile = 'Enter a mobile number.';
    } else {
      const mobileError = validateMobile(personalData.mobile);
      if (mobileError) {
        newErrors.mobile = mobileError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    // Mark all fields as touched to show validation errors
    setTouched({
      firstName: true,
      lastName: true,
      email: true,
      mobile: true,
    });

    if (!validate()) {
      return;
    }

    // ✅ NEW: Final check for email already registered
    if (isEmailAlreadyRegistered && !isSignupWithSocial) {
      showCustomAlert(
        'Email Already Registered',
        'This email is already registered. Please use a different email.',
      );
      return;
    }

    setLoading(true);
    try {
      onContinue();
    } catch (error: any) {
      showCustomAlert(
        'Error',
        error.message || 'Failed to save personal information',
      );
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    return () => {
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current);
      }
    };
  }, []);

  const scrollViewRef = React.useRef<ScrollView>(null);

  const BUTTON_HEIGHT = 60 + (insets.bottom > 0 ? insets.bottom : 16);

  return (
    <View style={personalModernStyles.outerContainer}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? insets.bottom * 3 : 0}
        style={personalModernStyles.container}>
        <ScrollView
          ref={scrollViewRef}
          style={personalModernStyles.scrollView}
          contentContainerStyle={[
            personalModernStyles.scrollViewContent,
            {paddingBottom: BUTTON_HEIGHT + 40},
          ]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          bounces={false}>
        <View style={personalModernStyles.formSection}>
          <RegistrationTitleSection
            title="PERSONAL INFORMATION"
            backgroundColor="#FFFFFF"
            borderBottomWidth={1}
            borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
            paddingVertical={18}
            paddingHorizontal={0}
          />

          <View style={personalModernStyles.inputContainer}>
            <Text style={personalModernStyles.inputLabel}>
              {AppStrings.MCX_FIRST_NAME_TITLE}
            </Text>
            <CommonTextInput
              value={personalData.firstName}
              onChangeText={text => {
                if (
                  !(isSignupWithSocial && personalData.firstName.trim() !== '')
                ) {
                  setPersonalData({...personalData, firstName: text});
                }
              }}
              onEndEditing={() => handleBlur('firstName', personalData.firstName)}
              placeholder="Enter your first name"
              style={personalModernStyles.textInput}
              placeholderTextColor="#9CA3AF"
              disabled={
                isSignupWithSocial && personalData.firstName.trim() !== ''
              }
            />
            {errors.firstName &&
              touched.firstName &&
              !(isSignupWithSocial && personalData.firstName.trim() !== '') && (
                <Text style={personalModernStyles.errorText}>
                  {errors.firstName}
                </Text>
              )}
          </View>

          <View style={personalModernStyles.inputContainer}>
            <Text style={personalModernStyles.inputLabel}>
              {AppStrings.MCX_LAST_NAME_TITLE}
            </Text>
            <CommonTextInput
              value={personalData.lastName}
              onChangeText={text => {
                if (
                  !(isSignupWithSocial && personalData.lastName.trim() !== '')
                ) {
                  setPersonalData({...personalData, lastName: text});
                }
              }}
              onEndEditing={() => handleBlur('lastName', personalData.lastName)}
              placeholder="Enter your last name"
              style={personalModernStyles.textInput}
              placeholderTextColor="#9CA3AF"
              disabled={
                isSignupWithSocial && personalData.lastName.trim() !== ''
              }
            />
            {errors.lastName &&
              touched.lastName &&
              !(isSignupWithSocial && personalData.lastName.trim() !== '') && (
                <Text style={personalModernStyles.errorText}>
                  {errors.lastName}
                </Text>
              )}
          </View>

          <View style={personalModernStyles.inputContainer}>
            <Text style={personalModernStyles.inputLabel}>
              {AppStrings.MCX_EMAIL_INPUT_TITLE}
            </Text>
            <CommonTextInput
              value={personalData.email}
              onChangeText={text => {
                if (!isSignupWithSocial) {
                  setPersonalData({...personalData, email: text});
                  // Clear email already registered error while typing
                  if (isEmailAlreadyRegistered) {
                    setIsEmailAlreadyRegistered(false);
                  }
                }
              }}
              onFocus={() => {
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({animated: true});
                }, 100);
              }}
              onEndEditing={() => handleBlur('email', personalData.email)}
              placeholder="Enter your email address"
              style={personalModernStyles.textInput}
              placeholderTextColor="#9CA3AF"
              keyboardType="email-address"
              disabled={isSignupWithSocial}
            />
            {errors.email && touched.email && !isSignupWithSocial && (
              <Text style={personalModernStyles.errorText}>{errors.email}</Text>
            )}
          </View>

          <View style={personalModernStyles.inputContainer}>
            <Text style={personalModernStyles.inputLabel}>
              {AppStrings.MCX_MOBILE_NUMBER_TITLE}
            </Text>
            <CommonTextInput
              value={personalData.mobile}
              onChangeText={text => {
                const currentDigits = text.replace(/\D/g, '');
                setRawMobile(currentDigits);
                const formatted = formatPhoneNumber(currentDigits);
                prevMobileRef.current = formatted;
                setPersonalData({...personalData, mobile: formatted});
              }}
              onFocus={() => {
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({animated: true});
                }, 100);
              }}
              onEndEditing={() => handleBlur('mobile', personalData.mobile)}
              placeholder="(*************"
              style={personalModernStyles.textInput}
              placeholderTextColor="#9CA3AF"
              keyboardType="phone-pad"
              maxLength={14}
            />
            {errors.mobile && touched.mobile && (
              <Text style={personalModernStyles.errorText}>
                {errors.mobile}
              </Text>
            )}
          </View>
        </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <View
        style={[
          personalModernStyles.fixedBottomContainer,
          {
            paddingBottom: insets.bottom > 0 ? insets.bottom : 16,
          },
        ]}>
        <CustomButton
          text={loading ? 'Saving...' : AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={loading || isEmailAlreadyRegistered}
        />
      </View>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

const personalModernStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    position: 'relative',
  },
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: 0,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputContainer: {
    marginBottom: 10,
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#374151',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 10,
    letterSpacing: 0.3,
  },
  textInput: {
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    fontSize: 15,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#F9FAFB',
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 13,
    marginTop: 6,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
  },
  fixedBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
});

export default RegistrationPersonalDetails;