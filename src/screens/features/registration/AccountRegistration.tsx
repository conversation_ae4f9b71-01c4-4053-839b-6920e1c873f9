import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Text,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
  GC_APPLE_FIRST_NAME,
  GC_APPLE_EMAIL,
} from '../../../utils/globals';

import {useNavigation} from '@react-navigation/native';
import TitleSection from '../../../components/common/TitleSection';

import AppBar from '../../../components/common/AppBar';
import {registrationTabData} from '../../../utils/templates/TemplateConfig';
import {AppStrings, RouteNames} from '../../../utils/constants/AppStrings';
import {Colors} from '../../../utils/constants/Theme';
import RegistrationPersonalDetails from './components/RegistrationPersonalDetails';
import RegistrationCreatePassword from './components/RegistrationCreatePassword';
import LoadVinScreen from './components/LoadVinScreen';
import AccountCreatedScreen from './components/AccountCreatedScreen';
import CustomAlert from '../../../components/common/CustomAlert';
import {AnalyticService} from '../../../utils/services/AnalyticService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from '../../../utils/configs/AuthContext';
import {UserInfo} from '../../../utils/configs/types';
import {getAuth} from '@react-native-firebase/auth';
import {navigationRef} from '../../../navigations/navigationRef';
import AppBackground from '../../../components/ui/AppBackground';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
const {AddAnalyticsScreenView, AddAnalyticCustomer} = AnalyticService;

type TabType = keyof typeof registrationTabData;

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

const AccountRegistration = () => {
  const [activeTab, setActiveTab] = useState<TabType>('1');
  const [isSignupWithSocial, setIsSignupWithSocial] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [_userInfo, setUserInfo] = useState({} as UserInfo);
  const [isFromSignup, setIsFromSignup] = useState(false);
  const insets = useSafeAreaInsets();

  const {fetchUserInfo} = useAuth();

  const [personalData, setPersonalData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
  });
  const [vinData, setVinData] = useState({
    vinNumber: '',
    vehicleInfo: {} as VehicleInfo,
  });
  const [passwordData, setPasswordData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    password: '',
  });
  const navigation = useNavigation();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    const initialiseScreen = async () => {
      try {
        setIsLoading(true);
        const _userId = (await AsyncStorage.getItem(GC_CUSTOMER_ID)) as string;
        const providerType = await AsyncStorage.getItem(
          GC_SIGNUP_PROVIDER_TYPE_PREF,
        );

        await AddAnalyticCustomer();
        AddAnalyticsScreenView('Signup Page', '/#/signup');

        const isSocial = providerType && providerType !== 'normal';
        if (isSocial && _userId) {
          setIsSignupWithSocial(true);
          setIsFromSignup(true);
          showCustomAlert(
            'Profile Information',
            'Please complete your profile information to proceed.',
          );
          const auth = getAuth();
          const firebaseUser = auth.currentUser;
          if (providerType === 'google' && firebaseUser) {
            const displayNameParts = firebaseUser.displayName?.split(' ') || [];
            setPersonalData({
              firstName: displayNameParts[0] || '',
              lastName: displayNameParts[1] || '',
              email: firebaseUser.email || '',
              mobile: '',
            });
          } else if (providerType === 'apple') {
            try {
              const storedName = (await AsyncStorage.getItem(
                GC_APPLE_FIRST_NAME,
              )) as string;
              const storedEmail = (await AsyncStorage.getItem(
                GC_APPLE_EMAIL,
              )) as string;
              if (storedName) {
                const parts = storedName.split('|');
                setPersonalData(prev => ({
                  ...prev,
                  firstName: parts[0] || '',
                  lastName: parts[1] || '',
                }));
              }
              if (storedEmail) {
                setPersonalData(prev => ({
                  ...prev,
                  email: storedEmail,
                }));
              }
            } catch (e) {
              console.warn('Could not read Apple prefill info', e);
            }
          } else if (providerType === 'facebook' && firebaseUser) {
            const displayNameParts = firebaseUser.displayName?.split(' ') || [];
            setPersonalData({
              firstName: displayNameParts[0] || '',
              lastName: displayNameParts[1] || '',
              email: firebaseUser.email || '',
              mobile: '',
            });
          }
          setActiveTab('1');
        } else {
          // Email signup - fetch existing user info if any
          if (_userId) {
            const _userInfo = await fetchUserInfo(_userId);
            if (_userInfo != null) {
              setUserInfo(_userInfo);
              // FIX: Use proper state update instead of mutation
              updateFormValues(_userInfo);

              // FIX: Check for 'COMPLETED' instead of 'registered'
              if (
                _userInfo['registration-status'] &&
                _userInfo['registration-status'] === 'COMPLETED'
              ) {
                setIsFromSignup(true);
                navigation.navigate(RouteNames.MCX_NAV_DashBoard as never);
                setIsLoading(false);
                return;
              }
            }
          }
        }
      } catch (error) {
        console.error('Error during initialization:', error);
        showCustomAlert('Error', 'Failed to initialize. Please try again.');
        navigation.navigate(RouteNames.MCX_NAV_LoginMainScreen as never);
      } finally {
        setIsLoading(false);
      }
    };
    initialiseScreen();
  }, []);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabType);
  };

  useEffect(() => {
    setUserData({
      firstName: personalData.firstName,
      lastName: personalData.lastName,
      email: personalData.email,
      mobile: personalData.mobile,
      password: passwordData.password,
    });
  }, [personalData, passwordData]);

  // FIX: Properly update form values using setPersonalData
  const updateFormValues = (userInfo: UserInfo) => {
    if (userInfo.loginParty !== 'normal') {
      setIsSignupWithSocial(true);
      console.log('isSignupWithSocial :', true);
    }

    setPersonalData({
      firstName: userInfo['first-name'] || '',
      lastName: userInfo['last-name'] || '',
      email: userInfo.email || '',
      mobile: userInfo.mobile || '',
    });
  };

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile?.replace(/\D/g, '');
    return cleanMobile?.length === 10;
  };

  const isTab1Valid = () => {
    return (
      personalData.firstName.trim() !== '' &&
      personalData.lastName.trim() !== '' &&
      validateEmail(personalData.email?.trim()) &&
      validateMobile(personalData.mobile?.trim())
    );
  };

  const isTab2Valid = () => {
    return (
      passwordData.password.trim().length >= 8 &&
      passwordData.confirmPassword.trim() !== '' &&
      passwordData.password === passwordData.confirmPassword &&
      /[A-Z]/.test(passwordData.password) &&
      /[a-z]/.test(passwordData.password) &&
      /\d/.test(passwordData.password)
    );
  };

  const isFirstThreeTabsValid = () => {
    const personalValid = isTab1Valid();
    const passwordValid = isSignupWithSocial ? true : isTab2Valid();
    const vinValid = vinData.vehicleInfo.valid === true;

    return personalValid && passwordValid && vinValid;
  };

  const isSkipEnabled = () => {
    if (isSignupWithSocial) {
      return isTab1Valid();
    } else {
      return isTab1Valid() && isTab2Valid();
    }
  };

  const canAccessTab = (tabKey: string): boolean => {
    if (tabKey === '1') return true;

    if (isSignupWithSocial) {
      if (tabKey === '3') {
        return isTab1Valid();
      }
      if (tabKey === '4') {
        return isTab1Valid();
      }
    } else {
      if (tabKey === '2') {
        return isTab1Valid();
      }
      if (tabKey === '3') {
        return isTab1Valid() && isTab2Valid();
      }
      if (tabKey === '4') {
        return isTab1Valid() && isTab2Valid();
      }
    }
    return false;
  };

  const handleTabPress = (tab: TabType) => {
    if (!canAccessTab(tab)) {
      showCustomAlert(
        'Incomplete Information',
        'Please complete all required information in previous steps before proceeding.',
      );
      return;
    }
    setActiveTab(tab);
  };

  const {signOut} = useAuth();

  const handleBackPress = async () => {
    try {
      const auth = getAuth();
      if (auth.currentUser) {
        await signOut();
      }
      navigationRef.current?.reset({
        index: 0,
        routes: [{name: RouteNames.MCX_NAV_LoginMainScreen}],
      });
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  const getDisplayTabNumber = (key: string): string => {
    if (isSignupWithSocial) {
      if (key === '1') return '1';
      if (key === '3') return '2';
      if (key === '4') return '3';
    }
    return key;
  };

  const isTabValid = (tabKey: string) => {
    switch (tabKey) {
      case '1':
        return isTab1Valid();
      case '2':
        return isSignupWithSocial ? true : isTab2Valid();
      case '3':
        return vinData.vehicleInfo.valid;
      case '4':
        return true;
      default:
        return false;
    }
  };

  const renderTabs = () => {
    const tabs = Object.entries(registrationTabData).filter(
      ([key]) =>
        !isSignupWithSocial || key === '1' || key === '3' || key === '4',
    );

    return (
      <SafeAreaView
        style={[modernStyles.tabsContainer, {paddingTop: insets.top}]}>
        {tabs.map(([key, label], index) => {
          const isActive = activeTab === key;
          const isDisabled = !canAccessTab(key);
          const isCompleted = isSignupWithSocial
            ? (key === '1' && (activeTab === '3' || activeTab === '4')) ||
              (key === '3' && activeTab === '4')
            : parseInt(key) < parseInt(activeTab);
          const isValid = isTabValid(key);
          const displayNumber = getDisplayTabNumber(key);

          return (
            <React.Fragment key={key}>
              <TouchableOpacity
                style={[
                  modernStyles.tab,
                  isDisabled && modernStyles.tabDisabled,
                ]}
                onPress={() => handleTabPress(key as TabType)}
                disabled={isDisabled}>
                <View
                  style={[
                    modernStyles.tabNumber,
                    isActive && modernStyles.tabNumberActive,
                    isCompleted &&
                      (isValid
                        ? modernStyles.tabNumberCompleted
                        : modernStyles.tabNumberInvalid),
                  ]}>
                  {isCompleted ? (
                    isValid ? (
                      <Icon name="check" size={16} color="#FFFFFF" />
                    ) : (
                      <Icon name="close" size={16} color="#FFFFFF" />
                    )
                  ) : (
                    <Text
                      style={[
                        modernStyles.tabNumberText,
                        isActive && modernStyles.tabNumberTextActive,
                      ]}>
                      {displayNumber}
                    </Text>
                  )}
                </View>
                <Text
                  style={[
                    modernStyles.tabLabel,
                    isActive && modernStyles.tabLabelActive,
                    isDisabled && modernStyles.tabLabelDisabled,
                    isCompleted &&
                      (isValid
                        ? modernStyles.tabLabelCompleted
                        : modernStyles.tabLabelInvalid),
                  ]}
                  numberOfLines={1}>
                  {displayNumber}
                </Text>
              </TouchableOpacity>

              {index < tabs.length - 1 && (
                <View
                  style={[
                    modernStyles.tabConnector,
                    isCompleted &&
                      (isValid
                        ? modernStyles.tabConnectorCompleted
                        : modernStyles.tabConnectorInvalid),
                  ]}
                />
              )}
            </React.Fragment>
          );
        })}
      </SafeAreaView>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab(isSignupWithSocial ? '3' : '2')}
            isSignupWithSocial={isSignupWithSocial}
          />
        );
      case '2':
        return (
          <RegistrationCreatePassword
            personalData={personalData}
            passwordData={passwordData}
            setPasswordData={setPasswordData}
            setActiveTab={handleTabChange}
          />
        );
      case '3':
        return (
          <LoadVinScreen
            vinData={vinData}
            setVinData={setVinData}
            onContinue={() => setActiveTab('4')}
            isSkipEnabled={isSkipEnabled}
            onNavigateBack={() => setActiveTab('1')}
          />
        );
      case '4':
        return (
          <AccountCreatedScreen
            onContinue={async () => {
              setTimeout(() => {
                navigationRef.current?.reset({
                  index: 0,
                  routes: [{name: RouteNames.MCX_NAV_DashBoard}],
                });
              }, 300);
            }}
            vinData={vinData}
            userData={userData}
            isSignupWithSocial={isSignupWithSocial}
          />
        );
      default:
        return (
          <RegistrationPersonalDetails
            personalData={personalData}
            setPersonalData={setPersonalData}
            onContinue={() => setActiveTab('2')}
            isSignupWithSocial={isSignupWithSocial}
          />
        );
    }
  };

  return (
    <SafeAreaView style={[modernStyles.mainContainer, {paddingTop: insets.top}]}>
      {isLoading && (
        <View style={modernStyles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
        </View>
      )}

      <AppBackground />

      <AppBar
        showBackButton={true}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onBackPress={handleBackPress}
      />

      <TitleSection
        title={AppStrings.MCX_ACCOUNT_REGISTRATION_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={modernStyles.titleSection}
      />

      <View style={modernStyles.container}>
        {renderTabs()}
        <View style={modernStyles.contentContainer}>
          {renderTabContent()}
        </View>
      </View>

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </SafeAreaView>
  );
};

const modernStyles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 12,
  },
  tabsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    padding: 6,
  },
  tabDisabled: {
    opacity: 0.5,
  },
  tabNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  tabNumberActive: {
    backgroundColor: '#A10000',
    shadowColor: '#A10000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  tabNumberCompleted: {
    backgroundColor: '#10B981',
  },
  tabNumberInvalid: {
    backgroundColor: '#DC2626',
  },
  tabNumberText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#6B7280',
  },
  tabNumberTextActive: {
    color: '#FFFFFF',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    textAlign: 'center',
  },
  tabLabelActive: {
    color: '#A10000',
    fontWeight: '700',
  },
  tabLabelDisabled: {
    color: '#9CA3AF',
  },
  tabLabelCompleted: {
    color: '#10B981',
    fontWeight: '600',
  },
  tabLabelInvalid: {
    color: '#DC2626',
    fontWeight: '600',
  },
  tabConnector: {
    height: 2,
    width: 20,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 4,
    marginBottom: 32,
  },
  tabConnectorCompleted: {
    backgroundColor: '#10B981',
  },
  tabConnectorInvalid: {
    backgroundColor: '#DC2626',
  },
  contentContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default AccountRegistration;