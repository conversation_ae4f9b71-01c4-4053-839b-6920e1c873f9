import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ProfileCard from '../../../components/common/ProfileCard';
import ServicesMessage from '../../../components/common/ServiceMessage';
import LoaderSpin from '../../../components/common/LoaderSpin';
import RecordNotFound from '../../../components/common/RecordNotFound';
import AppBackground from '../../../components/ui/AppBackground';
import { VehicleService } from '../../../utils/services/VehicleService';
import { get } from '@react-native-firebase/database';
import { GC_CUSTOMER_ID } from '../../../utils/globals';
import {BottomTabNavigationProp} from '@react-navigation/bottom-tabs';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import NetInfo from '@react-native-community/netinfo';

type AppointmentsListProps = {
  navigation: BottomTabNavigationProp<any>;
};

type AppointmentDetails = {
  'mechanic-img': string;
  'mechanic-name': string;
  'mechanic-address': string;
  'mechanic-availability': string;
  'mechanic-rate': number;
  'schedule-time': number | undefined;
  day: string;
  month: string;
  services: any[];
  time: string;
  mechanic: string;
};

type Appointment = {
  'is-approved'?: boolean;
  details: AppointmentDetails;
  'work-request-id': string;
  'appointment-id'?: string;
  'work-status'?: string;
  'created-time'?: number;
};

const AppointmentsList = ({navigation}: AppointmentsListProps) => {
  const nav = useNavigation();
  const [appointmentArray, setAppointmentArray] = useState<Appointment[]>([]);
  const [completedAppointmentArray, setCompletedAppointmentArray] = useState<
    Appointment[]
  >([]);
  const [loaderAllAppointment, setLoaderAllAppointment] = useState(false);
  const [loaderHistoryAppointment, setLoaderHistoryAppointment] =
    useState(false);

  const loadAppointments = useCallback(async () => {
    setLoaderAllAppointment(false);

    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) {
      setLoaderAllAppointment(true);
      setAppointmentArray([]);
      return;
    }

    try {
      const appointmentSnapshot = await get(VehicleService.getAppointments(customerId));
      setLoaderAllAppointment(false);
      const appointmentRes = appointmentSnapshot.val();
      if (!appointmentRes) {
        setLoaderAllAppointment(true);
        setAppointmentArray([]);
        return;
      }

      const keys = Object.keys(appointmentRes);


      const appointmentPromises = keys.map(async (appointmentKey) => {
        try {
          const appointmentDetailSnapshot = await get(VehicleService.fetchAppointmentDetails(appointmentKey));
          const resDetailAppointment = appointmentDetailSnapshot.val();
          const workReqId = resDetailAppointment[appointmentKey]['work-request-id'];
          const workRequestSnapshot = await get(VehicleService.fetchWorkRequest(workReqId));
          const workReqDetail = workRequestSnapshot.val();
          const scheduleTime = resDetailAppointment[appointmentKey]['schedule-time'];
          const date = new Date(scheduleTime * 1000);
          const dayName = date.getDate().toString();
          const month = VehicleService.monthNames[date.getMonth()];
          const tempSer = workReqDetail[workReqId].services;
          const serId = Object.keys(tempSer);
          const allSubServiceIds: string[] = [];
          const servicePromises: Promise<any>[] = [];
          const subServicePromises: Promise<any>[] = [];

          serId.forEach((serviceId) => {
            const tempSubser = workReqDetail[workReqId].services[serviceId]['sub-services'];
            tempSubser.forEach((subServiceId: string) => {
              if (!allSubServiceIds.includes(subServiceId)) {
                allSubServiceIds.push(subServiceId);
                subServicePromises.push(get(VehicleService.fetchSubServiceName(subServiceId)));
              }
            });
            servicePromises.push(get(VehicleService.fetchServiceName(serviceId)));
          });
          const mechanicPromise = get(VehicleService.getMechanicDetail(workReqDetail[workReqId].mechanic));
          const [subServiceSnapshots, serviceSnapshots, mechanicSnapshot] = await Promise.all([
            Promise.all(subServicePromises),
            Promise.all(servicePromises),
            mechanicPromise,
          ]);
          const subServiceMap: {[key: string]: string} = {};
          subServiceSnapshots.forEach((snapshot, index) => {
            const val = snapshot.val();
            const subServiceId = allSubServiceIds[index];
            const subServiceData = val ? Object.values(val)[0] : null;
            subServiceMap[subServiceId] = (subServiceData as any)?.name || 'Unknown Subservice';
          });
          const serviceMap: {[key: string]: string} = {};
          serviceSnapshots.forEach((snapshot, index) => {
            const val = snapshot.val();
            const serviceId = serId[index];
            const serviceData = val ? Object.values(val)[0] : null;
            serviceMap[serviceId] = (serviceData as any)?.name || 'Unknown Service';
          });
          const exitServiceName = serId.map((serviceId) => {
            const tempSubser = workReqDetail[workReqId].services[serviceId]['sub-services'];
            const exitSubServiceName = tempSubser.map((subServiceId: string) => subServiceMap[subServiceId] || 'Unknown');
            return {
              service: serviceMap[serviceId],
              image: workReqDetail[workReqId].services[serviceId].image || 'assets/imgs/default-service.jpg', // Use actual service image if available
              subservice: exitSubServiceName,
            };
          });

          const mechanicDetail = mechanicSnapshot.val() || {};
          const details = {
            services: exitServiceName,
            'schedule-time': scheduleTime,
            time:  workReqDetail[workReqId]['request-time-range'] || 'Time not available',
            day: dayName,
            month: month,
            mechanic: workReqDetail[workReqId].mechanic,
            'mechanic-name': (mechanicDetail['first-name'] || '') + ' ' + (mechanicDetail['last-name'] || ''),
            'mechanic-rate': mechanicDetail['mechanic-rating'] || 0,
            'mechanic-img': mechanicDetail.imageUrl || 'path_to_default_image',
            'mechanic-address': mechanicDetail.address2 || 'Address not available',
            'mechanic-availability': mechanicDetail.availability ? 'Open' : 'Appointment',
          };

          return {
            'work-request-id': workReqId,
            'created-time': resDetailAppointment[appointmentKey]['created-time'] || workReqDetail[workReqId]['created-time'],
            details: details,
            'work-status': workReqDetail[workReqId].status,
            'appointment-id': workReqDetail[workReqId].appointmentId,
            'is-approved': workReqDetail[workReqId].isApproved,
          };
        } catch (error) {
          console.error('Error fetching appointment details:', error);
          return null;
        }
      });

      const tempAppointmentArray = (await Promise.all(appointmentPromises)).filter(item => item !== null);
      setAppointmentArray(tempAppointmentArray);
      setLoaderAllAppointment(true);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setLoaderAllAppointment(true);
    }
  }, []);

const getOtherAppointment = useCallback(async () => {
  setLoaderHistoryAppointment(false);
  try {
    const allAcceptedResponse = await get(
      await VehicleService.getAllWorkRequest(),
    );
    const allAcceptedData = allAcceptedResponse.val();
    console.log('allAcceptedData:', allAcceptedData);

    if (!allAcceptedData) {
      setCompletedAppointmentArray([]);
      return;
    }

    const keys = Object.keys(allAcceptedData);
    console.log('keys:', keys);

    const appointmentPromises = keys.map(async workReqId => {
      console.log('Processing work request ID:', workReqId);

      try {
        const workReqSnapshot = await get(
          VehicleService.fetchWorkRequest(workReqId),
        );
        const workReqDetail = workReqSnapshot.val();

        if (!workReqDetail || !workReqDetail[workReqId]?.paymentStatus) {
          return null;
        }

        const workRequestData = workReqDetail[workReqId];
        const date = new Date(workRequestData['request-date']);
        date.setMinutes(date.getMinutes() + date.getTimezoneOffset());
        const dayName = date.getDate().toString();
        const month = VehicleService.monthNames[date.getMonth()];

        const tempSer = workRequestData.services;
        if (!tempSer) {
          return null;
        }

        const serId = Object.keys(tempSer);
        const servicePromises = serId.map(async svId => {
          const tempSubser =
            workRequestData.services[svId]['sub-services'] || [];

          const subServicePromises = tempSubser.map(async (ssv: string) => {
            const subServiceSnapshot = await get(
              VehicleService.fetchSubServiceName(ssv),
            );
            const subServiceData = subServiceSnapshot.val();
            return subServiceData?.[ssv]?.name || 'Unknown';
          });

          const subServiceNames = await Promise.all(subServicePromises);
          const serviceSnapshot = await get(
            VehicleService.fetchServiceName(svId),
          );
          const serviceData = serviceSnapshot.val();
          const serviceName = serviceData?.[svId]?.name || 'Unknown Service';

          return {
            service: serviceName,
            image: 'assets/imgs/oil-icon.jpg',
            subservice: subServiceNames,
          };
        });

        const exitServiceName = await Promise.all(servicePromises);

        let mechanic = {
          'first-name': '',
          'last-name': '',
          'mechanic-rating': 0,
          imageUrl: '',
          address2: '',
          availability: false,
        };

        if (workRequestData.mechanic) {
          const mechanicSnapshot = await get(
            VehicleService.getMechanicDetail(workRequestData.mechanic),
          );
          const mechanicData = mechanicSnapshot.val();
          if (mechanicData) {
            mechanic = {
              ...mechanic,
              ...mechanicData,
            };
          }
        }

        const details = {
          services: exitServiceName,
          time: workRequestData['request-time-range'] || 'Time not available',
          day: dayName,
          month: month,
          mechanic: workRequestData.mechanic || '',
          'mechanic-name':
            `${mechanic['first-name'] || ''} ${
              mechanic['last-name'] || ''
            }`.trim() || 'Mechanic not assigned',
          'mechanic-rate': mechanic['mechanic-rating'] || 0,
          'mechanic-img': mechanic.imageUrl || 'path_to_default_image',
          'mechanic-address': mechanic.address2 || 'Address not available',
          'mechanic-availability': mechanic.availability
            ? 'Open'
            : 'Appointment',
        };

        return {
          'work-request-id': workReqId,
          'created-time': workRequestData['created-time'] || Date.now(),
          details: details,
          // 'work-status': workRequestData.status || 'completed',
          // 'is-approved': workRequestData.isApproved || false,
        };
      } catch (error) {
        console.error(`Error processing work request ${workReqId}:`, error);
        return null;
      }
    });

    const completedAppointments = (
      await Promise.all(appointmentPromises)
    ).filter(Boolean);
    setCompletedAppointmentArray(completedAppointments as Appointment[]);
  } catch (error) {
    console.error('Error in getOtherAppointment:', error);
  } finally {
    setLoaderHistoryAppointment(true);
  }
  }, []);

  const loadCompletedAppointments = useCallback(() => {
    setLoaderHistoryAppointment(false);
    getOtherAppointment().finally(() => {
      setLoaderHistoryAppointment(true);
    });
  }, [getOtherAppointment]);

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        nav.navigate('NetworkFailedPage' as never);
        return;
      }

      loadAppointments();
      loadCompletedAppointments();
    };

    checkNetworkAndLoad();
  }, [loadAppointments, loadCompletedAppointments, nav]);

  useFocusEffect(
    useCallback(() => {
      loadAppointments();
    }, [loadAppointments])
  );

  const timeBetweenDates = (scheduleTime: number | undefined) => {
    if (!scheduleTime) {
      return '0 sec';
    }
    const now = new Date();
    const scheduled = new Date(scheduleTime * 1000);
    const timeDiff = scheduled.getTime() - now.getTime();

    if (timeDiff <= 0) {
      return '0 sec';
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    const parts = [];
    if (days > 0) {
      parts.push(`${days} day${days > 1 ? 's' : ''}`);
    }
    if (hours > 0) {
      parts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
    }
    if (minutes > 0) {
      parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);
    }

    return parts.join(', ') || '0 sec';
  };

  const goToAppointmentDetail = (appointment: Appointment) => {
    const rootNavigation = navigation.getParent()?.getParent();
    if (rootNavigation) {
      if (appointment['is-approved']) {
        rootNavigation.navigate('AppointmentDetails', { appointment });
      } else {
        rootNavigation.navigate('ConfirmAppointment', {
          'appointment-detail': appointment,
          origin: 'appointments',
        });
      }
    }
  };

  const goToCompletedAppointment = useCallback(
    (completedAppointment: Appointment) => {
      console.log('completedAppointment:', completedAppointment);
      const rootNavigation = navigation.getParent()?.getParent();
      const params = {'appointment-detail': completedAppointment};
      console.log('params:', params);
      if (rootNavigation) {
        rootNavigation.navigate('CompletedSession', params);
      }
    },
    [navigation],
  );

  const renderAppointmentItem = (
    appointment: Appointment,
    index: number,
    isLast: boolean,
  ) => (
    <TouchableOpacity
      key={index}
      style={styles.appointmentItem}
      onPress={() => goToAppointmentDetail(appointment)}
      activeOpacity={0.7}>
      <View style={styles.profileCardContainer}>
        <ProfileCard
          imageUrl={appointment.details['mechanic-img']}
          name={appointment.details['mechanic-name']}
          address={appointment.details['mechanic-address']}
          availabilityStatus={appointment.details['mechanic-availability']}
          rate={appointment.details['mechanic-rate']}
        />
      </View>

      {appointment['is-approved'] &&
        timeBetweenDates(appointment.details['schedule-time']) !== '0 sec' && (
          <View style={styles.sessionTimerContainer}>
            <Text style={styles.sessionTimer}>
              Session starts in{' '}
              {timeBetweenDates(appointment.details['schedule-time'])}
            </Text>
          </View>
        )}

      {appointment['is-approved'] &&
        timeBetweenDates(appointment.details['schedule-time']) === '0 sec' && (
          <View style={styles.awaitingContainer}>
            <Text style={[ styles.awaitingText]}>
              Awaiting mechanic arrival
            </Text>
          </View>
        )}

      <View style={styles.appointmentDetails}>
        <View style={styles.messagesBoxArea}>
          {appointment['is-approved'] ? (
            <ServicesMessage
              isIcon={true}
              day={appointment.details.day}
              month={appointment.details.month}
              services={appointment.details.services}
              time={appointment.details.time}
            />
          ) : (
            <View style={styles.statusContainer}>
              <ServicesMessage
                isIcon={true}
                day={appointment.details.day}
                month={appointment.details.month}
                services={appointment.details.services}
                time={appointment.details.time}
                status="Mechanic waiting for your confirmation"
              />
            </View>
          )}
        </View>
      </View>

      {!isLast && <View style={styles.blankSpace} />}
    </TouchableOpacity>
  );

  const renderCompletedAppointmentItem = (
    completedAppointment: Appointment,
    index: number,
    isLast: boolean,
  ) => (
    <TouchableOpacity
      key={index}
      style={styles.appointmentItem}
      onPress={() => goToCompletedAppointment(completedAppointment)}
      activeOpacity={0.7}>
      <View style={styles.profileCardContainer}>
        <ProfileCard
          imageUrl={completedAppointment.details['mechanic-img']}
          name={completedAppointment.details['mechanic-name']}
          address={completedAppointment.details['mechanic-address']}
          availabilityStatus={
            completedAppointment.details['mechanic-availability']
          }
          rate={completedAppointment.details['mechanic-rate']}
        />
      </View>

      <View style={styles.appointmentDetails}>
        <View style={styles.messagesBoxArea}>
          <ServicesMessage
            isIcon={true}
            day={completedAppointment.details.day}
            month={completedAppointment.details.month}
            services={completedAppointment.details.services}
            time={completedAppointment.details.time}
          />
        </View>
      </View>

      {!isLast && <View style={styles.blankSpace} />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <AppBackground />
      <ScrollView
        style={styles.content}
        contentContainerStyle={[styles.contentContainer, styles.scrollContent]}>
        <View style={styles.listContainer}>
          <CommonCardStyle header="UPCOMING APPOINTMENTS"textColor="#ffff">
            {!loaderAllAppointment && (
              <View style={styles.loaderContainer}>
                <LoaderSpin />
              </View>
            )}

            {appointmentArray.length === 0 && loaderAllAppointment && (
              <RecordNotFound />
            )}

            {appointmentArray.length > 0 && loaderAllAppointment && (
              <View style={styles.profileDetail}>
                {appointmentArray.map((appointment, index) =>
                  renderAppointmentItem(
                    appointment,
                    index,
                    index === appointmentArray.length - 1,
                  ),
                )}
              </View>
            )}
          </CommonCardStyle>
          <CommonCardStyle header="OTHER APPOINTMENTS"textColor="#ffff">
            {!loaderHistoryAppointment && (
              <View style={styles.loaderContainer}>
                <LoaderSpin />
              </View>
            )}

            {completedAppointmentArray.length === 0 &&
              loaderHistoryAppointment && <RecordNotFound />}

            {completedAppointmentArray.length > 0 && loaderHistoryAppointment && (
              <View style={styles.profileDetail}>
                {completedAppointmentArray.map((completedAppointment, index) =>
                  renderCompletedAppointmentItem(
                    completedAppointment,
                    index,
                    index === completedAppointmentArray.length - 1,
                  ),
                )}
              </View>
            )}
          </CommonCardStyle>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: '#4a5c6a',
  },
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#4a5c6a',
  },
  logo: {
    height: 30,
    width: 120,
    position: 'absolute',
    left: 0,
    right: 0,
    alignSelf: 'center',
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  scrollContent: {
    paddingBottom: 100, // Add extra padding to ensure content doesn't get cut off by bottom bar
  },
  listContainer: {
    backgroundColor: 'transparent',
  },
  mainTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 16,
  },
  loaderContainer: {
    backgroundColor: '#fff',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  profileDetail: {
    marginBottom: 16,
  },
  appointmentItem: {
    backgroundColor: '#fff',
    marginBottom: 10,
    flex: 1,
  },
  profileCardContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  sessionTimerContainer: {
    backgroundColor: '#646363ff',
    paddingHorizontal: 16,
    paddingLeft: 10,
    alignSelf: 'flex-start',
    maxWidth: '100%',
  },
  awaitingContainer: {
    backgroundColor: '#999696ff',
    marginLeft: 12,
    width: "57%",
    height: 22,
    alignSelf: 'flex-start',
  }
  ,
  sessionTimer: {
    fontSize: 12,
    color: '#ffffff',
    fontStyle: 'italic',
    fontWeight: '600',
    flexWrap: 'wrap',
  },
  awaitingText: {
    color: '#ece6e6ff',
    fontWeight: 'bold',
    fontSize: 13,
    paddingLeft: 2,
    paddingRight: 2,
    textAlign: 'center',
  },
  appointmentDetails: {
    backgroundColor: 'transparent',
    padding: 10,
    flex: 1,
  },
  messagesBoxArea: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  blankSpace: {
    height: 2,
    backgroundColor: 'transparent',
  },
  statusContainer: {
    flex: 1,
    width: '100%',
  },
});

export default AppointmentsList;
