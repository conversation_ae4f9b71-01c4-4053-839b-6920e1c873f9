import { StyleSheet, Platform } from 'react-native';
import { Colors } from '../../../utils/constants/Theme';

// Modern styling toggle - set to true for modern styles, false for original
const useModernStyles = true;

// Modern color palette with glassmorphism effects
const modernColors = {
  background: '#1a1a2e',
  glassmorphismPrimary: 'rgba(255, 255, 255, 0.1)',
  glassmorphismSecondary: 'rgba(255, 255, 255, 0.15)',
  glassmorphismCard: 'rgba(255, 255, 255, 0.95)',
  modernGradient1: 'rgba(161, 0, 0, 0.8)',
  modernGradient2: 'rgba(161, 0, 0, 0.6)',
  modernShadow: 'rgba(0, 0, 0, 0.15)',
  modernBorder: 'rgba(255, 255, 255, 0.3)',
  modernText: '#ffffff',
  modernTextSecondary: 'rgba(255, 255, 255, 0.9)',
  modernOverlay: 'rgba(0, 0, 0, 0.3)',
  cardText: '#333333',
  headerBg: '#1a1a2e', // Solid color for better status bar handling
};

// Modern styles with glassmorphism effects
const modernStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: modernColors.headerBg,
  },
  container: {
    flex: 1,
    backgroundColor: modernColors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: modernColors.headerBg,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  radioGroup: {
    marginBottom: 12,
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: modernColors.cardText,
  },
  radioCircleSelected: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 6,
    borderColor: modernColors.modernGradient1,
  },
  optionText: {
    fontSize: 15,
    color: modernColors.cardText,
    flex: 1,
    fontWeight: '500',
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: modernColors.modernText,
    transform: [{ rotate: '180deg' }],
  },
    disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  disabledText: {
    color: '#666666',
  },
  disabledInput: {
    backgroundColor: '#f0f0f0',
    color: '#666666',
  },
  bidErrorText: {
  color: '#FF6B6B',
  fontSize: 12,
  fontWeight: '600',
  marginTop: 4,
  marginLeft: 8,
  marginBottom: 8,
},
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: modernColors.modernText,
    fontSize: 18,
    fontWeight: '700',
    marginRight: 24,
    letterSpacing: 0.5,
  },
   locationInputPlaceholder: {
    color: '#666666', // Darker gray color for better placeholder visibility
  },
  scrollContent: {
    padding: Platform.OS === 'android' ? 20 : 0,
    paddingBottom: Platform.OS === 'android'  ? 100 : 0,
  },
  bottomButtonsContainer: {
    backgroundColor: modernColors.background,
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 20 : 16,
    borderTopWidth: 1,
    borderTopColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },

  sectionTitle: {
    color: modernColors.modernText,
    fontWeight: '700',
    fontSize: 18,
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  mechanicCard: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 8,
  },
  mechanicImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  mechanicInfo: {
    flex: 1,
  },
  mechanicName: {
    fontWeight: '700',
    fontSize: 18,
    color: modernColors.cardText,
    marginBottom: 4,
  },
    section: {
    marginBottom: Platform.OS === 'android' ? 24 : 0,
  },
  mechanicLocation: {
    fontSize: 14,
    color: modernColors.cardText,
    opacity: 0.7,
    marginBottom: 8,
  },
  ratingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingStars: {
    color: modernColors.modernGradient1,
    fontSize: 16,
  },
  availabilityText: {
    fontSize: 14,
    color: modernColors.cardText,
  },
  appointmentText: {
    color: modernColors.modernGradient1,
    fontWeight: '600',
  },
  dropdownContainer: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  dropdownLabel: {
    fontWeight: '600',
    color: modernColors.cardText,
    marginBottom: 8,
    fontSize: 16,
  },
  input: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: modernColors.cardText,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  locationInputContainer: {
    marginBottom: 12,
    zIndex: 1000,
  },
  locationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.COMMON_BlACK_SHADE,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    zIndex: 1000,
  },
  locationListView: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  locationRow: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    padding: 13,
    height: 44,
    flexDirection: 'row',
  },
  locationDescription: {
    color: Colors.COMMON_BlACK_SHADE,
  },
  locationDisplay: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  locationText: {
    fontSize: 16,
    color: modernColors.cardText,
  },
  // Additional modern styles for complete functionality
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 12,
  },
  imageContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 12,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: modernColors.modernGradient1,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    elevation: 3,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  deleteIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    color: modernColors.cardText,
  },
  validIndicator: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
  },
  priceModelTable: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  priceModelHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: modernColors.modernGradient1,
    paddingBottom: 8,
  },
  priceModelHeaderCell: {
    fontWeight: '700',
    color: modernColors.modernGradient1,
    fontSize: 14,
  },
  priceModelRow: {
    flexDirection: 'row',
    paddingVertical: 12,
  },
  priceModelCell: {
    fontSize: 14,
    color: modernColors.cardText,
  },
  priceModelTotalRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: modernColors.modernGradient1,
    paddingTop: 12,
  },
  priceModelTotalText: {
    fontWeight: '700',
    color: modernColors.modernGradient1,
    fontSize: 14,
  },
  imageButton: {
    marginTop: 12,
    borderRadius: 12,
    backgroundColor: modernColors.glassmorphismPrimary,
    alignItems: 'center',
    justifyContent: 'center',
    width: 56,
    height: 56,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  imageIcon: {
    width: 24,
    height: 24,
    tintColor: modernColors.modernText,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: modernColors.modernBorder,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  cancelButtonText: {
    color: modernColors.modernText,
    fontWeight: '600',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: modernColors.modernGradient1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  confirmButtonText: {
    color: modernColors.modernText,
    fontWeight: '700',
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#FF6B6B',
    fontWeight: '600',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 4,
  },
  servicesTitle: {
    color: modernColors.modernText,
    fontWeight: '700',
    fontSize: 18,
    marginBottom: 16,
    backgroundColor: modernColors.modernGradient1,
    padding: 12,
    borderRadius: 8,
  },
  serviceItem: {
    marginBottom: 20,
  },
  removeServiceButton: {
    backgroundColor: '#FF6B6B',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
  },
  removeServiceText: {
    color: modernColors.modernText,
    fontWeight: '600',
  },
  subServiceContainer: {
    marginTop: 12,
  },
  addServiceButton: {
    backgroundColor: modernColors.modernGradient1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 12,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  addServiceText: {
    color: modernColors.modernText,
    fontWeight: '600',
    fontSize: 16,
  },
  saveLocationButton: {
    marginTop: 12,
    padding: 12,
    borderRadius: 8,
    backgroundColor: modernColors.modernGradient1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
    minWidth: 180,
  },
  saveLocationText: {
    color: modernColors.modernText,
    fontWeight: '600',
    fontSize: 16,
  },
  errorInput: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
  },
  errorDropdown: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
    borderRadius: 12,
  },
  dateInput: {
    backgroundColor: modernColors.glassmorphismCard,
    borderRadius: 12,
    padding: 16,
    minHeight: 48,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  dateInputText: {
    fontSize: 16,
    color: modernColors.cardText,
    fontWeight: '500',
  },
  dateInputPlaceholder: {
    color: 'rgba(51, 51, 51, 0.5)',
    fontWeight: '400',
  },
  dateModalContainer: {
    width: '100%',
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  dateModalContent: {
    width: 340,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: modernColors.modernShadow,
    shadowOffset: {width: 0, height: 6},
  },
    dateModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  dateModalButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    minHeight: 44,
    justifyContent: 'center',
    shadowColor: modernColors.modernShadow,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 2,
  },
  dateModalCancelButton: {
    backgroundColor: '#f2f2f2',
    marginRight: 8,
  },
  dateModalSetButton: {
    backgroundColor: modernColors.modernGradient1,
  },
  dateModalButtonText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 16,
  },
  dateModalSetButtonText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 16,
  },
  availableText: {
    color: '#4CAF50',
  },
  notAvailableText: {
    color: '#FF6B6B',
  },
  rateText: {
    marginTop: 8,
    color: modernColors.cardText,
    fontSize: 14,
    fontWeight: '500',
  },
  priceModelHeaderCellService: {
    flex: 3,
  },
  priceModelHeaderCellRange: {
    flex: 2,
  },
  priceModelHeaderCellBid: {
    flex: 2,
  },
  priceModelCellService: {
    flex: 3,
  },
  priceModelCellRange: {
    flex: 2,
  },
  priceModelInput: {
    flex: 2,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: modernColors.glassmorphismCard,
    color: modernColors.cardText,
  },
  priceModelTotalTextService: {
    flex: 3,
  },
  priceModelTotalTextSpacer: {
    flex: 2,
  },
  priceModelTotalTextValue: {
    flex: 2,
  },
  subServiceList: {
    marginTop: 8,
  },
  selectedSubService: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedSubServiceText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
    flex: 1,
    fontWeight: '500',
  },
  subServiceCheckboxContainer: {
    marginTop: 12,
  },
  subServiceCheckboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: modernColors.cardText,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: modernColors.modernGradient1,
    borderColor: modernColors.modernGradient1,
  },
  checkboxCheckmark: {
    color: modernColors.modernText,
    fontSize: 14,
    fontWeight: 'bold',
  },
  subServiceCheckboxText: {
    fontSize: 14,
    color: modernColors.cardText,
    flex: 1,
  },
  removeSubServiceButton: {
    backgroundColor: '#FF6B6B',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  removeSubServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  addSubServiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  addSubServiceButton: {
    backgroundColor: Colors.PRIMARY,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
   // elevation: 4,
  },
  addSubServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 18,
  },
  loaderContainer: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 8,
    fontSize: 14,
    color: '#8B0000',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});

// Original styles (preserved for easy reversion)
const originalStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#1f2a3a',
  },
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2a3a',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
    transform: [{ rotate: '180deg' }],
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 24,
  },
  scrollContent: {
    padding: Platform.OS === 'android' ? 16 : 0,
    paddingBottom: Platform.OS === 'android'  ? 20 : 0,
  },
  bottomButtonsContainer: {
    backgroundColor: Colors.BACKGROUND,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 20 : 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  section: {
    marginBottom: Platform.OS === 'android' ? 24 : 0,
  },
  sectionTitle: {
    color: Colors.COMMON_BlACK_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 12,
  },
   disabledInput: {
    backgroundColor: '#f0f0f0',
    color: '#666666',
  },
  mechanicCard: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
    locationListView: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  locationRow: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    padding: 13,
    height: 44,
    flexDirection: 'row',
  },
  locationDescription: {
    color: Colors.COMMON_BlACK_SHADE,
  },
   locationInputPlaceholder: {
    color: '#666666', // Darker gray color for better placeholder visibility
  },
  mechanicImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  bidErrorText: {
  color: '#FF0000',
  fontSize: 12,
  fontWeight: '600',
  marginTop: 4,
  marginBottom: 4,
},
  mechanicInfo: {
    flex: 1,
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  mechanicLocation: {
    fontSize: 14,
    color: Colors.PRIMARY_DARK,
  },
  ratingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  ratingStars: {
    color: Colors.PRIMARY,
    fontSize: 16,
  },
  availabilityText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  appointmentText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  dropdownContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginBottom: 8,
  },
  dropdownLabel: {
    fontWeight: 'bold',
    color: Colors.TEXT_COLOR,
    marginBottom: 4,
    flex: 1,
  },
  input: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  radioGroup: {
    marginBottom: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  radioCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.TEXT_COLOR,
    marginRight: 8,
  },
  radioCircleSelected: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  optionText: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  locationInputContainer: {
    marginBottom: 8,
    zIndex: 1000,
  },
  locationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    zIndex: 1000,
  },
  locationDisplay: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginTop: 8,
  },
  locationText: {
    fontSize: 16,
    color: Colors.TEXT_COLOR,
  },
  // Additional styles for complete functionality
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    gap: 8,
  },

  imageContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  deleteIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  validIndicator: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
  },
  priceModelTable: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 8,
  },
  priceModelHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.PRIMARY,
    paddingBottom: 4,
  },
  priceModelHeaderCell: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  priceModelRow: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  priceModelCell: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  priceModelTotalRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: Colors.PRIMARY,
    paddingTop: 8,
  },
  priceModelTotalText: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  imageButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.SECONDARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
  },
  imageIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
  cancelButton: {
    backgroundColor: Colors.BACKGROUND,
    borderWidth: 2,
    borderColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 12,
  },
  cancelButtonText: {
    color: Colors.COMMON_BlACK_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 14,
    borderRadius: 4,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#FF0000',
    fontWeight: '600',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 4,
  },
  servicesTitle: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 12,
    backgroundColor: '#9b0a0f',
    padding: 8,
  },
  serviceItem: {
    marginBottom: 16,
  },
  removeServiceButton: {
    backgroundColor: '#ff4444',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    alignItems: 'center',
  },
  removeServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
  },
  subServiceContainer: {
    marginTop: 8,
  },
  addServiceButton: {
    backgroundColor: Colors.PRIMARY,
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 8,
  },
  addServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  subServiceList: {
    marginTop: 8,
  },
  selectedSubService: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: modernColors.glassmorphismCard,
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: modernColors.modernBorder,
  },
  selectedSubServiceText: {
    fontSize: 14,
    color: modernColors.cardText,
    flex: 1,
    fontWeight: '500',
  },
  removeSubServiceButton: {
    backgroundColor: '#FF6B6B',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  removeSubServiceText: {
    color: modernColors.modernText,
    fontWeight: 'bold',
    fontSize: 16,
  },
  subServiceCheckboxContainer: {
    marginTop: 12,
  },
  subServiceCheckboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: modernColors.cardText,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: modernColors.modernGradient1,
    borderColor: modernColors.modernGradient1,
  },
  checkboxCheckmark: {
    color: modernColors.modernText,
    fontSize: 14,
    fontWeight: 'bold',
  },
  subServiceCheckboxText: {
    fontSize: 14,
    color: modernColors.cardText,
    flex: 1,
  },
  addSubServiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  addSubServiceButton: {
    backgroundColor: modernColors.modernGradient1,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: modernColors.modernShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  addSubServiceText: {
    color: modernColors.modernText,
    fontWeight: 'bold',
    fontSize: 18,
  },
  saveLocationButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
    minWidth: 180,
  },
  saveLocationText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  disabledText: {
    color: '#666666',
  },
  errorInput: {
    borderColor: '#FF0000',
    borderWidth: 2,
  },
  errorDropdown: {
    borderColor: '#FF0000',
    borderWidth: 2,
    borderRadius: 4,
  },
  dateInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    minHeight: 40,
    justifyContent: 'center',
  },
  dateInputText: {
    fontSize: 16,
    color: Colors.TEXT_COLOR,
    fontWeight: '400',
  },
  dateInputPlaceholder: {
    color: '#999',
    fontWeight: '400',
  },
  // Modal container to center the date picker on iOS
  dateModalContainer: {
    width: '100%',
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  dateModalContent: {
    width: 320,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.12,
    shadowRadius: 10,
    elevation: 10,
  },
  dateModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  dateModalButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 6,
    minHeight: 44,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  dateModalCancelButton: {
    backgroundColor: '#f2f2f2',
    marginRight: 8,
  },
  dateModalSetButton: {
    backgroundColor: Colors.PRIMARY,
  },
  dateModalButtonText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 16,
  },
  availableText: {
    color: 'green',
  },
  notAvailableText: {
    color: 'red',
  },
  rateText: {
    marginTop: 4,
    color: '#444',
    fontSize: 14,
  },
  priceModelHeaderCellService: {
    flex: 3,
  },
  priceModelHeaderCellRange: {
    flex: 2,
  },
  priceModelHeaderCellBid: {
    flex: 2,
  },
  priceModelCellService: {
    flex: 3,
  },
  priceModelCellRange: {
    flex: 2,
  },
  priceModelInput: {
    flex: 2,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 8,
  },
  priceModelTotalTextService: {
    flex: 3,
  },
  priceModelTotalTextSpacer: {
    flex: 2,
  },
  priceModelTotalTextValue: {
    flex: 2,
  },
  loaderContainer: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 8,
    fontSize: 14,
    color: '#8B0000',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});

// Style selection based on toggle
export const styles = useModernStyles ? modernStyles : originalStyles;
