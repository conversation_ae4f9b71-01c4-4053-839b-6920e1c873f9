/**
 * BookAppointmentScreen with Modern Styling
 *
 * Features modern glassmorphism design with enhanced visual appeal.
 * Toggle 'useModernStyles' constant to switch between modern and original styling.
 */
import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Platform,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import {request, check, PERMISSIONS, RESULTS} from 'react-native-permissions';
import DateTimePicker from '@react-native-community/datetimepicker';
import Modal from 'react-native-modal';
import {useNavigation, useRoute, useFocusEffect} from '@react-navigation/native';
import {useForm, useFieldArray, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {launchImageLibrary} from 'react-native-image-picker';
import Geolocation from 'react-native-geolocation-service';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import {
  AppCommonIcons,
  AppStrings,
  RouteNames,
} from '../../../utils/constants/AppStrings';
import {MechanicItem} from '../../../utils/configs/types';
import CommonDropdown from '../../../components/common/CommonDropdown';
import {VehicleService} from '../../../utils/services/VehicleService';
import {useAuth} from '../../../utils/configs/AuthContext';
import axios from 'axios';
import {GooglePlacesAutocompleteDefaultProps} from '../../../utils/configs/GooglePlacesAutocompleteProps';
import Config from 'react-native-config';
import {get} from '@react-native-firebase/database';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import StarRating from '../../../components/common/StarRating';
import {styles} from './BookAppointmentsScreen.styles';
import {child, set} from '@react-native-firebase/database';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import NetInfo from '@react-native-community/netinfo';
import CustomAlert from '../../../components/common/CustomAlert';
const createValidationSchema = (screeningQuestions: any) => {
  const screeningValidation: any = {};
  Object.keys(screeningQuestions).forEach(key => {
    const question = screeningQuestions[key];
    if (question && !question.hide) {
      if (key === 'que-2') {
        screeningValidation[key] = yup
          .string()
          .when('que-1', ([que1], schema) => {
            return que1?.toLowerCase() === 'yes'
              ? schema.required('Choose atleast one')
              : schema.nullable();
          });
      } else if (key === 'que-6') {
        screeningValidation[key] = yup
          .string()
          .when('que-5', ([que5], schema) => {
            return que5?.toLowerCase() === 'yes'
              ? schema.required('Choose atleast one')
              : schema.nullable();
          });
      } else {
        if (question.conditional) {
          const conditionKey = question.conditional.dependsOn;
          const conditionValue = question.conditional.value;

          screeningValidation[key] = yup
            .string()
            .when(conditionKey, ([dependentValue], schema) => {
              return dependentValue === conditionValue
                ? schema.required(question.error || 'Choose atleast one')
                : schema.nullable();
            });
        } else {
          // FIX: Use the error message from the question data based on type
          const errorMessage = question.error || 
            (question.type === 'entry' ? 'Type here' : 'Choose atleast one');
          screeningValidation[key] = yup
            .string()
            .required(errorMessage);
        }
      }
    }
  });

  return yup.object().shape({
    yourvehicle: yup.string().required('Please select your vehicle'),
    requestlocation: yup.string().required('Please select a location type'),
    locationInput: yup
      .mixed()
      .when('requestlocation', ([requestlocation], schema) => {
        return requestlocation === 'searchlocation'
          ? schema.required('Please select a location')
          : schema;
      }),
    serviceNeed: yup
      .array()
      .of(
        yup.object().shape({
          vehicleservice: yup.string().required('Please select a service'),
          subServices: yup
            .array()
            .of(
              yup.object().shape({
                id: yup.string().required('Sub-service ID is required'),
                name: yup.string().required('Sub-service name is required'),
              }),
            )
            .min(1, 'At least one sub-service is required'),
        }),
      )
      .min(1, 'At least one service is required'),
    alertconfirmation: yup
      .string()
      .required('Please select alert confirmation'),
    scheduledate: yup
  .mixed()
  .required('Choose date for service')
  .test('is-date', 'Choose date for service', (value) => {
    if (!value) return false;
    if (value instanceof Date) return !isNaN(value.getTime());
    if (typeof value === 'string') return !isNaN(Date.parse(value));
    return false;
  }),
    notes: yup.string(),
    ...screeningValidation,
  });
};

// Modern styling toggle - set to true for modern styles, false for original
const useModernStyles = true;

// Modern color palette with glassmorphism effects
const modernColors = {
  background: '#1a1a2e',
  glassmorphismPrimary: 'rgba(255, 255, 255, 0.1)',
  glassmorphismSecondary: 'rgba(255, 255, 255, 0.15)',
  glassmorphismCard: 'rgba(255, 255, 255, 0.95)',
  modernGradient1: 'rgba(161, 0, 0, 0.8)',
  modernGradient2: 'rgba(161, 0, 0, 0.6)',
  modernShadow: 'rgba(0, 0, 0, 0.15)',
  modernBorder: 'rgba(255, 255, 255, 0.3)',
  modernText: '#ffffff',
  modernTextSecondary: 'rgba(255, 255, 255, 0.9)',
  modernOverlay: 'rgba(0, 0, 0, 0.3)',
  cardText: '#333333',
  headerBg: '#1a1a2e', // Solid color for better status bar handling
};

const BookAppointmentsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {user} = useAuth();
  const {mechanic, selectedAvailability} = route.params as {
    mechanic: MechanicItem;
    selectedAvailability?: string;
  };
  const googlePlacesRef = useRef<any>(null);
  const [completeServiceArray, setCompleteServiceArray] = useState<any[]>([]);

  // const [timeRange, setTimeRange] = useState('');
  // const [showTimePicker, setShowTimePicker] = useState<boolean>(false);
  // const [timePickerValue, setTimePickerValue] = useState<Date>(new Date());
  const scrollViewRef = useRef<ScrollView>(null);
  const getFullName = () => {
    if (mechanic?.['first-name'] && mechanic?.['last-name']) {
      return `${mechanic['first-name']} ${mechanic['last-name']}`;
    }
    return mechanic?.name || 'Unknown Mechanic';
  };


  const [validationSchema, setValidationSchema] = useState(() =>
    createValidationSchema({}),
  );

  const {
    control,
    watch,
    setValue,
    formState: {errors},
    trigger,
    clearErrors,
    register,
    reset,
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      yourvehicle: '',
      requestlocation: 'currentlocation',
      locationInput: {},
      serviceNeed: [{vehicleservice: '', subServices: []}],
      alertconfirmation: '',
      scheduledate: null,
      // timerange: '',
      notes: '',
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const {
    fields: serviceFields,
    append: appendService,
    remove: removeService,
  } = useFieldArray({
    control,
    name: 'serviceNeed',
  });

  const addSubService = (serviceIndex: number, subService: any) => {
    const currentServices = watch('serviceNeed');
    const updatedServices = [...currentServices];
    const currentSubServices = updatedServices[serviceIndex].subServices || [];
    if (!currentSubServices.find((s: any) => s.id === subService.id)) {
      updatedServices[serviceIndex].subServices = [
        ...currentSubServices,
        subService,
      ];
      setValue(
        `serviceNeed.${serviceIndex}.subServices`,
        updatedServices[serviceIndex].subServices,
      );
    }
  };

  const removeSubService = (serviceIndex: number, subServiceId: string) => {
    const currentServices = watch('serviceNeed');
    const updatedServices = [...currentServices];
    updatedServices[serviceIndex].subServices = updatedServices[
      serviceIndex
    ].subServices.filter((s: any) => s.id !== subServiceId);
    setValue(
      `serviceNeed.${serviceIndex}.subServices`,
      updatedServices[serviceIndex].subServices,
    );

    // Clean up bid rates, errors, and timeouts for the removed subService
    const bidKey = `${serviceIndex}-${subServiceId}`;
    const newBidRates = {...bidRates};
    delete newBidRates[bidKey];
    setBidRates(newBidRates);

    const newBidErrors = {...bidErrors};
    delete newBidErrors[bidKey];
    setBidErrors(newBidErrors);

    const newTimeouts = {...bidValidationTimeouts};
    if (newTimeouts[bidKey]) {
      clearTimeout(newTimeouts[bidKey]);
      delete newTimeouts[bidKey];
    }
    setBidValidationTimeouts(newTimeouts);
  };

  const [userVehicleList, setUserVehicleList] = useState<any[]>([]);
  const [isLoadingVehicles, setIsLoadingVehicles] = useState(true);
  const [customerRequestAdrress, setCustomerRequestAdrress] =
    useState<any>(null);
  const [_selectedLocation, _setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [selectedImages, setSelectedImages] = useState<any[]>([]);

  const handleDeleteImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, idx) => idx !== index));
  };
  const [savedLocations, setSavedLocations] = useState<any[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [screeningAnswers, setScreeningAnswers] = useState<
    Record<string, string | null>
  >({});
  const [date, setDate] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedSavedLocation, setSelectedSavedLocation] = useState('');
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [datePickerValue, setDatePickerValue] = useState<Date>(new Date()); 
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [prevDatePickerValue, setPrevDatePickerValue] = useState<Date | null>(null);
  const [screeningQuestions, setScreeningQuestions] = useState<any>({});
  const [alertConfirmationData, setAlertConfirmationData] = useState<any[]>([]);
  const [isLoadingAlerts, setIsLoadingAlerts] = useState(true);
  const [serviceIdNameMap, setServiceIdNameMap] = useState<
    Record<string, string>
  >({});
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [subServiceIdNameMap, setSubServiceIdNameMap] = useState<
    Record<string, string>
  >({});
  const [subServicePriceData, setSubServicePriceData] = useState<
    Record<string, any>
  >({});
  const [isLoadingSubServices, setIsLoadingSubServices] = useState(true);
  const [bidRates, setBidRates] = useState<Record<string, string>>({});
  const [bidErrors, setBidErrors] = useState<Record<string, string>>({});
  const [bidValidationTimeouts, setBidValidationTimeouts] = useState<
    Record<string, NodeJS.Timeout>
  >({});
  const [locationSaved, setLocationSaved] = useState(false);
  const [customerCard, setCustomerCard] = useState<any>(null);
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);
  const insets = useSafeAreaInsets();

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    const que1 = String(screeningAnswers['que-1'] || '').toLowerCase();
    if (que1 !== 'yes' && screeningAnswers['que-2']) {
      setScreeningAnswers(prev => ({...prev, 'que-2': null}));
      setValue('que-2', null);
      clearErrors('que-2');
      trigger('que-2');
    }

    const que5 = String(screeningAnswers['que-5'] || '').toLowerCase();
    if (que5 !== 'yes' && screeningAnswers['que-6']) {
      setScreeningAnswers(prev => ({...prev, 'que-6': null}));
      setValue('que-6', null);
      clearErrors('que-6');
      trigger('que-6');
    }
  }, [screeningAnswers, clearErrors, setValue, trigger]);

  const fetchSavedLocations = useCallback(async () => {
    try {
      if (user?.uid) {
        const locations = await VehicleService.fetchSavedLocations(user.uid);
        setSavedLocations(locations);
      }
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      setSavedLocations([]);
    } finally {
      setIsLoadingLocations(false);
    }
  }, [user?.uid]);

  useEffect(() => {
    const fetchScreeningQuestions = async () => {
      try {
        const snapshot = await get(VehicleService.getPreScreeningQuestions());
        const screeningData = snapshot.val();
        if (screeningData) {
          setScreeningQuestions(screeningData);
          setValidationSchema(createValidationSchema(screeningData));
          const initialAnswers: Record<string, string | null> = {};
          Object.keys(screeningData).forEach(key => {
            if (screeningData[key] && !screeningData[key].hide) {
              initialAnswers[key] = null;
            }
          });
          setScreeningAnswers(initialAnswers);
        } else {
          setScreeningQuestions({});
          setValidationSchema(createValidationSchema({}));
        }
      } catch (error) {
        console.error('Error fetching screening questions:', error);
        setScreeningQuestions({});
        setValidationSchema(createValidationSchema({}));
      }
    };
    const fetchAlertConfirmations = async () => {
      try {
        const data = await VehicleService.getAlertConfirmation();
        setAlertConfirmationData(data);
      } catch (error) {
        console.error('Error fetching alert confirmations:', error);
      } finally {
        setIsLoadingAlerts(false);
      }
    };
    const fetchServiceMaps = async () => {
      try {
        const serviceMap = await VehicleService.getServiceIdNameMap();
        const subServiceMap = await VehicleService.getSubServiceIdNameMap();
        setServiceIdNameMap(serviceMap);
        setSubServiceIdNameMap(subServiceMap);
      } catch (error) {
        console.error('Error fetching service maps:', error);
      } finally {
        setIsLoadingServices(false);
      }
    };
    fetchScreeningQuestions();
    fetchAlertConfirmations();
    fetchServiceMaps();
    const fetchSubServicePriceData = async () => {
      try {
        const priceData = await VehicleService.getSubServicePriceData();
        setSubServicePriceData(priceData);
      } catch (error) {
        console.error('Error fetching sub-service price data:', error);
      } finally {
        setIsLoadingSubServices(false);
      }
    };
    fetchSubServicePriceData();
    if (user?.uid) {
      fetchUserVehicles(user.uid);
    }
    fetchSavedLocations();
  }, [user, fetchSavedLocations]);

  useEffect(() => {
    Object.keys(screeningQuestions).forEach(key => {
      const q = screeningQuestions[key];
      if (q && !q.hide) {
        try {
          register(key);
        } catch (e) {}
        if (screeningAnswers[key] === undefined) {
          setValue(key, null);
        }
      }
    });
  }, [screeningQuestions, register, setValue, screeningAnswers]);

  useEffect(() => {
    const que1 = String(screeningAnswers['que-1'] || '').toLowerCase();
    if (que1 !== 'yes' && screeningAnswers['que-2']) {
      setScreeningAnswers(prev => ({...prev, 'que-2': null}));
      setValue('que-2', null);
      clearErrors('que-2');
    }

    const que5 = String(screeningAnswers['que-5'] || '').toLowerCase();
    if (que5 !== 'yes' && screeningAnswers['que-6']) {
      setScreeningAnswers(prev => ({...prev, 'que-6': null}));
      setValue('que-6', null);
      clearErrors('que-6');
    }
  }, [screeningAnswers, clearErrors, setValue]);

  useEffect(() => {
    if (selectedSavedLocation) {
      const location = savedLocations.find(l => l.id === selectedSavedLocation);
      if (location) {
        const addressParts = location.address
          .split(',')
          .map((part: string) => part.trim());
        setCustomerRequestAdrress({
          address_array: {
            address1: addressParts[0] || '',
            address2: '',
            city: addressParts[1] || '',
            state: addressParts[2] || '',
            country: addressParts[3] || 'India',
            zipcode: '',
          },
          formatted_address: location.address,
          location: location.coordinates,
        });
        _setSelectedLocation(location.coordinates);
      }
    }
  }, [selectedSavedLocation, savedLocations]);

  useEffect(() => {
    const subscription = watch((value, {name, type}) => {
      if (name && name.startsWith('que-') && type === 'change') {
        const timeoutId = setTimeout(() => {
          trigger(name);
        }, 50);
        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, trigger]);

  const fetchUserVehicles = async (userId: string) => {
    try {
      if (userId) {
        const vehicles = await VehicleService.getVehicles(userId);
        setUserVehicleList(vehicles);
      }
    } catch (error) {
      console.error('Error fetching user vehicles:', error);
    } finally {
      setIsLoadingVehicles(false);
    }
  };
  // Pre-load services filtered by mechanic
  const preLoadService = useCallback(async () => {
    try {
      const [serviceSnapshot, subServiceSnapshot] = await Promise.all([
        get(VehicleService.getVehicleServices()),
        get(VehicleService.refSubService()),
      ]);
      const serviceNodeVal = serviceSnapshot.val();
      const subServiceNodeVal = subServiceSnapshot.val();
      const mechanicServices = mechanic?.services
        ? Object.keys(mechanic.services)
        : [];

      const completeArray: any[] = [];
      if (serviceNodeVal && subServiceNodeVal) {
        Object.keys(serviceNodeVal).forEach(elementService => {
          const subserviceList: any[] = [];
          Object.keys(subServiceNodeVal).forEach(elementSubService => {
            if (
              subServiceNodeVal[elementSubService].service === elementService
            ) {
              subserviceList.push({
                id: elementSubService,
                name: subServiceNodeVal[elementSubService].name,
                maxprice: subServiceNodeVal[elementSubService]['max-price'],
                minprice: subServiceNodeVal[elementSubService]['min-price'],
                pricetype: subServiceNodeVal[elementSubService]['price-type'],
              });
            }
          });

          mechanicServices.forEach(mechServ => {
            if (mechServ === elementService) {
              completeArray.push({
                id: elementService,
                name: serviceNodeVal[elementService].name,
                subservice: subserviceList,
              });
            }
          });
        });
      }
      setCompleteServiceArray(completeArray);
    } catch (error) {
      console.error('Error pre-loading services:', error);
    } finally {
      setIsLoadingServices(false);
    }
  }, [mechanic?.services]);

  const handleAddPaymentMethod = useCallback(() => {
    navigation.navigate(RouteNames.MCX_NAV_EDIT_PAYMENT as never);
  }, [navigation]);

  // Bid validation

  // Enhanced bid validation with detailed error tracking
  const checkPriceModelBidList = () => {
    let hasError = false;
    let errorMessage = '';

    for (let sv = 0; sv < serviceFields.length; sv++) {
      const subServices = watch(`serviceNeed.${sv}.subServices`) || [];

      for (let subIndex = 0; subIndex < subServices.length; subIndex++) {
        const subService = subServices[subIndex];
        const bidKey = `${sv}-${subService.id}`;
        const bidRateStr = bidRates[bidKey] || '';

        if (!bidRateStr || bidRateStr.trim() === '') {
          hasError = true;
          errorMessage = 'Please add your bid on price model list';
          break;
        }

        if (subServicePriceData[subService.id]) {
          const bid = parseFloat(bidRateStr);
          const minPrice = parseFloat(
            subServicePriceData[subService.id]['min-price'] || '0',
          );
          const maxPrice = parseFloat(
            subServicePriceData[subService.id]['max-price'] || '0',
          );

          // Check if bid is a valid number and greater than 0
          if (isNaN(bid) || bid <= 0) {
            hasError = true;
            errorMessage = 'Please enter a valid bid amount';
            break;
          }

          // Check if bid is within range
          if (minPrice && maxPrice && (bid < minPrice || bid > maxPrice)) {
            hasError = true;
            errorMessage = `Please enter an amount between $${minPrice} - ${maxPrice}`;
            break;
          }
        }
      }

      if (hasError) break;
    }

    return errorMessage;
  };

  // Form cleanup for invalid services
  const removeValidationServiceForm = () => {
    const currentServices = watch('serviceNeed') as Array<{
      vehicleservice: string;
      subServices: any[];
    }>;
    const validServices = currentServices.filter(
      (service: {vehicleservice: string; subServices: any[]}) =>
        service.vehicleservice &&
        service.subServices &&
        service.subServices.length > 0,
    );
    // Clear all services first
    const servicesToRemove = serviceFields.length;
    for (let i = servicesToRemove - 1; i >= 0; i--) {
      removeService(i);
    }
    validServices.forEach((service, index) => {
      appendService(service);
    });
  };

  // Error scrolling
  const focusToFirstError = () => {
    const errorKeys = Object.keys(errors);
    if (errorKeys.length > 0) {
      const firstError = errorKeys[0];
      let ycords = 0;
      if (firstError === 'yourvehicle') ycords = 200;
      else if (firstError === 'requestlocation') ycords = 400;
      else if (firstError.startsWith('serviceNeed')) ycords = 600;
      else if (firstError.startsWith('que-')) ycords = 800;
      else if (
        firstError === 'alertconfirmation' ||
        firstError === 'scheduledate'
      )
        ycords = 1000;
      if (ycords > 0 && scrollViewRef.current) {
        scrollViewRef.current.scrollTo({y: ycords, animated: true});
      }
    }
  };
  useFocusEffect(
    React.useCallback(() => {
      // Network check - similar to Ionic's ionViewCanEnter
      const checkNetworkAndLoad = async () => {
        const state = await NetInfo.fetch();
        if (!state.isConnected) {
          console.log("Network check failed: ", state.isConnected);
          navigation.navigate('NetworkFailedPage' as never);
          return;
        }

        // Reset all screening answers first
        setScreeningAnswers({});

        // Clear all form errors explicitly
        clearErrors();

        // Reset all form state
        const resetData: any = {
          yourvehicle: '',
          requestlocation: 'currentlocation',
          locationInput: {},
          serviceNeed: [{vehicleservice: '', subServices: []}],
          alertconfirmation: '',
          scheduledate: null,
          notes: '',
        };

        reset(resetData);

        // Reset all other state
        setSelectedImages([]);
        setBidRates({});
        setBidErrors({});
        setSelectedSavedLocation('');
        setDate('');
        setNotes('');
        setShowDatePicker(false);
        setDatePickerValue(new Date());
        setLocationSaved(false);

        // Scroll to top when screen gains focus
        scrollViewRef.current?.scrollTo({ y: 0, animated: true });

        const loadCardDetailsAndCheck = async () => {
          try {
            const hasCard = await VehicleService.fetchCustomerPaymentMethod(
              user?.uid || '',
            );
            setCustomerCard(hasCard ? {} : null);
            if (!hasCard) {
              showCustomAlert(
                'No payment method',
                'You are not eligible to schedule services without a payment method. Please add one.',
                [
                  {
                    text: 'Cancel',
                    onPress: () =>
                      navigation.navigate(RouteNames.MCX_DASHBOARD as never),
                  },
                  {text: 'OK', onPress: () => handleAddPaymentMethod()},
                ],
              );
            }
          } catch (error) {
            setCustomerCard(null);
          }
        };
        if (user?.uid) {
          loadCardDetailsAndCheck();
        }
      };

      checkNetworkAndLoad();
    }, [handleAddPaymentMethod, reset, clearErrors, user?.uid, navigation]),
  );

  useEffect(() => {
    preLoadService();
  }, [preLoadService]);
   const uploadImagesToWorkRequest = async (workRequestId: string, imageUris: string[]) => {
    try {
      const uploadPromises = imageUris.map(async (uri, index) => {
        try {   
          const snapshot = await VehicleService.uploadImage(
            uri,
            workRequestId,
            index,
            'appointmentImages'
          );
          const profileURL = snapshot.metadata.fullPath;
          const downloadURL = await VehicleService.getDownloadURL(profileURL);
          return downloadURL;
        } catch (error) {
          console.error(`Error uploading image ${index}:`, error);
          throw error;
        }
      });

      // Wait for all uploads to complete
      const uploadedUrls = await Promise.all(uploadPromises);
      // Update the work request with image URLs (as array)
      await VehicleService.updateBookAppointmentImagePath(uploadedUrls, workRequestId);
      return uploadedUrls;
    } catch (error) {
      console.error('Error in uploadImagesToWorkRequest:', error);
      throw error;
    }
  };

  const handleConfirmAppointment = async (data: any) => {
    setIsLoading(true);
    try {
      //Payment check
       if (!customerCard) {
         showCustomAlert('No payment method', 'You are not eligible to pay services, please add a payment method', [
           { text: 'Cancel', onPress: () => navigation.navigate('DashBoard' as never) },
           { text: 'Add Payment', onPress: () => navigation.navigate('EditPaymentPage' as never) },
         ]);
         return;
       }

      // Bid validation
      const bidError = checkPriceModelBidList();
      if (bidError) {
        showCustomAlert('Check Bid List', bidError);
        return;
      }
      const currentTime = Math.floor(Date.now() / 1000);
      const requestDate = data.scheduledate.toISOString().split('T')[0];

      const selectedAlert = alertConfirmationData.find(
        alert => alert.value === data.alertconfirmation,
      );
      let alertValue = '30';
      if (selectedAlert?.label) {
        const match = selectedAlert.label.match(
          /(\d+(?:\.\d+)?)\s*(hour|minute)/i,
        );
        if (match) {
          const value = parseFloat(match[1]);
          const unit = match[2].toLowerCase();
          if (unit.includes('hour')) {
            alertValue = (value * 60).toString();
          } else {
            alertValue = value.toString();
          }
        }
      }

      const screeningFAQ: Record<string, any> = {};
      Object.keys(screeningAnswers).forEach(key => {
        if (screeningAnswers[key] && screeningQuestions[key]) {
          screeningFAQ[key] = {
            answer: screeningAnswers[key].toUpperCase(),
            questions: screeningQuestions[key].question,
          };
        }
      });

      const services: Record<string, any> = {};
      data.serviceNeed.forEach((service: any, index: number) => {
        if (
          service.vehicleservice &&
          service.subServices &&
          service.subServices.length > 0
        ) {
          const serviceKey = service.vehicleservice;
          const customerBids: string[] = [];
          const subServiceIds: string[] = [];

          service.subServices.forEach((subService: any, subIndex: number) => {
          const bidKey = `${index}-${subService.id}`;
            const bidRate = bidRates[bidKey] || '0';
            customerBids.push(bidRate);
            subServiceIds.push(subService.id);
          });

          services[serviceKey] = {
            'customer-bid': customerBids,
            'mechanic-bid': customerBids.map(() => '0'),
            'service-type': serviceKey,
            'sub-services': subServiceIds,
          };
        }
      });

      const workRequestData = {
        alert: alertValue,
        'created-time': currentTime,
        customer: user?.uid || '',
        latitude: customerRequestAdrress?.location?.latitude || 0,
        longitude: customerRequestAdrress?.location?.longitude || 0,
        mechanic: mechanic?.id?.toString() || '',
        notes: {
          images: '', // Empty image URL - will be updated after upload
          'notes-content': notes || '',
        },
        'request-address': {
          address_array: {
            address1: customerRequestAdrress?.address_array?.address1 || '',
            address2: customerRequestAdrress?.address_array?.address2 || '',
            city: customerRequestAdrress?.address_array?.city || '',
            country: customerRequestAdrress?.address_array?.country || 'India',
            state: customerRequestAdrress?.address_array?.state || '',
            zipcode: customerRequestAdrress?.address_array?.zipcode || '',
          },
          formatted_address: customerRequestAdrress?.formatted_address || '',
          location: {
            latitude: customerRequestAdrress?.location?.latitude || 0,
            longitude: customerRequestAdrress?.location?.longitude || 0,
          },
        },
        'request-date': requestDate,
        'request-time-range': data.timerange || '1:00 pm - 4:30 pm',
        'request-type':
          selectedAvailability === 'Open' ? 'Right Now' : 'Appointment',
        'screening-FAQ': screeningFAQ,
        services: services,
        status: 'work-request',
        vehicle: data.yourvehicle || '',
        isApproved: false,
      };

      const workRequestId = await VehicleService.createWorkRequest(
        workRequestData,
      );

      // Upload images AFTER work request creation (matching Schedule.tsx flow)
      if (selectedImages.length > 0) {
        const imageUris = selectedImages.map(img => img.uri);
        await uploadImagesToWorkRequest(workRequestId, imageUris);
      }

      // Check if "Right Now" was selected from fmAvailabilityOptions
      const availability = selectedAvailability;
      if (availability === 'Open') {
        await VehicleService.updateRightNow(workRequestId, mechanic.id); // Pass mechanic.id
      }
      // Update statuses
      await VehicleService.updateWorkRequestStatus(workRequestId, 'pending');
      // Log update
      const logRef = VehicleService.updateLog(workRequestId);
      await set(child(logRef, 'work-request/created-time'), currentTime);

      // FCM Notification
      if (mechanic?.settings?.notification?.['fcm-token']) {
        try {
          // await AppointmentService.sendFCMNotification(
          //   'Work request received',
          //   `${user?.displayName || 'Customer'} has sent a work request`,
          //   mechanic.settings.notification['fcm-token'],
          //   'workrequest',
          //   workRequestId,
          // );
          await AppointmentService.sendPUSHToUser(
            mechanic.settings.notification['fcm-token'],
            'Work request received',
            `${user?.displayName || 'Customer'} has sent a work request`,
            'workrequest',
            workRequestId,
          ).then(response => {
            console.log('fcm send');
            console.log(response);
          }).catch(notificationError => {
            console.error('Notification error:', notificationError);
          });
          console.log('FCM notification sent successfully');
        } catch (notifError) {
          console.error('Notification error:', notifError);
        }
      }

      console.log('Appointment creation completed successfully');
      showCustomAlert(
        'Appointment Requested',
        'Please wait for mechanic response...',
        [
          {
            text: 'OK',
            onPress: () =>
              navigation.navigate(RouteNames.MCX_NAV_DashBoard as never),
          },
        ],
      );
    } catch (error) {
      console.error('Error submitting appointment:', error);
      showCustomAlert('Error', 'Failed to submit appointment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  // const handleTimeChange = (event: any, selectedTime?: Date) => {
  //   if (Platform.OS === 'android') {
  //     setShowTimePicker(false);
  //   }

  //   if (selectedTime) {
  //     setTimePickerValue(selectedTime);
  //     const hours = selectedTime.getHours();
  //     const minutes = selectedTime.getMinutes();
  //     const ampm = hours >= 12 ? 'pm' : 'am';
  //     const formattedHours = hours % 12 || 12;
  //     const formattedTime = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  //     setTimeRange(formattedTime);
  //     setValue('timerange', formattedTime);

  //     if (Platform.OS === 'ios') {
  //       setShowTimePicker(false);
  //     }
  //   } else if (Platform.OS === 'android') {
  //     setShowTimePicker(false);
  //   }
  // };
  const handleSubmitWithValidation = async () => {
    try {
      const currentValues = watch();

      // Clear previous bid errors
      setBidErrors({});

      // Location validation
      let locationError = false;
      if (
        currentValues.requestlocation === 'currentlocation' &&
        !customerRequestAdrress
      ) {
        locationError = true;
        showCustomAlert(
          'Location Required',
          'Please wait for current location to load or select a different location type.',
        );
        return;
      } else if (
        currentValues.requestlocation === 'savedlocation' &&
        !selectedSavedLocation
      ) {
        locationError = true;
        showCustomAlert('Location Required', 'Please select a saved location.');
        return;
      } else if (
        currentValues.requestlocation === 'searchlocation' &&
        !customerRequestAdrress
      ) {
        locationError = true;
        showCustomAlert(
          'Location Required',
          'Please search and select a location.',
        );
        return;
      }

      const isValid = await trigger();
      if (!isValid || locationError) {
        focusToFirstError();
        if (!isValid) {
          showCustomAlert(
            'Please fill all the entries',
            'Some fields are missing or invalid. Please complete all required fields.',
          );
        }
        return;
      }

      removeValidationServiceForm();
      const screeningErrorKeys = Object.keys(errors).filter(
        key => key.startsWith('que-') && errors[key],
      );

      if (screeningErrorKeys.length > 0) {
        showCustomAlert(
          'Incomplete Screening',
          'Please complete all required screening questions before submitting.',
        );
        focusToFirstError();
        return;
      }

      // Validate bid rates with detailed error checking
      for (let index = 0; index < currentValues.serviceNeed.length; index++) {
        const service = currentValues.serviceNeed[index];

        if (!service.vehicleservice) {
          showCustomAlert(
            'Check Bid List',
            'Service type is missing for service #' + (index + 1),
          );
          return;
        }

        if (!service.subServices || service.subServices.length === 0) {
          showCustomAlert(
            'Check Bid List',
            'No sub-services selected for service #' + (index + 1),
          );
          return;
        }

        // Validate each sub-service in this service
        for (
          let subIndex = 0;
          subIndex < service.subServices.length;
          subIndex++
        ) {
          const subService = service.subServices[subIndex];
          const bidKey = `${index}-${subService.id}`;
          const bidRateStr = bidRates[bidKey] || '';
          const bidRate = parseFloat(bidRateStr);

          const subServiceData = subServicePriceData[subService.id];
          if (!subServiceData) {
            showCustomAlert(
              'Check Bid List',
              `Price data not found for sub-service "${subService.name}"`,
            );
            return;
          }

          const minPrice =
            subServiceData['min-price'] ||
            subServiceData.minPrice ||
            subServiceData.min_rate ||
            0;
          const maxPrice =
            subServiceData['max-price'] ||
            subServiceData.maxPrice ||
            subServiceData.max_rate ||
            Number.MAX_SAFE_INTEGER;

          const priceType =
            subServiceData['price-type'] ||
            subServiceData.price_type ||
            subServiceData.priceType;

          // FIXED: Skip validation for flat price types
          if (priceType === 'flat') {
            // Ensure flat price is set if not already
            if (!bidRateStr || bidRateStr.trim() === '') {
              const flatPrice =
                minPrice || subServiceData.price || subServiceData.rate || 0;
              setBidRates(prev => ({...prev, [bidKey]: flatPrice.toString()}));
            }
            continue; // Skip other validations for flat prices
          }

          if (!bidRateStr || bidRateStr.trim() === '') {
            showCustomAlert(
              'Check Bid List',
              `Please add your bid for "${subService.name}"`,
            );
            return;
          }

          if (isNaN(bidRate) || bidRate <= 0) {
            showCustomAlert(
              'Check Bid List',
              `Please enter a valid bid amount for "${subService.name}"`,
            );
            return;
          }

          if (bidRate < minPrice || bidRate > maxPrice) {
            showCustomAlert(
              'Check Bid List',
              `Please enter an amount between $${minPrice} - $${maxPrice} for "${subService.name}"`,
            );
            return;
          }
        }
      }
      await handleConfirmAppointment(currentValues);
    } catch (error) {
      console.error('Error in handleSubmitWithValidation:', error);
      showCustomAlert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const handleCancel = () => {
    showCustomAlert(
      'Confirm',
      'Are you sure you want to leave this screen? All entered data will be lost.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'OK', onPress: () => navigation.goBack()},
      ],
    );
  };

  const handleLocationTypeChange = (type: string) => {
    setValue('requestlocation', type);
    setLocationSaved(false); // Reset location saved state when changing location type
    if (type === 'currentlocation') {
      requestCurrentLocation();
    } else if (type === 'savedlocation') {
      requestSavedLocation();
    } else {
      setCustomerRequestAdrress(null);
      _setSelectedLocation(null);
      if (googlePlacesRef.current) {
        googlePlacesRef.current.setAddressText('');
      }
    }
  };

  const processLocation = useCallback(
    async (latitude: number, longitude: number) => {
      try {
        const response = await axios.get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`,
        );

        if (response.data.results.length > 0) {
          const result = response.data.results[0];
          const parsedAddress = parseAddressComponents(
            result.address_components || [],
            result.formatted_address || '',
          );

          const locationData = {
            address_array: parsedAddress,
            formatted_address: result.formatted_address,
            location: {latitude, longitude},
          };

          setCustomerRequestAdrress(locationData);
          setValue('locationInput', {
            formatted_address: result.formatted_address,
            geometry: {location: {lat: latitude, lng: longitude}},
            address_components: result.address_components || [],
          });
          _setSelectedLocation({latitude, longitude});
        }
      } catch (error) {
        console.error('Error in processLocation:', error);
      }
    },
    [setValue],
  );

  const requestCurrentLocation = useCallback(async () => {
    try {
      const permission =
        Platform.OS === 'ios'
          ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
          : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
      let status = await check(permission);
      if (status !== RESULTS.GRANTED) {
        status = await request(permission);
      }
      if (status === RESULTS.GRANTED) {
        Geolocation.getCurrentPosition(
          async position => {
            const {latitude, longitude} = position.coords;
            if (user?.uid) {
              try {
                await VehicleService.updateCustomerLocation(user.uid, {
                  latitude,
                  longitude,
                });
              } catch (error) {
                console.error('Error updating location in DB:', error);
              }
            }
            await processLocation(latitude, longitude);
          },
          error => {
            console.error('Geolocation error:', error);
            showCustomAlert('Error', 'Unable to get current location');
          },
          {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
        );
      } else {
        showCustomAlert('Permission Denied', 'Location permission is required');
      }
    } catch (error) {
      console.error('Location permission error:', error);
    }
  }, [user?.uid, processLocation]);

  useEffect(() => {
    const loadCurrentLocation = async () => {
      try {
        await requestCurrentLocation();
      } catch (error) {
        console.error('Error loading current location:', error);
      }
    };
    loadCurrentLocation();
  }, [requestCurrentLocation]);

  // useEffect(() => {
  //   const today = new Date();
  //   setDatePickerValue(today);
  //   const month = (today.getMonth() + 1).toString().padStart(2, '0');
  //   const day = today.getDate().toString().padStart(2, '0');
  //   const year = today.getFullYear().toString();
  //   const formattedDate = `${month}/${day}/${year}`;
  //   setDate(formattedDate);
  //  // setValue('scheduledate', today);
  // }, [setValue]);

  const requestSavedLocation = () => {
    if (savedLocations.length === 0) {
      showCustomAlert(
        'No Saved Locations',
        "You don't have any saved locations yet.",
      );
    }
  };

  const parseAddressComponents = (
    addressComponents: any[],
    formattedAddress: string,
  ) => {
    const addressData = {
      address1: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      zipcode: '',
    };

    if (addressComponents && addressComponents.length > 0) {
      addressComponents.forEach((component: any) => {
        const types = component.types;

        if (types.includes('street_number') || types.includes('route')) {
          if (!addressData.address1) {
            addressData.address1 = component.long_name;
          } else {
            addressData.address1 += ' ' + component.long_name;
          }
        } else if (
          types.includes('sublocality') ||
          types.includes('neighborhood')
        ) {
          addressData.address2 = component.long_name;
        } else if (
          types.includes('locality') ||
          types.includes('administrative_area_level_2')
        ) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressData.state = component.long_name;
        } else if (types.includes('country')) {
          addressData.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressData.zipcode = component.long_name;
        }
      });
    }
    if (!addressData.address1 && formattedAddress) {
      const parts = formattedAddress.split(',');
      addressData.address1 = parts[0]?.trim() || '';
      if (parts.length > 1 && !addressData.city) {
        addressData.city = parts[1]?.trim() || '';
      }
      if (parts.length > 2 && !addressData.state) {
        addressData.state = parts[2]?.trim() || '';
      }
    }

    return addressData;
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      if (selectedDate) {
        setDatePickerValue(selectedDate);
        const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
        const day = selectedDate.getDate().toString().padStart(2, '0');
        const year = selectedDate.getFullYear().toString();
        const formattedDate = `${month}/${day}/${year}`;
        setDate(formattedDate);
        setValue('scheduledate', selectedDate);
        trigger('scheduledate');
      }
      setShowDatePicker(false);
      return;
    }

    if (selectedDate) {
      setTempDate(selectedDate);
      setDatePickerValue(selectedDate);
    }
  };

  const handleDateModalCancel = () => {
    if (prevDatePickerValue) {
      setDatePickerValue(prevDatePickerValue);
    }
    setPrevDatePickerValue(null);
    setTempDate(new Date());
    setShowDatePicker(false);
  };

  const handleDateModalSet = () => {
    const selectedDate = tempDate || datePickerValue;
    setDatePickerValue(selectedDate);
    const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
    const day = selectedDate.getDate().toString().padStart(2, '0');
    const year = selectedDate.getFullYear().toString();
    const formattedDate = `${month}/${day}/${year}`;
    setDate(formattedDate);
    setValue('scheduledate', selectedDate);
    trigger('scheduledate');
    setPrevDatePickerValue(null);
    setTempDate(new Date());
    setShowDatePicker(false);
  };

  const _handleImagePick = () => {
    const options = {
      mediaType: 'photo' as const,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel) {
      } else if (response.errorMessage) {
        console.error('ImagePicker Error: ', response.errorMessage);
      } else if (response.assets && response.assets[0]) {
        setSelectedImages([...selectedImages, response.assets[0]]);
      }
    });
  };

  return (
    <SafeAreaView
      style={[
        styles.safeArea,
        {paddingTop: insets.top},
        {paddingBottom: insets.bottom},
      ]}>
      <StatusBar
        barStyle={useModernStyles ? 'light-content' : 'default'}
        backgroundColor={useModernStyles ? modernColors.headerBg : '#1f2a3a'}
        translucent={false}
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <Image
              source={AppCommonIcons.MCX_ARROW_RIGHT}
              style={styles.backIcon}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {AppStrings.MCX_BOOK_APPOINTMENT_TITLE}
          </Text>
        </View>

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'position' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? insets.bottom : 0}
          style={styles.keyboardAvoidingView}>
          <ScrollView
            ref={scrollViewRef}
            bounces={false}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag">
          <View>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_MY_MECHANIC_TITLE}
            </Text>
            <View style={styles.mechanicCard}>
              <Image
                source={
                  mechanic?.imageUrl
                    ? {uri: mechanic.imageUrl}
                    : AppCommonIcons.MCX_USER_PROFILE_PIC
                }
                style={styles.mechanicImage}
              />
              <View style={styles.mechanicInfo}>
                <Text style={styles.mechanicName}>{getFullName()}</Text>
                <Text style={styles.mechanicLocation}>{mechanic.address2}</Text>
                <View style={styles.ratingRow}>
                  <StarRating
                    rating={mechanic?.['mechanic-rating'] || 0}
                    maxRating={5}
                    size={16}
                    useImages={false}
                    showEmptyStars={true}
                    color="#8f0a19"
                  />
                  <Text style={styles.availabilityText}>
                    Availability{' '}
                    <Text
                      style={[
                        styles.appointmentText,
                        mechanic?.availability
                          ? styles.availableText
                          : styles.notAvailableText,
                      ]}>
                      {mechanic?.availability ? 'Open' : 'Appointment'}
                    </Text>
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_MY_VEHICLE_TEXT}
            </Text>
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>
                {AppStrings.MCX_MY_VEHICLE_TEXT}
              </Text>
              <Controller
                control={control}
                name="yourvehicle"
                render={({field: {onChange, value}}) => (
                  <CommonDropdown
                    data={userVehicleList.map(item => ({
                      label: String(item.label),
                      value: String(item.value),
                    }))}
                    value={value}
                    onValueChange={onChange}
                    placeholder="Select my vehicle"
                    loading={isLoadingVehicles}
                  />
                )}
              />
              {errors.yourvehicle && (
                <Text style={styles.errorText}>
                  {String((errors.yourvehicle as any)?.message || '')}
                </Text>
              )}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_MY_LOCATION_TEXT}
            </Text>
            <Controller
              control={control}
              name="requestlocation"
              render={({field: {onChange, value}}) => (
                <View style={styles.radioGroup}>
                  <TouchableOpacity
                    style={styles.radioOption}
                    onPress={() => {
                      onChange('currentlocation');
                      handleLocationTypeChange('currentlocation');
                    }}>
                    <Text style={styles.optionText}>
                      {AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}
                    </Text>
                    <View
                      style={[
                        styles.radioCircle,
                        value === 'currentlocation' &&
                          styles.radioCircleSelected,
                      ]}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.radioOption}
                    onPress={() => {
                      onChange('savedlocation');
                      handleLocationTypeChange('savedlocation');
                    }}>
                    <Text style={styles.optionText}>
                      {AppStrings.MCX_SAVED_LOCATION_TEXT}
                    </Text>
                    <View
                      style={[
                        styles.radioCircle,
                        value === 'savedlocation' && styles.radioCircleSelected,
                      ]}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.radioOption}
                    onPress={() => {
                      onChange('searchlocation');
                      handleLocationTypeChange('searchlocation');
                    }}>
                    <Text style={styles.optionText}>
                      {AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}
                    </Text>
                    <View
                      style={[
                        styles.radioCircle,
                        value === 'searchlocation' &&
                          styles.radioCircleSelected,
                      ]}
                    />
                  </TouchableOpacity>
                </View>
              )}
            />

            {watch('requestlocation') === 'searchlocation' && (
              <View style={styles.locationInputContainer}>
                <GooglePlacesAutocomplete
                  {...GooglePlacesAutocompleteDefaultProps}
                  ref={googlePlacesRef}
                  placeholder="Search for a location"
                  minLength={2}
                  fetchDetails={true}
                  onPress={(data, details = null) => {
                    if (details) {
                      // Reset locationSaved flag when a new location is selected
                      setLocationSaved(false);

                      setValue('locationInput', details);
                      const parsedAddress = parseAddressComponents(
                        details.address_components || [],
                        details.formatted_address || '',
                      );
                      setCustomerRequestAdrress({
                        address_array: parsedAddress,
                        formatted_address: details.formatted_address,
                        location: {
                          latitude: details.geometry.location.lat,
                          longitude: details.geometry.location.lng,
                        },
                      });
                      _setSelectedLocation({
                        latitude: details.geometry.location.lat,
                        longitude: details.geometry.location.lng,
                      });
                    }
                  }}
                  query={{
                    key: Config.GOOGLE_PLACES_API_KEY,
                    language: 'en',
                    components: 'country:in',
                  }}
                  debounce={300}
                  styles={{
                    container: {flex: 0, zIndex: 1000},
                    textInput: styles.locationInput,
                    textInputContainer: styles.locationInputContainer,
                    listView: styles.locationListView,
                    row: styles.locationRow,
                    description: styles.locationDescription,
                    placeholder: styles.locationInputPlaceholder,
                  }}
                  enablePoweredByContainer={false}
                />
                {errors.locationInput && (
                  <Text style={styles.errorText}>
                    {String(
                      (errors.locationInput as any)?.message ||
                        'Location is required',
                    )}
                  </Text>
                )}
              </View>
            )}

            {(watch('requestlocation') === 'currentlocation' ||
              watch('requestlocation') === 'selectedlocation') && (
              <View style={styles.locationDisplay}>
                <Text style={styles.locationText}>
                  {customerRequestAdrress
                    ? String(customerRequestAdrress.formatted_address)
                    : 'Loading location...'}
                </Text>
              </View>
            )}

            {watch('requestlocation') === 'searchlocation' &&
              customerRequestAdrress && (
                <View style={styles.locationDisplay}>
                  <Text style={styles.locationText}>
                    {String(customerRequestAdrress.formatted_address)}
                  </Text>
                  {(() => {
                    const isAlreadySaved = savedLocations.some(savedLoc =>
                      savedLoc.address === customerRequestAdrress.formatted_address ||
                      (savedLoc.coordinates?.latitude === customerRequestAdrress.location?.latitude &&
                        savedLoc.coordinates?.longitude === customerRequestAdrress.location?.longitude)
                    );

                    return (
                      <TouchableOpacity
                        style={[
                          styles.saveLocationButton,
                          (locationSaved || isAlreadySaved) && styles.disabledButton,
                        ]}
                        disabled={locationSaved || isAlreadySaved}
                        onPress={async () => {
                          try {
                            if (user?.uid && customerRequestAdrress) {
                              const locationData = {
                                name: String(
                                  customerRequestAdrress.formatted_address.split(
                                    ',',
                                  )[0] || 'Searched Location',
                                ),
                                address: String(
                                  customerRequestAdrress.formatted_address,
                                ),
                                coordinates: customerRequestAdrress.location,
                              };

                              await VehicleService.saveLocation(
                                user.uid,
                                locationData,
                              );
                              await fetchSavedLocations();
                              setLocationSaved(true);

                              showCustomAlert('Success', 'Location saved!');
                            }
                          } catch (error) {
                            console.error('Error saving location:', error);
                            showCustomAlert(
                              'Error',
                              'Failed to save location. Please try again.',
                            );
                          }
                        }}>
                        <Text
                          style={[
                            styles.saveLocationText,
                            (locationSaved || isAlreadySaved) && styles.disabledText,
                          ]}>
                          {locationSaved
                            ? 'Location Saved'
                            : isAlreadySaved
                            ? 'Location Already Saved'
                            : AppStrings.MCX_SAVE_LOCATION_TEXT}
                        </Text>
                      </TouchableOpacity>
                    );
                  })()}
                </View>
              )}

            {watch('requestlocation') === 'savedlocation' && (
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>
                  {AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}
                </Text>
                <CommonDropdown
                  data={savedLocations.map(l => ({
                    label: String(l.name),
                    value: l.id,
                  }))}
                  value={selectedSavedLocation}
                  onValueChange={setSelectedSavedLocation}
                  placeholder="Select saved location"
                  loading={isLoadingLocations}
                />
              </View>
            )}
          </View>

          <View style={styles.section}>
            <Text style={styles.servicesTitle}>
              {AppStrings.MCX_SERVICES_TITLE}
            </Text>
            {serviceFields.map((field, index) => (
              <View key={field.id} style={styles.serviceItem}>
                <View>
                  <View style={styles.dropdownContainer}>
                    <Text style={styles.dropdownLabel}>
                      {AppStrings.MCX_SERVICE_TYPE_KEYWORD}
                    </Text>
                    <Controller
                      control={control}
                      name={`serviceNeed.${index}.vehicleservice`}
                      render={({field: {onChange, value}}) => {
                        // Get all currently selected service IDs except current field
                        const watchedServiceNeed = watch('serviceNeed');
                        const selectedServiceIds = watchedServiceNeed
                          .map((s: any, i: number) =>
                            i !== index ? s.vehicleservice : null,
                          )
                          .filter(Boolean);

                        // Filter out already selected services
                        const availableServices = completeServiceArray
                          .filter(
                            service => !selectedServiceIds.includes(service.id),
                          )
                          .map(service => ({
                            label: service.name,
                            value: service.id,
                          }));

                        return (
                          <CommonDropdown
                            data={availableServices}
                            value={value}
                            onValueChange={newValue => {
                              // Clear sub-services when service type changes
                              if (value !== newValue) {
                                setValue(
                                  `serviceNeed.${index}.subServices`,
                                  [],
                                );

                                // Clear any bid rates associated with old sub-services
                                const currentBidRates = {...bidRates};
                                const currentBidErrors = {...bidErrors};

                                // Remove all bid rates and errors for this service index
                                Object.keys(currentBidRates).forEach(key => {
                                  if (key.startsWith(`${index}-`)) {
                                    delete currentBidRates[key];
                                  }
                                });

                                Object.keys(currentBidErrors).forEach(key => {
                                  if (key.startsWith(`${index}-`)) {
                                    delete currentBidErrors[key];
                                  }
                                });

                                setBidRates(currentBidRates);
                                setBidErrors(currentBidErrors);

                                // Clear any validation timeouts
                                Object.keys(bidValidationTimeouts).forEach(
                                  key => {
                                    if (key.startsWith(`${index}-`)) {
                                      clearTimeout(bidValidationTimeouts[key]);
                                      delete bidValidationTimeouts[key];
                                    }
                                  },
                                );
                                setBidValidationTimeouts({
                                  ...bidValidationTimeouts,
                                });
                              }

                              // Update the service type
                              onChange(newValue);
                            }}
                            placeholder="Select service"
                            loading={isLoadingServices}
                          />
                        );
                      }}
                    />
                    {errors.serviceNeed &&
                      (errors.serviceNeed as any)[index]?.vehicleservice && (
                        <Text style={styles.errorText}>
                          {
                            (errors.serviceNeed as any)[index]?.vehicleservice
                              ?.message
                          }
                        </Text>
                      )}
                    {index > 0 && (
                      <TouchableOpacity
                        style={styles.removeServiceButton}
                        onPress={() => removeService(index)}>
                        <Text style={styles.removeServiceText}>
                          {AppStrings.MCX_REMOVE_SERVICE_TEXT}
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>

                  {watch(`serviceNeed.${index}.vehicleservice`) && (
                    <View style={styles.dropdownContainer}>
                      <Text style={styles.dropdownLabel}>
                        {AppStrings.MCX_SUB_SERVICE_LABEL}
                      </Text>
                      <View style={styles.subServiceList}>
                        {watch(`serviceNeed.${index}.subServices`)?.map(
                          (subService: any, subIndex: number) => (
                            <View
                              key={`${subService.id}-${subIndex}`}
                              style={styles.selectedSubService}>
                              <Text style={styles.selectedSubServiceText}>
                                {subService.name}
                              </Text>
                              <TouchableOpacity
                                style={styles.removeSubServiceButton}
                                onPress={() =>
                                  removeSubService(index, subService.id)
                                }>
                                <Text style={styles.removeSubServiceText}>
                                  -
                                </Text>
                              </TouchableOpacity>
                            </View>
                          ),
                        )}
                      </View>
                      {(() => {
                        const selectedService = watch(
                          `serviceNeed.${index}.vehicleservice`,
                        );
                        const currentSubServices =
                          watch(`serviceNeed.${index}.subServices`) || [];
                        // FIXED: Ensure unique sub-services to prevent duplicates and duplicate keys
                        const allSubServices = [
                          ...new Set(
                            (mechanic?.services?.[selectedService]?.[
                              'sub-services'
                            ] as string[]) || [],
                          ),
                        ];

                        if (!allSubServices || allSubServices.length === 0) {
                          return null;
                        }

                        return isLoadingSubServices ? (
                          <View style={styles.loaderContainer}>
                            <ActivityIndicator size="small" color="#8B0000" />
                            <Text style={styles.loaderText}>
                              Loading sub-services...
                            </Text>
                          </View>
                        ) : (
                          <View style={styles.subServiceCheckboxContainer}>
                            {allSubServices.map((subServiceId: string) => {
                              const isSelected = currentSubServices.some(
                                (selected: any) => selected.id === subServiceId,
                              );
                              const subServiceName =
                                subServiceIdNameMap[subServiceId] ||
                                subServiceId;

                              return (
                                <TouchableOpacity
                                  key={subServiceId}
                                  style={styles.subServiceCheckboxItem}
                                  onPress={() => {
                                    if (isSelected) {
                                      removeSubService(index, subServiceId);
                                    } else {
                                      const subServiceData =
                                        subServicePriceData[subServiceId];
                                      if (subServiceData) {
                                        const subService = {
                                          id: subServiceId,
                                          name: subServiceName,
                                        };
                                        addSubService(index, subService);

                                        // FIXED: Auto-populate flat price if applicable
                                        const priceType =
                                          subServiceData?.['price-type'] ||
                                          subServiceData?.price_type ||
                                          subServiceData?.priceType;
                                        if (priceType === 'flat') {
                                          // Use setTimeout to ensure sub-service is added first
                                          setTimeout(() => {
                                            const currentSubServices =
                                              watch(
                                                `serviceNeed.${index}.subServices`,
                                              ) || [];
                                            const subIndex =
                                              currentSubServices.findIndex(
                                                (s: any) =>
                                                  s.id === subServiceId,
                                              );
                                            if (subIndex !== -1) {
                                              const fixedPrice =
                                                subServiceData['min-price'] ||
                                                subServiceData.minPrice ||
                                                subServiceData.min_rate ||
                                                subServiceData.price ||
                                                subServiceData.rate ||
                                                0;
                                              const bidKey = `${index}-${subService.id}`;
                                              setBidRates(prev => ({
                                                ...prev,
                                                [bidKey]: fixedPrice.toString(),
                                              }));
                                              // Clear any error for this flat price
                                              setBidErrors(prev => {
                                                const newErrors = {...prev};
                                                delete newErrors[bidKey];
                                                return newErrors;
                                              });
                                            }
                                          }, 100);
                                        }
                                      }
                                    }
                                  }}>
                                  <View
                                    style={[
                                      styles.checkbox,
                                      isSelected && styles.checkboxSelected,
                                    ]}>
                                    {isSelected && (
                                      <Text style={styles.checkboxCheckmark}>
                                        ✓
                                      </Text>
                                    )}
                                  </View>
                                  <Text style={styles.subServiceCheckboxText}>
                                    {subServiceName}
                                  </Text>
                                </TouchableOpacity>
                              );
                            })}
                          </View>
                        );
                      })()}
                      {errors.serviceNeed &&
                        (errors.serviceNeed as any)[index]?.subServices && (
                          <Text style={styles.errorText}>
                            {
                              (errors.serviceNeed as any)[index]?.subServices
                                ?.message
                            }
                          </Text>
                        )}
                    </View>
                  )}
                </View>
              </View>
            ))}

            {(() => {
              const watchedServiceNeed = watch('serviceNeed');
              const selectedServiceIds = watchedServiceNeed
                .map((s: any) => s.vehicleservice)
                .filter(Boolean);

              const availableServicesCount = completeServiceArray.length;
              const hasAvailableServices =
                selectedServiceIds.length < availableServicesCount;

              return (
                hasAvailableServices && (
                  <TouchableOpacity
                    style={styles.addServiceButton}
                    onPress={() =>
                      appendService({vehicleservice: '', subServices: []})
                    }>
                    <Text style={styles.addServiceText}>
                      {AppStrings.MCX_ADD_ANOTHER_SERVICE_TEXT}
                    </Text>
                  </TouchableOpacity>
                )
              );
            })()}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_SCREENING_DATA_TITLE}
            </Text>
            {Object.keys(screeningQuestions)
              .sort((a, b) => {
                const numA = parseInt(a.replace('que-', ''), 10);
                const numB = parseInt(b.replace('que-', ''), 10);
                return numA - numB;
              })
              .map(key => {
                const questionData = screeningQuestions[key];
                if (!questionData || questionData.hide) {
                  return null;
                }
                if (key === 'que-2') {
                  const que1Answer =
                    screeningAnswers['que-1']?.toLowerCase() || '';
                  if (que1Answer !== 'yes') {
                    return null;
                  }
                }
                if (key === 'que-6') {
                  const rideShareAnswer =
                    screeningAnswers['que-5']?.toLowerCase() || '';
                  if (rideShareAnswer !== 'yes') {
                    return null;
                  }
                }
                if (questionData.conditional) {
                  const conditionKey = questionData.conditional.dependsOn;
                  const conditionValue = questionData.conditional.value;
                  if (screeningAnswers[conditionKey] !== conditionValue) {
                    return null;
                  }
                }

                const hasError = errors[key] !== undefined;

                return (
                  <View key={key} style={styles.dropdownContainer}>
                    <View style={styles.questionHeader}>
                      <Text style={styles.dropdownLabel}>
                        {questionData.question}
                      </Text>
                    </View>
                    {questionData.type === 'entry' ? (
                      <TextInput
                        style={[
                          styles.input,
                          hasError ? styles.errorInput : undefined,
                        ]}
                        placeholder={questionData.error}
                        value={screeningAnswers[key] || ''}
                        onChangeText={text => {
                          setScreeningAnswers(prev => ({...prev, [key]: text}));
                          setValue(key, text);
                          trigger(key);
                        }}
                        keyboardType="default"
                      />
                    ) : (
                      <CommonDropdown
                        data={questionData.options.map((option: string) => ({
                          label: option,
                          value: option.toLowerCase(),
                        }))}
                        value={screeningAnswers[key] || null}
                        onValueChange={value => {
                          setScreeningAnswers(prev => ({
                            ...prev,
                            [key]: value,
                          }));
                          setValue(key, value);
                          trigger(key);
                        }}
                        placeholder="Choose option"
                        style={hasError ? styles.errorDropdown : undefined}
                      />
                    )}
                    {hasError && (
                      <Text style={styles.errorText}>
                        {typeof (errors[key] as any)?.message === 'string'
                          ? (errors[key] as any)?.message
                          : 'Choose at least one'}
                      </Text>
                    )}
                  </View>
                );
              })}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_ADD_YOUR_OWN_PRICE_MODEL_TEXT}
            </Text>
            <View style={styles.priceModelTable}>
              <View style={styles.priceModelHeaderRow}>
                <Text
                  style={[
                    styles.priceModelHeaderCell,
                    styles.priceModelHeaderCellService,
                  ]}>
                  {AppStrings.MCX_PRICE_MODEL_SERVICE_NAME}
                </Text>
                <Text
                  style={[
                    styles.priceModelHeaderCell,
                    styles.priceModelHeaderCellRange,
                  ]}>
                  {AppStrings.MCX_PRICE_MODEL_PRICE_RANGE}
                </Text>
                <Text
                  style={[
                    styles.priceModelHeaderCell,
                    styles.priceModelHeaderCellBid,
                  ]}>
                  {AppStrings.MCX_PRICE_MODEL_BID_RATE}
                </Text>
              </View>
              {serviceFields
                .map((field, index) => {
                  const vehicleservice = watch(
                    `serviceNeed.${index}.vehicleservice`,
                  );
                  const subServices =
                    watch(`serviceNeed.${index}.subServices`) || [];
                  const serviceName =
                    serviceIdNameMap[vehicleservice] || vehicleservice || '';

                  return subServices.map(
                    (subService: any, subIndex: number) => {
                      const subServiceData =
                        subServicePriceData[subService.id] || {};
                      const minPrice =
                        subServiceData['min-price'] ||
                        subServiceData.minPrice ||
                        subServiceData.min_rate ||
                        null;
                      const maxPrice =
                        subServiceData['max-price'] ||
                        subServiceData.maxPrice ||
                        subServiceData.max_rate ||
                        null;
                      let priceRange = '';
                      if (minPrice !== null && maxPrice !== null) {
                        priceRange = `$${minPrice} - $${maxPrice}`;
                      } else if (minPrice !== null) {
                        priceRange = `$${minPrice}`;
                      } else {
                        priceRange =
                          subServiceData.rate || subServiceData.price || '';
                        if (priceRange !== '') {
                          priceRange = `$${priceRange}`;
                        }
                      }
                      const bidKey = `${index}-${subService.id}`;
                      const bidRate = bidRates[bidKey] || '';
                      const bid = parseFloat(bidRate);
                      const hasError =
                        bidRate &&
                        minPrice &&
                        maxPrice &&
                        (bid < parseFloat(minPrice) ||
                          bid > parseFloat(maxPrice));

                      return (
                        <View key={`${field.id}-${subService.id}`}>
                          <View style={styles.priceModelRow}>
                            <Text
                              style={[
                                styles.priceModelCell,
                                styles.priceModelCellService,
                              ]}>
                              {serviceName} - {subService.name}
                            </Text>
                            <Text
                              style={[
                                styles.priceModelCell,
                                styles.priceModelCellRange,
                              ]}>
                              {priceRange}
                            </Text>
                            <TextInput
                              style={[
                                styles.priceModelCell,
                                styles.priceModelInput,
                                // FIXED: Don't show error for flat price types
                                hasError &&
                                  subServiceData?.['price-type'] !== 'flat' &&
                                  subServiceData?.price_type !== 'flat' &&
                                  styles.errorInput,
                                bidRate.trim() === '' &&
                                  subServiceData?.['price-type'] !== 'flat' &&
                                  subServiceData?.price_type !== 'flat' &&
                                  styles.errorInput,
                                (subServiceData?.['price-type'] === 'flat' ||
                                  subServiceData?.price_type === 'flat' ||
                                  subServiceData?.priceType === 'flat') &&
                                  styles.disabledInput,
                              ]}
                              keyboardType="numeric"
                              value={bidRate}
                              onChangeText={text => {
                                const priceType =
                                  subServiceData?.['price-type'] ||
                                  subServiceData?.price_type ||
                                  subServiceData?.priceType;
                                if (priceType === 'flat') return; // Prevent editing for flat bids

                                const newBidRates = {
                                  ...bidRates,
                                  [bidKey]: text,
                                };
                                setBidRates(newBidRates);

                                // Clear error for this field when user starts typing
                                if (bidErrors[bidKey]) {
                                  const newErrors = {...bidErrors};
                                  delete newErrors[bidKey];
                                  setBidErrors(newErrors);
                                }

                                // Clear existing timeout for this key
                                if (bidValidationTimeouts[bidKey]) {
                                  clearTimeout(bidValidationTimeouts[bidKey]);
                                }

                                // Set new timeout to validate after user stops typing
                                const timeoutId = setTimeout(() => {
                                  const bid = parseFloat(text);
                                  const minPrice = parseFloat(
                                    subServiceData['min-price'] ||
                                      subServiceData.minPrice ||
                                      '0',
                                  );
                                  const maxPrice = parseFloat(
                                    subServiceData['max-price'] ||
                                      subServiceData.maxPrice ||
                                      '0',
                                  );

                                  if (
                                    text &&
                                    minPrice &&
                                    maxPrice &&
                                    (bid < minPrice || bid > maxPrice)
                                  ) {
                                    setBidErrors(prev => ({
                                      ...prev,
                                      [bidKey]: `Please enter an amount between $${minPrice} - $${maxPrice}`,
                                    }));
                                  }

                                  // Clean up timeout reference
                                  setBidValidationTimeouts(prev => {
                                    const newTimeouts = {...prev};
                                    delete newTimeouts[bidKey];
                                    return newTimeouts;
                                  });
                                }, 1000); // 1 second delay

                                setBidValidationTimeouts(prev => ({
                                  ...prev,
                                  [bidKey]: timeoutId,
                                }));
                              }}
                              placeholder="Enter bid rate"
                              placeholderTextColor="#999999"
                              editable={
                                (subServiceData?.['price-type'] ||
                                  subServiceData?.price_type ||
                                  subServiceData?.priceType) !== 'flat'
                              }
                            />
                          </View>
                          {hasError &&
                            bidErrors[bidKey] &&
                            subServiceData?.['price-type'] !== 'flat' &&
                            subServiceData?.price_type !== 'flat' &&
                            subServiceData?.priceType !== 'flat' && (
                              <Text style={styles.bidErrorText}>
                                {bidErrors[bidKey]}
                              </Text>
                            )}
                          {bidRate.trim() === '' &&
                            subServiceData?.['price-type'] !== 'flat' &&
                            subServiceData?.price_type !== 'flat' &&
                            subServiceData?.priceType !== 'flat' && (
                              <Text style={styles.bidErrorText}>
                                Bid amount is required
                              </Text>
                            )}
                        </View>
                      );
                    },
                  );
                })
                .flat()}
              <View style={styles.priceModelTotalRow}>
                <Text
                  style={[
                    styles.priceModelTotalText,
                    styles.priceModelTotalTextService,
                  ]}>
                  {AppStrings.MCX_PRICE_MODEL_TOTAL}
                </Text>
                <Text
                  style={[
                    styles.priceModelTotalText,
                    styles.priceModelTotalTextSpacer,
                  ]}
                />
                <Text
                  style={[
                    styles.priceModelTotalText,
                    styles.priceModelTotalTextValue,
                  ]}>
                  ${' '}
                  {Object.values(bidRates)
                    .reduce((acc, val) => acc + (parseFloat(val) || 0), 0)
                    .toFixed(2)}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_SELECT_TIME_TEXT}
            </Text>
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>
                {AppStrings.MCX_ALERTS_AND_CONFIRMATION_TEXT}
              </Text>
              <Controller
                control={control}
                name="alertconfirmation"
                render={({field: {onChange, value}}) => (
                  <CommonDropdown
                    data={alertConfirmationData}
                    value={value}
                    onValueChange={val => {
                      onChange(val);
                    }}
                    placeholder="Select alert and confirmation"
                    loading={isLoadingAlerts}
                  />
                )}
              />
              {errors.alertconfirmation && (
                <Text style={styles.errorText}>
                  {String((errors.alertconfirmation as any)?.message || '')}
                </Text>
              )}
            </View>
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>
                {AppStrings.MCX_DATE_TEXT}
              </Text>
              <TouchableOpacity
               style={[
        styles.dateInput,
        errors.scheduledate && styles.errorInput,
       ]}
               onPress={() => {
         setShowDatePicker(true);
        // Trigger validation when user opens date picker
        trigger('scheduledate');
       }}>
                <Text
                  style={[
                    styles.dateInputText,
                    !date && styles.dateInputPlaceholder,
                  ]}>
                  {date || 'MM/DD/YYYY'}
                </Text>
              </TouchableOpacity>
              {errors.scheduledate && (
                <Text style={styles.errorText}>
                  {String((errors.scheduledate as any)?.message || 'Choose date for service')}
                </Text>
              )}
            </View>
            {/* <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>Time Range</Text>
          <TouchableOpacity
            style={styles.dateInput}
            onPress={() => setShowTimePicker(true)}>
            <Text
              style={[
                styles.dateInputText,
                !timeRange && styles.dateInputPlaceholder,
              ]}>
              {timeRange || 'Select time'}
            </Text>
          </TouchableOpacity>
          {errors.timerange && (
            <Text style={styles.errorText}>
              {String((errors.timerange as any)?.message || '')}
            </Text>
          )}
        </View> */}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_NOTES_AND_IMAGES}
            </Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder={AppStrings.MCX_NOTES_PLACEHOLDER_TEXT}
              value={notes}
              onChangeText={setNotes}
              multiline
              onFocus={() => {
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({animated: true});
                }, 100);
              }}
            />
            <View>
              <TouchableOpacity
                style={styles.imageButton}
                onPress={_handleImagePick}>
                <Image
                  source={AppCommonIcons.MCX_IMAGE_UPLOAD_ICON}
                  style={styles.imageIcon}
                />
              </TouchableOpacity>

              <View style={styles.imageGrid}>
                {selectedImages.map((img, idx) => (
                  <View key={`image-${idx}`} style={styles.imageContainer}>
                    <Image
                      source={{uri: img.uri}}
                      style={styles.thumbnailImage}
                      resizeMode="cover"
                    />
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteImage(idx)}>
                      <Image
                        source={require('../../../assets/common_icons/delete.png')}
                        style={styles.deleteIcon}
                      />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          </View>
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Fixed bottom buttons */}
        <View style={styles.bottomButtonsContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>
              {AppStrings.MCX_CANCEL_BUTTON_TEXT}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.confirmButton, isLoading && {opacity: 0.6}]}
            onPress={handleSubmitWithValidation}
            disabled={isLoading}>
            <Text style={styles.confirmButtonText}>
              {isLoading
                ? 'Processing...'
                : AppStrings.MCX_CONFIRM_APPOINTMENT_TEXT}
            </Text>
          </TouchableOpacity>
        </View>

        {Platform.OS === 'ios' ? (
          <Modal
            isVisible={showDatePicker}
            onBackdropPress={handleDateModalCancel}
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{justifyContent: 'center', alignItems: 'center'}}>
            <View style={styles.dateModalContainer}>
              <View style={styles.dateModalContent}>
                <DateTimePicker
                  value={datePickerValue}
                  mode="date"
                  display="spinner"
                  onChange={handleDateChange}
                  style={{width: '100%'}}
                  minimumDate={new Date()}
                />
                <View style={styles.dateModalButtons}>
                  <TouchableOpacity
                    style={[styles.dateModalButton, styles.dateModalCancelButton]}
                    onPress={handleDateModalCancel}>
                    <Text style={styles.dateModalButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.dateModalButton, styles.dateModalSetButton]}
                    onPress={handleDateModalSet}>
                    <Text style={[styles.dateModalButtonText, {color: '#fff', fontWeight: '700'}]}>Set</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        ) : (
          showDatePicker && (
            <DateTimePicker
              value={datePickerValue}
              mode="date"
              display="calendar"
              onChange={handleDateChange}
              minimumDate={new Date()}
              onTouchCancel={() => setShowDatePicker(false)}
            />
          )
        )}
        {/* {Platform.OS === 'ios'
          ? showTimePicker && (
              <DateTimePicker
                value={timePickerValue}
                mode="time"
                display="spinner"
                onChange={handleTimeChange}
                style={{width: '100%', backgroundColor: 'white'}}
              />
            )
          : showTimePicker && (
              <DateTimePicker
                value={timePickerValue}
                mode="time"
                display="clock"
                onChange={handleTimeChange}
                onTouchCancel={() => setShowTimePicker(false)}
              />
            )}
        */}
        <LoaderOverlay visible={isLoading} />
        <CustomAlert
          visible={alertVisible}
          title={alertTitle}
          message={alertMessage}
          onDismiss={hideAlert}
          buttons={alertButtons}
        />
      </View>
    </SafeAreaView>
  );
};

export default BookAppointmentsScreen;
