import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  Modal,
  FlatList,
  BackHandler,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import {launchImageLibrary} from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import ProfileCard from '../../../components/common/ProfileCard';
import ServicesMessage from '../../../components/common/ServiceMessage';
import type {StackNavigationProp} from '@react-navigation/stack';
import type {RouteProp} from '@react-navigation/native';
import type {RootStackParamList} from '../../../utils/configs/types';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import {VehicleService} from '../../../utils/services/VehicleService';
import PaymentService from '../../../utils/services/PaymentService';
import {set, get} from '@react-native-firebase/database';
import {AppStrings, RouteNames} from '../../../utils/constants/AppStrings';
import AppBackground from '../../../components/ui/AppBackground';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {CommonActions} from '@react-navigation/native';
import CustomAlert from '../../../components/common/CustomAlert';
type AppointmentDetailsProps = {
  navigation: StackNavigationProp<RootStackParamList, 'AppointmentDetails'>;
  route: RouteProp<RootStackParamList, 'AppointmentDetails'>;
};

type SmartNavigationOverrides = {
  messageParams?: Record<string, any>;
  appointmentParams?: Record<string, any>;
};

type SmartNavigationTarget = {
  screen: string;
  params?: any;
};

// Helper function for smart navigation
const getSmartNavigationTarget = (
  navigation: any,
  route: any,
  appointment: any,
  initialTab: number,
  overrides: SmartNavigationOverrides = {},
): SmartNavigationTarget => {
  // Check if user came from MessageDetails
  const fromMessageDetails = route.params?.fromMessageDetails;
  const navigationState = navigation.getState?.();
  const previousRoute =
    navigationState?.routes?.[navigationState?.index - 1];

  if (
    fromMessageDetails ||
    previousRoute?.name === RouteNames.MCX_NAV_MESSAGE_DETAILS
  ) {
    return {
      screen: RouteNames.MCX_NAV_MESSAGES,
      params: {
        initialTab,
        fromAppointmentDetails: true,
        refresh: true,
        ...overrides.messageParams,
      },
    };
  }

  // Default to Appointments screen
  return {
    screen: 'MainTabs',
    params: {
      screen: RouteNames.MCX_APPOINTMENTS,
      params: {
        refresh: true,
        ...overrides.appointmentParams,
      },
    },
  };
};

const AppointmentDetails = ({navigation, route}: AppointmentDetailsProps) => {
  const [notes, setNotes] = useState('');
  const [selectedImages, setSelectedImages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [isProcessingComplete, setIsProcessingComplete] = useState(false);
  const [mechanicDetail, setMechanicDetail] = useState<any>(null);
  const [customerCard, setCustomerCard] = useState<any>({});
  const [appointmentInfo, setAppointmentInfo] = useState<any>(null);
  const [mycanxTax, setMycanxTax] = useState(0);
  const [preparedServiceList, setPreparedServiceList] = useState<any>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [cancelationFee, setCancelationFee] = useState<any>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const appointment =
    route.params?.['appointment-detail'] || route.params?.appointment;
  const initialTab = route.params?.initialTab ?? 0;
  const insets = useSafeAreaInsets();

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const loadMechanicDetails = async (mechanicId: string) => {
    try {
      const mechanicSnap = await get(
        VehicleService.getMechanicDetail(mechanicId),
      );
      const mech = mechanicSnap.val();
      setMechanicDetail(mech);
    } catch (error) {
      console.error('Error loading mechanic details:', error);
    }
  };

  const loadCardDetails = async () => {
    try {
      const cards = await PaymentService.fetchCustomerCardDetails();
      setCustomerCard(cards || {});
    } catch (error) {
      console.error('Error loading card details:', error);
    }
  };

  const fetchAppointmentId = async (wrkReqId: string) => {
    try {
      const wrkReqSnap = await get(VehicleService.fetchWorkRequest(wrkReqId));
      const wrkReqDetail = wrkReqSnap.val()[wrkReqId];
      if (wrkReqDetail) {
        // Fetch service tax
        const state = wrkReqDetail[
          'request-address'
        ]?.address_array?.state?.replace(' ', '-');
        if (state) {
          const taxSnap = await get(
            VehicleService.fetchServiceTaxBasedOnState(state),
          );
          const taxValue = taxSnap.val();
          setMycanxTax(taxValue || 0);
        }

        if (wrkReqDetail.status !== 'appointment') {
          // Smart navigation based on where user came from
          const navigationTarget = getSmartNavigationTarget(navigation, route, appointment, initialTab);
          (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, navigationTarget);
          return;
        }

        // Prepare services
        await prepareServices(wrkReqDetail);

        // Set appointment ID
        appointment['appointment-id'] = wrkReqDetail.appointmentId;

        // Fetch appointment details
        const appSnap = await get(
          VehicleService.fetchAppointmentDetails(appointment['appointment-id']),
        );
        const appInfo = appSnap.val()[appointment['appointment-id']];
        setAppointmentInfo(appInfo);
      }
    } catch (error) {
      console.error('Error fetching appointment:', error);
    }
  };

  const prepareServices = async (workRequestDetail: any) => {
    const tempSer = workRequestDetail.services;
    const serId = Object.keys(tempSer);
    let exitServiceName = [];

    for (const sv of serId) {
      let tempSubser = workRequestDetail.services[sv]['sub-services'];
      let exitSubServiceName = [];

      for (const ssv of tempSubser) {
        const subServiceSnap = await get(
          VehicleService.fetchSubServiceName(ssv),
        );
        const subServiceName = subServiceSnap.val();
        exitSubServiceName.push({
          name: subServiceName[ssv]?.name || '',
          mechanic_bid:
            workRequestDetail.services[sv]['mechanic-bid'][
              tempSubser.indexOf(ssv)
            ] || '',
        });
      }

      const serviceSnap = await get(VehicleService.fetchServiceName(sv));
      const serviceName = serviceSnap.val();
      exitServiceName.push({
        service: serviceName[sv]?.name || '',
        subservice: exitSubServiceName,
      });
    }

    setPreparedServiceList({
      services: exitServiceName,
    });
  };

  const calculateCancelationCharge = () => {
    if (!preparedServiceList)
      {return {cancelFee: '0.00', cancelTax: '0.00', serviceFee: '0.00'};}

    let totServ = 0;
    for (const sv of preparedServiceList.services) {
      for (const ssv of sv.subservice) {
        totServ += parseFloat(ssv.mechanic_bid || '0');
      }
    }

    const totalFee = totServ.toFixed(2);
    const cancelFee = (parseFloat(totalFee) / 2).toFixed(2);
    const calculateTax = ((mycanxTax * parseFloat(cancelFee)) / 100).toFixed(2);

    return {
      cancelTax: calculateTax,
      serviceFee: totalFee,
      cancelFee: cancelFee,
    };
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log('Network check failed: ', state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      try {
        console.log('Appointment param:', JSON.stringify(appointment));
      } catch (err) {
        console.log('Appointment param (non-serializable):', appointment);
      }

      if (appointment && appointment.details?.mechanic) {
        const mechanic = appointment.details.mechanic;
        if (typeof mechanic === 'object') {
          setMechanicDetail(mechanic);
        } else {
          loadMechanicDetails(mechanic);
        }
      }

      if (appointment?.['work-request-id']) {
        await fetchAppointmentId(appointment['work-request-id']);
      }

      await loadCardDetails();
    };

    checkNetworkAndLoad();
  }, [appointment, navigation]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        // Smart navigation based on where user came from
        const navigationTarget = getSmartNavigationTarget(
          navigation,
          route,
          appointment,
          initialTab,
        );
        (navigation as any).navigate(
          RouteNames.MCX_NAV_DashBoard,
          navigationTarget,
        );
        return true;
      },
    );

    return () => backHandler.remove();
  }, [navigation, route, initialTab, appointment]);

  if (!appointment || typeof appointment !== 'object' || appointment === null) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Invalid appointment data: {String(appointment)}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const timeArrayBetweenDates = (schDate: number | undefined) => {
    if (!schDate) {
      return {days: 0, hours: 0, minutes: 0, seconds: 0};
    }
    const fromDate = new Date(schDate * 1000);
    const now = new Date();
    const difference = fromDate.getTime() - now.getTime();
    if (difference <= 0) {
      return {days: 0, hours: 0, minutes: 0, seconds: 0};
    }
    const seconds = Math.floor(difference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    return {
      days,
      hours: hours % 24,
      minutes: minutes % 60,
      seconds: seconds % 60,
    };
  };

  const doYouWantTocancelAppointment = () => {
    showCustomAlert(
      'Cancel Appointment',
      'Are you sure you want to cancel Appointment? Note: There is no Undo.',
      [
        {
          text: 'No',
          onPress: hideAlert,
        },
        {
          text: 'Yes',
          onPress: () => {
            cancelAppointment();
          },
        },
      ],
    );
  };

  const doYouWantTocancelAppointmentwithCancelationFee = () => {
    showCustomAlert(
      'Cancel Appointment',
      'Are you sure you want to cancel Appointment? Note: You may charge cancellation fee as 50% of service fee.',
      [
        {
          text: 'No',
          onPress: hideAlert,
        },
        {
          text: 'PAY FEE',
          onPress: () => {
            payCancelationCharge();
          },
        },
      ],
    );
  };

  const checkChargeForCancelAppointment = () => {
    const differenceTime = timeArrayBetweenDates(
      appointmentInfo?.['schedule-time'],
    );
    console.log('Time between array:', differenceTime);

    if (differenceTime.days > 0) {
      console.log(
        'Customer can cancel without any cancellation fee due to more than 24 hours for service',
      );
      doYouWantTocancelAppointment();
    } else if (
      differenceTime.days === 0 &&
      differenceTime.hours === 0 &&
      differenceTime.minutes === 0 &&
      differenceTime.seconds === 0
    ) {
      console.log(
        'Customer can cancel without any cancellation fee due to mechanic delayed',
      );
      doYouWantTocancelAppointment();
    } else {
      console.log(
        'Customer can cancel with 50% of service fee charged as cancellation fee',
      );
      doYouWantTocancelAppointmentwithCancelationFee();
    }
  };

  const payCancelationCharge = () => {
    const fee = calculateCancelationCharge();
    setCancelationFee(fee);
    setShowPaymentModal(true);
  };

  const handlePaymentSelection = async (selectedCard: any) => {
    setShowPaymentModal(false);
    await doPayment(selectedCard, cancelationFee);
  };

  const doPayment = async (event: any, cancelationFee: any) => {
    setIsLoading(true);

    try {
      if (!mechanicDetail?.['stripe-acc-key']) {
        setIsLoading(false);
        showCustomAlert(
          'Payment Failed',
          'Your payment process failed on the payment gateway due to mechanic account information missing, Please contact your mechanic.',
        );
        return;
      }

      const paymentInfo = {
        mechanicStripeKey: mechanicDetail['stripe-acc-key'],
        tipToMechanic: 0,
        serviceFee: cancelationFee.cancelFee,
        serviceTax: cancelationFee.cancelTax,
        'work-request-id': appointment['work-request-id'],
      };

      const paymentResult = await PaymentService.processPayment(
        event.token,
        paymentInfo,
        (parseFloat(cancelationFee.cancelFee) +
          parseFloat(cancelationFee.cancelTax)) *
          100,
      );

      if ((paymentResult as any).charge?.status === 'succeeded') {
        await successCheckout((paymentResult as any).charge, cancelationFee);
      } else {
        showCustomAlert(
          'Payment Failed',
          'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
        );
      }
    } catch (error) {
      console.error('Payment error:', error);
      showCustomAlert(
        'Payment Failed',
        'Your payment process failed on the payment gateway due to some technical issues, Please try again later.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const successCheckout = async (chargDetail: any, cancelationFee: any) => {
    try {
      // Update invoice
      const params = {
        'work-request-id': appointment['work-request-id'],
        costDetail: cancelationFee,
        confirmationId: chargDetail.id,
        paymentType: chargDetail.source.brand + ' ' + chargDetail.source.object,
      };

      await PaymentService.updateInvoice(params);
      await PaymentService.updateWorkRequestPaymentStatus(params);

      // Update log
      const logRef = VehicleService.updateLog(appointment['work-request-id']);
      await set(logRef.child('checkout').push(), {
        'start-time': Math.floor(Date.now() / 1000),
        status: chargDetail.status,
        'confirmation-id': chargDetail.id,
        'transaction-id': chargDetail.balance_transaction,
        comment: 'Cancelled appointment payment',
      });

      // Now cancel the appointment
      await cancelAppointment();
    } catch (error) {
      console.error('Error in success checkout:', error);
      showCustomAlert(
        'Error',
        'Failed to complete payment process. Please contact support.',
      );
    }
  };

  const cancelAppointment = async () => {
    setIsCanceling(true);

    try {
      const mechanicId =
        typeof appointment.details?.mechanic === 'string'
          ? appointment.details.mechanic
          : appointment['mechanic-id'];

      if (appointment.details?.['multiple-request']) {
        await AppointmentService.refreshMultipleRequest(
          appointment.details['multiple-request'],
          mechanicId,
        );
      }

      await AppointmentService.cancelAppointment(
        appointment['appointment-id'] || appointment['work-request-id'],
        mechanicId,
        appointment['work-request-id'],
        notes,
      );

      await AppointmentService.updateCancelCount();

      if (selectedImages && selectedImages.length > 0) {
        await uploadingImage(appointment['appointment-id']);
      }

      const logRef = AppointmentService.updateLog(
        appointment['work-request-id'],
      );
      await set(
        logRef.child('schedule').child('cancelled-time'),
        Math.floor(Date.now() / 1000),
      );
      await set(logRef.child('schedule').child('cancelled-by'), 'customer');

      // Set blocking overlay state before showing alert
      setIsProcessingComplete(true);
      setIsCanceling(false);

      showCustomAlert('Success', 'Request Cancelled successfully', [
        {
          text: 'OK',
          onPress: () => {
            // Smart navigation based on where user came from
            const navigationTarget = getSmartNavigationTarget(
              navigation,
              route,
              appointment,
              initialTab,
              {
                messageParams: {
                  cancelledAppointment: appointment?.['work-request-id'],
                },
              },
            );
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: RouteNames.MCX_NAV_DashBoard,
                    params: navigationTarget,
                  },
                ],
              }),
            );
          },
        },
      ]);
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      showCustomAlert('Error', 'Failed to cancel appointment. Please try again.');
      setIsCanceling(false);
    }
  };

  const uploadingImage = async (appointmentId: string) => {
    if (!selectedImages || selectedImages.length === 0) {
      return;
    }

    try {
      const uploadPromises = selectedImages.map(
        async (imageAsset: any, index: number) => {
          const imageUri = imageAsset.uri;
          const snapshot = await VehicleService.uploadImage(
            imageUri,
            appointmentId,
            index,
            'appointmentImages',
          );
          const profileURL = snapshot.metadata.fullPath;
          const downloadURL = await VehicleService.getDownloadURL(profileURL);
          return downloadURL;
        },
      );
      const uploadedUrls = await Promise.all(uploadPromises);
      // Update the appointment with image URLs
      await VehicleService.updateCancelAppointmentImagePath(
        uploadedUrls,
        appointmentId,
      );
    } catch (error) {
      console.error('Error uploading images:', error);
    }
  };

  const handleImageSelection = () => {
    const options = {
      mediaType: 'photo' as const,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      selectionLimit: 0,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel || response.errorCode) {
        return;
      }

      if (response.assets && response.assets.length > 0) {
        setSelectedImages(response.assets);
      }
    });
  };

  const trackMechanic = () => {
    console.log('Track mechanic pressed');
  };

  const renderCardItem = ({item}: {item: any}) => {
    const cardKey = Object.keys(item)[0];
    const cardData = item[cardKey];

    const card = cardData.cards || cardData.card || cardData;
    const brand = card.brand || 'Unknown';
    const last4 = card.last4 || card.last4;
    const expMonth = card.exp_month || card.expMonth;
    const expYear = card.exp_year || card.expYear;

    console.log('Extracted card data:', {brand, last4, expMonth, expYear});

    return (
      <TouchableOpacity
        style={styles.cardItem}
        onPress={() => handlePaymentSelection({token: cardKey, ...cardData})}>
        <View style={styles.cardInfo}>
          <Text style={styles.cardBrand}>
            {brand?.toUpperCase() || 'CARD'} ****{last4 || '----'}
          </Text>
          <Text style={styles.cardExpiry}>
            {expMonth || '--'}/{expYear || '----'}
          </Text>
        </View>
        <Icon name="chevron-forward" size={20} color="#666" />
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, {paddingTop: insets.top}]}>
      <StatusBar barStyle="light-content" backgroundColor="#1a2332" />
      <AppBackground />
      <View style={styles.header}>
        <View style={styles.navbar}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              const navigationTarget = getSmartNavigationTarget(
                navigation,
                route,
                appointment,
                initialTab,
              );
              (navigation as any).navigate(
                RouteNames.MCX_NAV_DashBoard,
                navigationTarget,
              );
            }}
            disabled={isCanceling}>
            <Icon
              name="arrow-back"
              size={24}
              color={isCanceling ? '#ccc' : '#fff'}
            />
          </TouchableOpacity>
          <Image
            source={require('../../../assets/logo/logo_b_client.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <View style={styles.backButton} />
        </View>
        <View style={styles.titleBar}>
          <Text style={styles.titleBarText}>APPOINTMENT DETAIL</Text>
        </View>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.content}
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          scrollEnabled={!isCanceling}>
        <View style={styles.listContainer}>
          <Text style={styles.mainTitle}>
            {AppStrings.MCX_MY_MECHANIC_TITLE}
          </Text>

          <View style={styles.profileDetail}>
            <View style={styles.profileDetailPadding}>
              {mechanicDetail ? (
                <ProfileCard
                  imageUrl={mechanicDetail.imageUrl || 'path_to_image'}
                  name={
                    [mechanicDetail['first-name'], mechanicDetail['last-name']]
                      .filter(n => n && typeof n === 'string')
                      .join(' ') || 'Mech'
                  }
                  address={
                    mechanicDetail.address2 ||
                    mechanicDetail.address1 ||
                    'Address not available'
                  }
                  availabilityStatus={
                    mechanicDetail.availability ? 'Open' : 'Appointment'
                  }
                  rate={mechanicDetail['mechanic-rating'] || 0}
                />
              ) : (
                <Text style={styles.mechanicLoadingText}>
                  Loading mechanic details...
                </Text>
              )}

              <View style={styles.appointmentDetails}>
                <View style={styles.messagesBoxArea}>
                  {appointmentInfo ? (
                    <ServicesMessage
                      isIcon={true}
                      day={appointment.details?.day || '1'}
                      month={appointment.details?.month || 'Jan'}
                      services={appointment.details?.services || []}
                      time={appointment.details?.time || 'Time not available'}
                    />
                  ) : (
                    <View style={styles.servicesLoaderContainer}>
                      <ActivityIndicator size="small" color="#007AFF" />
                      <Text style={styles.servicesLoaderText}>Loading appointment details...</Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>

          <View style={styles.notesImagesContainer}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>NOTES AND IMAGES</Text>
              <TextInput
                style={[
                  styles.textArea,
                  isCanceling && styles.disabledTextArea,
                ]}
                multiline={true}
                numberOfLines={4}
                value={notes}
                onChangeText={setNotes}
                placeholder="Please add Notes"
                placeholderTextColor="#252020ff"
                editable={!isCanceling}
                onFocus={() => {
                  setTimeout(() => {
                    scrollViewRef.current?.scrollToEnd({animated: true});
                  }, 100);
                }}
              />
            </View>

            <View style={styles.imageContainer}>
              <TouchableOpacity
                style={[
                  styles.imageButton,
                  isCanceling && styles.disabledButton,
                ]}
                onPress={handleImageSelection}
                disabled={isCanceling}>
                <Icon
                  name="image-outline"
                  size={24}
                  color={isCanceling ? '#ccc' : '#007AFF'}
                />
                {selectedImages && selectedImages.length > 0 ? (
                  <Text
                    style={[
                      styles.imageSelectionText,
                      isCanceling && styles.disabledText,
                    ]}>
                    {selectedImages.length} image(s) selected
                  </Text>
                ) : null}
              </TouchableOpacity>
            </View>
          </View>
        </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <View style={[styles.footer, {paddingBottom: insets.bottom + 10}]}>
        <TouchableOpacity
          style={[
            styles.cancelButton,
            (isLoading || isCanceling) && styles.disabledButton,
          ]}
          onPress={checkChargeForCancelAppointment}
          disabled={isLoading || isCanceling}>
          {isLoading || isCanceling ? (
            <Text style={styles.cancelButtonText}>
              {isCanceling ? 'Cancelling Appointment...' : 'Processing...'}
            </Text>
          ) : (
            <Text style={styles.cancelButtonText}>Cancel Appointment</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Full Screen Loading Overlay */}
      {(isLoading || isCanceling) && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>
              {isCanceling ? 'Cancelling Appointment...' : 'Processing...'}
            </Text>
            <Text style={styles.loadingSubtext}>
              {isCanceling
                ? 'Please wait while we process your request'
                : 'Please wait'}
            </Text>
          </View>
        </View>
      )}

      {/* Payment Modal */}
      <Modal
        visible={showPaymentModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPaymentModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Choose Card</Text>
              <TouchableOpacity onPress={() => setShowPaymentModal(false)}>
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={Object.entries(customerCard).map(([key, value]) => ({
                [key]: value,
              }))}
              renderItem={renderCardItem}
              keyExtractor={(item, index) => index.toString()}
              style={styles.cardList}
            />

            <TouchableOpacity
              style={styles.payButton}
              onPress={() => {
                // Handle adding new card or other actions
                setShowPaymentModal(false);
              }}>
              <Text style={styles.payButtonText}>
                PAY $
                {(
                  parseFloat(cancelationFee?.cancelFee || '0') +
                  parseFloat(cancelationFee?.cancelTax || '0')
                ).toFixed(2)}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Simple interaction blocking overlay (no loader) */}
      {isProcessingComplete && <View style={styles.interactionBlocker} />}

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  scrollContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 10 : 140, // 👈 ensures scroll area includes footer height
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
    elevation: 10,
    // 👇 keep transparent footer
    backgroundColor: 'rgba(12, 16, 24, 0.95)',
  },
  cancelButton: {
    backgroundColor: '#383a3dff',
    paddingVertical: 16,
    borderRadius: 6,
    width: '100%',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 18,
    color: '#a00',
    textAlign: 'center',
  },
  header: {
    backgroundColor: '#4a5c6a',
  },
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1f2a38',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    height: 30,
    width: 120,
  },
  titleBar: {
    backgroundColor: '#a00',
    paddingVertical: 12,
    alignItems: 'center',
  },
  titleBarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  scrollContent: {
    paddingBottom: 200, // Add extra padding to ensure content scrolls behind footer
  },
  listContainer: {
    backgroundColor: 'transparent',
  },
  mainTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  profileDetail: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
  },
  profileDetailPadding: {
    padding: 16,
  },
  appointmentDetails: {
    marginTop: 16,
  },
  messagesBoxArea: {
    backgroundColor: 'transparent',
  },
  notesImagesContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    fontStyle: 'italic',
    textAlignVertical: 'top',
    minHeight: 80,
    color: '#333',
  },
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  imageSelectionText: {
    marginLeft: 8,
    color: '#007AFF',
    fontSize: 16,
  },
  mechanicLoadingText: {
    textAlign: 'center',
    padding: 16,
    color: '#666',
  },
  servicesLoaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  servicesLoaderText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledText: {
    color: '#ccc',
  },
  disabledTextArea: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ddd',
    color: '#999',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(116, 113, 113, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    minWidth: 250,
    maxWidth: 300,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    textAlign: 'center',
  },
  loadingSubtext: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  cardList: {
    maxHeight: 300,
  },
  cardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  cardInfo: {
    flex: 1,
  },
  cardBrand: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  cardExpiry: {
    fontSize: 14,
    color: '#666',
  },
  payButton: {
    backgroundColor: '#a00',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  payButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Simple interaction blocking overlay (no loader)
  interactionBlocker: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // Semi-transparent white
    zIndex: 9999,
  },
});

export default AppointmentDetails;
