import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { AppStrings, RouteNames } from '../../utils/constants/AppStrings';
import { useNavigation } from '@react-navigation/native';
import { Colors, Fonts } from '../../utils/constants/Theme';
import GoogleAuthService from '../../auth/google/googleAuthService';
import { AnalyticService } from '../../utils/services/AnalyticService';
import {
  FacebookAuthProvider,
  getAuth,
  signInWithCredential,
  AppleAuthProvider,
} from '@react-native-firebase/auth';
import { LoginManager, AccessToken } from 'react-native-fbsdk-next';
import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
  GC_APPLE_FIRST_NAME,
  GC_APPLE_EMAIL,
} from '../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import appleAuth from '@invertase/react-native-apple-authentication';
import { useAuth } from '../../utils/configs/AuthContext';
import LoaderOverlay from '../../components/common/LoaderOverlay';
import CustomAlert from '../../components/common/CustomAlert';
import {
  handleSocialAuthError,
  isAppleSignInCancelled,
  isFacebookLoginCancelled,
  isGoogleSignInCancelled,
  showCancellationAlert,
} from '../../utils/helpers/authErrorHandler';

const LoginMainScreen = () => {
  const navigation = useNavigation();
  const { signIn } = GoogleAuthService();
  const { AddAnalyticsScreenView } = AnalyticService;
  const { fetchUserInfo, isRegisteredAsMechanicUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const isMountedRef = useRef(true);

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    AddAnalyticsScreenView('WelcomeLogin Page', '/#/welcome-login');
  }, [AddAnalyticsScreenView]);

  const safeSetLoading = (value: boolean) => {
    if (isMountedRef.current) {
      setIsLoading(value);
    }
  };

  const handleMechanicCheck = async (userId: string) => {
    const isMechanic = await isRegisteredAsMechanicUser(userId);
    if (isMechanic) {
      try {
        const auth = getAuth();
        if (auth.currentUser) {
          await auth.signOut();
        }
      } catch (e) {
        console.warn('Sign out warning:', e);
      }

      await AsyncStorage.removeItem(GC_CUSTOMER_ID);
      await AsyncStorage.removeItem(GC_SIGNUP_PROVIDER_TYPE_PREF);
      await AsyncStorage.removeItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF);

      safeSetLoading(false);

      showCustomAlert(
        'Unable to Login',
        'Your email is already registered in another myCANx application. Please sign up for another account, or simply use a different email for this app.',
        [
          {
            text: 'OK',
            onPress: () => {
              if (isMountedRef.current) {
                console.log('Mechanic alert dismissed');
              }
            },
          },
        ]
      );
      return true;
    }
    return false;
  };

  const handleRegistrationCheckAndNavigate = async (userId: string) => {
    const userInfo = await fetchUserInfo(userId);
    const isRegistrationCompleted =
      userInfo && userInfo['registration-status'] === 'COMPLETED';

    if (isRegistrationCompleted) {
      safeSetLoading(false);
      return true;
    }

    if (isMountedRef.current) {
      (navigation as any).reset({
        index: 0,
        routes: [{name: RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}],
      });
    }
    safeSetLoading(false);
    return false;
  };

  const onGoogleButtonPress = async () => {
    try {
      const loggedUser = await signIn();
      console.log("logged user:",loggedUser)

      if (isGoogleSignInCancelled(loggedUser)) {
        showCancellationAlert('Google', () => safeSetLoading(false));
        return;
      }

      safeSetLoading(true);

      if (!loggedUser || !loggedUser.uid) {
        throw new Error('Google sign-in did not return a valid user ID.');
      }

      const userId = loggedUser.uid;
      // ✅ FIX: Get Google's profile 'sub' instead of providerId
       const googleProfileId = loggedUser.providerData[0]?.uid || userId;
      await Promise.all([
        AsyncStorage.setItem(GC_CUSTOMER_ID, userId),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'google'),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, googleProfileId),
      ]);

      const isMechanic = await handleMechanicCheck(userId);
      if (isMechanic) return;

      await handleRegistrationCheckAndNavigate(userId);
    } catch (error: any) {
      console.error('Google Sign-In Error:', error);
      safeSetLoading(false);
      handleSocialAuthError(error, 'Google', () => safeSetLoading(false));
    }
  };

  const onFacebookButtonPress = async () => {
    try {
      const result = await LoginManager.logInWithPermissions([
        'public_profile',
        'email',
      ]);

      if (isFacebookLoginCancelled(result)) {
        showCancellationAlert('Facebook', () => safeSetLoading(false));
        return;
      }

      safeSetLoading(true);

      const data = await AccessToken.getCurrentAccessToken();
      if (!data) {
        throw new Error('Something went wrong obtaining access token');
      }

      const facebookCredential = FacebookAuthProvider.credential(data.accessToken);
      const userCredential = await signInWithCredential(getAuth(), facebookCredential);

      const userId = userCredential.user.uid;

      // ✅ FIX: Get Facebook's profile 'id' instead of Firebase UID
      const facebookProfileId = 
        userCredential.additionalUserInfo?.profile?.id || userId;

      await Promise.all([
        AsyncStorage.setItem(GC_CUSTOMER_ID, userId),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'facebook'),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, facebookProfileId),
      ]);

      const isMechanic = await handleMechanicCheck(userId);
      if (isMechanic) return;

      await handleRegistrationCheckAndNavigate(userId);
    } catch (error: any) {
      console.error('Facebook Sign-In Error:', error);
      safeSetLoading(false);
      handleSocialAuthError(error, 'Facebook', () => safeSetLoading(false));
    }
  };

 const onAppleButtonPress = async () => {
  try {
    const rawNonce = Math.random().toString(36).substring(2, 15);
    const state = Math.random().toString(36).substring(2, 15);

    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      nonce: rawNonce,
      state,
    });

    const { identityToken, nonce: returnedNonce, email, fullName } =
      appleAuthRequestResponse;

    if (isAppleSignInCancelled(identityToken)) {
      showCancellationAlert('Apple', () => safeSetLoading(false));
      return;
    }

    safeSetLoading(true);

    const appleCredential = AppleAuthProvider.credential(
      identityToken,
      returnedNonce || rawNonce
    );

    const userCredential = await signInWithCredential(getAuth(), appleCredential);

    // ✅ FIX: Get Apple's provider UID
    const appleProviderId = 
      userCredential.user.providerData[0]?.uid || userCredential.user.uid;

    // await AsyncStorage.setItem(GC_APPLE_JWT_TOKEN_PREF, identityToken);
    // await AsyncStorage.setItem(
    //   GC_APPLE_JWT_AUTHORIZATION_CODE, 
    //   appleAuthRequestResponse.authorizationCode || ''
    // );

    // ✅ FIX: Use 'apple.com' constant
    await AsyncStorage.setItem(GC_CUSTOMER_ID, userCredential.user.uid);
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'apple.com');  // Use constant
    await AsyncStorage.setItem(
      GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
      appleProviderId
    );

    if (fullName) {
      const givenName = fullName.givenName || '';
      const familyName = fullName.familyName || '';
      const displayName = `${givenName} ${familyName}`.trim();
      await userCredential.user.updateProfile({ displayName });
      // Store for registration screen
      await AsyncStorage.setItem(GC_APPLE_FIRST_NAME, `${givenName}|${familyName}`);
    }
    if (email) {
      await AsyncStorage.setItem(GC_APPLE_EMAIL, email);
    }

    const userId = userCredential.user.uid;

    const isMechanic = await handleMechanicCheck(userId);
    if (isMechanic) return;

    await handleRegistrationCheckAndNavigate(userId);
  } catch (error: any) {
    console.error('Apple Sign In Error', error);
    safeSetLoading(false);
    handleSocialAuthError(error, 'Apple', () => safeSetLoading(false));
  }
};

  const onSignupButtonPress = async () => {
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'normal');
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, '');
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never);
  };

  const onLoginButtonPress = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_AppLoginPageScreen as never);
  };

  const openPrivacy = () => {
    navigation.navigate(RouteNames.MCX_NAV_PRIVACY_POLICY as never);
  };

  const openTerms = () => {
    navigation.navigate(RouteNames.MCX_NAV_TERMS_CONDITIONS as never);
  };

  const isAppleSignInSupported = () => {
    try {
      const supported = (appleAuth as any)?.isSupported;
      return typeof supported === 'boolean' ? supported : Platform.OS === 'ios';
    } catch {
      return Platform.OS === 'ios';
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      <TouchableOpacity style={styles.signUpBtn} onPress={onSignupButtonPress}>
        <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.loginBtn} onPress={onLoginButtonPress}>
        <Text style={styles.loginText}>{AppStrings.MCX_LOGIN}</Text>
      </TouchableOpacity>

      <Text style={styles.orText}>{AppStrings.MCX_OR_WITH}</Text>

      <View style={styles.socialRow}>
        <TouchableOpacity
          style={[styles.socialButton, styles.facebookButton]}
          onPress={onFacebookButtonPress}>
          <Icon name="facebook" size={24} color={Colors.BUTTON_TEXT_COLOR} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.socialButton, styles.googleButton]}
          onPress={onGoogleButtonPress}>
          <Image
            source={require('../../assets/social_network_icons/google_icon.png')}
            style={styles.socialIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
        {Platform.OS === 'ios' && isAppleSignInSupported() && (
          <TouchableOpacity
            style={[styles.socialButton, styles.appleButton]}
            onPress={onAppleButtonPress}>
            <Icon name="apple" size={24} color={Colors.BUTTON_TEXT_COLOR} />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.dividerLine} />

      <View style={styles.termsSection}>
        <Text style={styles.termsText}>
          {AppStrings.MCX_TERMS_PREFIX}
          {'\n'}
          <Text style={styles.linkText} onPress={openPrivacy}>
            {AppStrings.MCX_PRIVACY_POLICY}
          </Text>
          {AppStrings.MCX_AND_KEYWORD}
          <Text style={styles.linkText} onPress={openTerms}>
            {AppStrings.MCX_TERMS_OF_SERVICE}
          </Text>
        </Text>
      </View>

      <LoaderOverlay visible={isLoading} />

      <CustomAlert
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onDismiss={hideAlert}
        buttons={alertButtons}
      />
    </View>
  );
};

export default LoginMainScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
  },
  logo: {
    height: 200,
    width: 200,
    marginBottom: 60,
  },
  signUpBtn: {
    backgroundColor: '#A10000',
    paddingVertical: 16,
    paddingHorizontal: 80,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    alignSelf: 'stretch',
    shadowColor: '#A10000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loginBtn: {
    backgroundColor: '#FFFFFF',
    borderColor: '#A10000',
    borderWidth: 2,
    paddingVertical: 14,
    paddingHorizontal: 80,
    borderRadius: 12,
    marginBottom: 40,
    alignItems: 'center',
    alignSelf: 'stretch',
  },
  socialRow: {
    flexDirection: 'row',
    marginBottom: 32,
    gap: 20,
  },
  socialButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  facebookButton: {
    backgroundColor: '#1877F2',
  },
  googleButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  appleButton: {
    backgroundColor: '#000000',
  },
  socialIcon: {
    width: 26,
    height: 26,
  },
  signUpText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_BOLD,
    letterSpacing: 0.5,
  },
  loginText: {
    color: '#A10000',
    fontSize: 17,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_BOLD,
    letterSpacing: 0.5,
  },
  orText: {
    color: '#6B7280',
    fontSize: 14,
    marginBottom: 24,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  dividerLine: {
    height: 1,
    backgroundColor: '#E5E7EB',
    width: '100%',
    marginBottom: 20,
  },
  termsSection: {
    paddingBottom: 40,
    paddingTop: 8,
  },
  termsText: {
    fontSize: 13,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  linkText: {
    color: '#00BCD4',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
});