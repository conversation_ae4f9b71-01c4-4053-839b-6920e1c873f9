import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import LoaderOverlay from '../../components/common/LoaderOverlay';
import CustomAlert from '../../components/common/CustomAlert';
import {Colors, Fonts} from '../../utils/constants/Theme';
import {AppStrings, RouteNames} from '../../utils/constants/AppStrings';
import {wp} from '../../utils/ResponsiveParams';
import {useNavigation} from '@react-navigation/native';
import {useAuth} from '../../utils/configs/AuthContext';

import {GC_CUSTOMER_ID, GC_SOCIAL_LOGGED_IN} from '../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AnalyticService} from '../../utils/services/AnalyticService';

const AppLoginPageScreen = () => {
  const navigation = useNavigation();

  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Seby@123#');
  const [emailError, setEmailError] = useState('');
  const [loading, setLoading] = useState(false);
  const {AddAnalyticsEvent, AddAnalyticsScreenView, AddAnalyticCustomer} =
    AnalyticService;

  // Custom Alert states
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [alertTitle, setAlertTitle] = useState<string>('');
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertButtons, setAlertButtons] = useState<any[]>([]);

  const showCustomAlert = (title: string, message: string, buttons?: any[]) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertButtons(buttons || []);
    setAlertVisible(true);
  };

  const hideAlert = () => {
    setAlertVisible(false);
  };

  const {signIn, isRegisteredAsMechanicUser, signOut, setUser, fetchUserInfo} =
    useAuth();

  useEffect(() => {
    AddAnalyticsScreenView('WelcomeLogin Page', '/#/welcome-login');
  }, []);

  const validateEmail = (value: string) => {
    // Simple email regex
    const re = /\S+@\S+\.\S+/;
    return re.test(value);
  };

  const handleEmailBlur = () => {
    if (email && !validateEmail(email)) {
      setEmailError(AppStrings.MCX_ENTER_EMAIL);
    } else {
      setEmailError('');
    }
  };

  const handleSignupClick = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_LoginMainScreen as never);
  };

  const handleResetPasswordClick = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_ResetPasswordScreen as never);
  };
  const handleLogin = async () => {
    if (!email || !password) {
      showCustomAlert('Error', AppStrings.ERROR_MESSAGE_LOGIN_INPUT);
      return;
    }
    if (!validateEmail(email)) {
      setEmailError(AppStrings.MCX_ENTER_EMAIL);
      return;
    }

    setLoading(true);
    try {
      await AsyncStorage.setItem(GC_SOCIAL_LOGGED_IN, 'false');
      const resLogin = await signIn(email, password);
      const user_Id = resLogin.uid;

      // Check if user is registered as mechanic (loader stays visible during check)
      const isMechanic = await isRegisteredAsMechanicUser(user_Id);
      if (isMechanic) {
        AddAnalyticsEvent({
          action: 'login_failed',
          category: 'authentication',
          label: 'login attempt with mechanic credential - ' + user_Id,
          value: 0,
        });
        // Sign out and clear all user data (matches Ionic - logoutUser does this)
        await signOut();
        await AsyncStorage.removeItem(GC_CUSTOMER_ID);
        await AsyncStorage.setItem(GC_SOCIAL_LOGGED_IN, 'false');
        // Ensure user is not set in AuthContext
        setUser(null);
        setLoading(false);
        // Show alert after loader is dismissed (matches Ionic)
        // User stays on login screen - no navigation
        showCustomAlert(
          'Unable to Login',
          AppStrings.MCX_ALERADY_REGISTERED_EMAIL,
        );
        return;
      }

      await AsyncStorage.setItem(GC_CUSTOMER_ID, user_Id);

      // Check registration status (loader stays visible during check)
      const userInfo = await fetchUserInfo(user_Id);
      if (userInfo && userInfo['registration-status'] === 'COMPLETED') {
        // Registration completed - rely on AuthContext to switch stacks
        setUser(resLogin);
        setLoading(false);
      } else {
        // Registration not completed - navigate first, then dismiss loader
        (navigation as any).reset({
          index: 0,
          routes: [{name: RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}],
        });
        setLoading(false);
      }
    } catch (error: any) {
      console.log('Login error:', error);
      setLoading(false);
      // Handle network errors first
      if (
        error.code === 'auth/network-request-failed' ||
        error.message?.includes('network') ||
        error.message?.includes('timeout') ||
        error.message?.includes('connection') ||
        error.message?.includes('unreachable')
      ) {
        // Navigate to network failed page
        (navigation as any).navigate(
          RouteNames.MCX_NAV_NETWORK_FAILED as never,
        );
        return;
      }

      // Handle different error cases based on Ionic code
      let errorMessage = 'An error occurred during login';

      if (
        error.code === 'auth/user-not-found' ||
        error.code === 'auth/wrong-password'
      ) {
        errorMessage = 'The email or password that you typed is incorrect.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage =
          'Too many failed login attempts. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      showCustomAlert('Unable to Login', errorMessage);
    }
  };
  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0} // Adjust if needed
    >
      <ScrollView
        contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        <View style={modernStyles.container}>
          <Image
            source={require('../../assets/mycanx_logo.png')}
            style={modernStyles.logo}
            resizeMode="contain"
          />

          {emailError ? (
            <Text style={modernStyles.errorText}>{emailError}</Text>
          ) : null}

          <TextInput
            style={[modernStyles.input, emailError && modernStyles.inputError]}
            placeholder={AppStrings.MCX_ENTER_EMAIL}
            placeholderTextColor="#9CA3AF"
            value={email}
            onChangeText={setEmail}
            onBlur={handleEmailBlur}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <TextInput
            style={modernStyles.input}
            placeholder={AppStrings.MCX_ENTER_PASSWORD}
            placeholderTextColor="#9CA3AF"
            secureTextEntry
            value={password}
            onChangeText={setPassword}
          />

          <TouchableOpacity style={modernStyles.loginBtn} onPress={handleLogin}>
            <Text style={modernStyles.loginText}>
              {AppStrings.MCX_LOGIN.toUpperCase()}
            </Text>
          </TouchableOpacity>

          <View style={modernStyles.bottomRow}>
            <TouchableOpacity onPress={handleResetPasswordClick}>
              <Text style={modernStyles.forgotText}>
                {AppStrings.MCX_FORGOT_PASSWORD}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modernStyles.signUpBtn}
              onPress={handleSignupClick}>
              <Text style={modernStyles.signUpText}>
                {AppStrings.MCX_SIGN_UP.toUpperCase()}
              </Text>
            </TouchableOpacity>
          </View>

          <LoaderOverlay visible={loading} />

          <CustomAlert
            visible={alertVisible}
            title={alertTitle}
            message={alertMessage}
            onDismiss={hideAlert}
            buttons={alertButtons}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AppLoginPageScreen;

// Modern themed styles - easy to swap back to original
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 24,
  },
  logo: {
    height: 200,
    width: 200,
    marginBottom: 50,
  },
  errorText: {
    color: '#DC2626',
    alignSelf: 'flex-start',
    marginBottom: 8,
    fontSize: 13,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
  },
  input: {
    width: '100%',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 18,
    color: '#1F2937',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 16,
    fontSize: 15,
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
  },
  inputError: {
    borderColor: '#DC2626',
    borderWidth: 1.5,
  },
  loginBtn: {
    width: '100%',
    backgroundColor: '#A10000',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 24,
    shadowColor: '#A10000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loginText: {
    color: '#FFFFFF',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  bottomRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  forgotText: {
    color: '#6B7280',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 14,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  signUpBtn: {
    borderColor: '#A10000',
    borderWidth: 2,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: wp(18),
    backgroundColor: '#FFFFFF',
  },
  signUpText: {
    color: '#A10000',
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 14,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
});

// Original styles preserved for easy rollback
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
    padding: 14,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  input: {
    width: '100%',
    backgroundColor: Colors.LOGIN_INPUT_TEXT_BG_COLOR,
    borderRadius: 2,
    paddingVertical: 10,
    paddingHorizontal: 16,
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 14,
    fontSize: 14,
    alignItems: 'center',
  },
  loginBtn: {
    width: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 18,
  },
  loginText: {
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
  },
  bottomRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
  },
  forgotText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 13,
  },
  signUpBtn: {
    borderColor: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    borderWidth: 1,
    borderRadius: 2,
    paddingVertical: 8,
    paddingHorizontal: wp(15),
    marginLeft: wp(20),
  },
  signUpText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 14,
    fontWeight: 'bold',
  },
});
